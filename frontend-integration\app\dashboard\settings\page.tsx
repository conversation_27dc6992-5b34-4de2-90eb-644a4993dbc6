'use client';

import { Settings } from 'lucide-react';
import { SettingsShell } from './components/SettingsShell';

/**
 * Settings Page - Main Settings Hub
 * 
 * Features:
 * - 8 settings tabs with URL hash sync
 * - Unsaved changes protection
 * - Admin-only access
 * - Consistent branding and UX
 */
export default function SettingsPage() {
  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
        <div>
          <h1 className="text-3xl md:text-4xl font-bold tracking-tight bg-gradient-to-r from-sky-600 to-violet-600 bg-clip-text text-transparent flex items-center">
            <Settings className="h-8 w-8 mr-3 text-sky-600" />
            Settings
          </h1>
          <p className="text-slate-600 mt-1">
            Configure your school management system settings and preferences.
          </p>
        </div>
      </div>

      {/* Settings Shell */}
      <SettingsShell />
    </div>
  );
}
