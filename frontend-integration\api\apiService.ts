/**
 * API Service - Central API client for School Management System
 *
 * A clean, focused API client for real backend integration with:
 * - Environment-based configuration
 * - Zustand auth store integration
 * - Request/Response interceptors
 * - Server-side rendering compatibility
 */

import axios from 'axios';
import { authUtils, useAuthStore } from '../lib/authStore';

// Create axios instance with environment-based configuration
export const apiClient = axios.create({
  baseURL: process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000/api/v1',
  withCredentials: true,
});

// Request interceptor - Add auth token with expiration checking
apiClient.interceptors.request.use(
  config => {
    // Get token from auth utils (includes expiration check)
    const token = authUtils.getToken();

    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }

    // Add request timestamp for debugging
    if (process.env.NODE_ENV === 'development') {
      config.metadata = { startTime: Date.now() };
    }

    return config;
  },
  error => {
    console.error('API Request Error:', error);
    return Promise.reject(error);
  }
);

// Response interceptor - Handle common errors and auth state
apiClient.interceptors.response.use(
  response => {
    // Log response time in development
    if (process.env.NODE_ENV === 'development' && response.config.metadata) {
      const endTime = Date.now();
      const duration = endTime - response.config.metadata.startTime;
      console.log(
        `API ${response.config.method?.toUpperCase()} ${response.config.url}: ${duration}ms`
      );
    }

    return response;
  },
  async error => {
    // Handle different error status codes
    if (error.response) {
      const { status, data } = error.response;

      switch (status) {
        case 401:
          // Unauthorized - Token expired or invalid
          console.warn('Authentication failed - redirecting to login');
          if (typeof window !== 'undefined') {
            useAuthStore.getState().logout();
            const currentPath = window.location.pathname;
            const returnUrl = encodeURIComponent(currentPath);
            window.location.href = `/login?returnUrl=${returnUrl}`;
          }
          break;

        case 403:
          // Forbidden - User doesn't have permission
          console.warn('Access forbidden:', data?.message || 'Insufficient permissions');
          break;

        case 429:
          // Too Many Requests
          console.warn('Rate limit exceeded:', data?.message || 'Too many requests');
          break;

        case 500:
          // Internal Server Error
          console.error('Server error:', data?.message || 'Internal server error');
          break;

        default:
          console.error(`API Error ${status}:`, data?.message || error.message);
      }
    } else if (error.request) {
      // Network error
      console.error('Network error:', error.message);
    } else {
      // Other error
      console.error('Request error:', error.message);
    }

    return Promise.reject(error);
  }
);

// Export the configured client
export default apiClient;
