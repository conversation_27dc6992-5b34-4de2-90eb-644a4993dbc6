'use client';

/**
 * Teacher Edit Page - Production-Grade Implementation
 *
 * Features:
 * - Form validation with Zod + React Hook Form
 * - Real API integration with React Query
 * - Role-based access control (ADMIN only)
 * - Professional UI with loading and error states
 * - Redirect to detail page on success
 * - Optimistic updates
 * - Hard refresh support
 */

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useParams } from 'next/navigation';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import {
  ArrowLeft,
  Save,
  User,
  Mail,
  Phone,
  Building,
  BookOpen,
  Calendar,
  AlertTriangle,
} from 'lucide-react';

// UI Components
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Skeleton } from '@/components/ui/skeleton';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { useToast } from '@/hooks/use-toast';

// Hooks and Services
import { useTeacher, useUpdateTeacher } from '@/hooks/useTeachers';
import { usePermissions } from '@/components/auth/ProtectedRoute';
import { TeacherUpdateSchema, type TeacherUpdate } from '@/schemas/zodSchemas';

export default function TeacherEditPage() {
  const params = useParams();
  const router = useRouter();
  const { toast } = useToast();
  const { isAdmin } = usePermissions();

  const teacherId = params.id as string;

  // Check permissions
  if (!isAdmin()) {
    router.push('/dashboard/teachers');
    return null;
  }

  // Fetch teacher data
  const {
    data: teacher,
    isLoading,
    error,
  } = useTeacher(teacherId);

  // Update mutation
  const updateTeacherMutation = useUpdateTeacher();

  // Form setup
  const form = useForm<TeacherUpdate>({
    resolver: zodResolver(TeacherUpdateSchema),
    defaultValues: {
      name: '',
      subject: '',
      email: '',
      department: '',
      phone: '',
      status: 'ACTIVE',
      hire_date: '',
    },
  });

  // Update form when teacher data loads
  useEffect(() => {
    if (teacher) {
      form.reset({
        name: teacher.name || '',
        subject: teacher.subject || '',
        email: teacher.email || '',
        department: teacher.department || '',
        phone: teacher.phone || '',
        status: teacher.status || 'ACTIVE',
        hire_date: teacher.hire_date || '',
      });
    }
  }, [teacher, form]);

  const onSubmit = async (data: TeacherUpdate) => {
    try {
      await updateTeacherMutation.mutateAsync({
        id: teacherId,
        data,
      });

      toast({
        title: 'Success',
        description: 'Teacher updated successfully',
      });

      // Redirect to detail page
      router.push(`/dashboard/teachers/${teacherId}`);
    } catch (error) {
      console.error('Update teacher error:', error);
      toast({
        title: 'Error',
        description: 'Failed to update teacher',
        variant: 'destructive',
      });
    }
  };

  const handleBack = () => {
    router.push(`/dashboard/teachers/${teacherId}`);
  };

  // Loading state
  if (isLoading) {
    return (
      <div className="container mx-auto p-4 sm:p-6 lg:p-8 space-y-6">
        <div className="flex items-center gap-4">
          <Skeleton className="h-10 w-10" />
          <Skeleton className="h-8 w-48" />
        </div>
        <Card>
          <CardHeader>
            <Skeleton className="h-6 w-32" />
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {Array.from({ length: 6 }).map((_, i) => (
                <div key={i} className="space-y-2">
                  <Skeleton className="h-4 w-24" />
                  <Skeleton className="h-10 w-full" />
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Error state
  if (error || !teacher) {
    return (
      <div className="container mx-auto p-4 sm:p-6 lg:p-8">
        <div className="flex items-center gap-4 mb-6">
          <Button variant="ghost" size="sm" onClick={handleBack}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Teacher
          </Button>
        </div>
        <Card>
          <CardContent className="flex flex-col items-center justify-center py-12">
            <AlertTriangle className="h-12 w-12 text-muted-foreground mb-4" />
            <h3 className="text-lg font-semibold mb-2">Teacher Not Found</h3>
            <p className="text-muted-foreground text-center mb-4">
              The teacher you're trying to edit doesn't exist or has been removed.
            </p>
            <Button onClick={() => router.push('/dashboard/teachers')}>
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Teachers
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-4 sm:p-6 lg:p-8 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button variant="ghost" size="sm" onClick={handleBack}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Teacher
          </Button>
          <div>
            <h1 className="text-2xl sm:text-3xl font-bold tracking-tight">
              Edit Teacher
            </h1>
            <p className="text-muted-foreground">
              Update teacher information
            </p>
          </div>
        </div>
      </div>

      {/* Form */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <User className="h-5 w-5" />
            Teacher Information
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Name */}
                <FormField
                  control={form.control}
                  name="name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Full Name</FormLabel>
                      <FormControl>
                        <Input
                          placeholder="Enter teacher's full name"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Subject */}
                <FormField
                  control={form.control}
                  name="subject"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Subject</FormLabel>
                      <FormControl>
                        <Input
                          placeholder="Enter subject taught"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Email */}
                <FormField
                  control={form.control}
                  name="email"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Email</FormLabel>
                      <FormControl>
                        <Input
                          type="email"
                          placeholder="Enter email address"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Phone */}
                <FormField
                  control={form.control}
                  name="phone"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Phone</FormLabel>
                      <FormControl>
                        <Input
                          placeholder="Enter phone number"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Department */}
                <FormField
                  control={form.control}
                  name="department"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Department</FormLabel>
                      <FormControl>
                        <Input
                          placeholder="Enter department"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Status */}
                <FormField
                  control={form.control}
                  name="status"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Status</FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        defaultValue={field.value}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select status" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="ACTIVE">Active</SelectItem>
                          <SelectItem value="INACTIVE">Inactive</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Hire Date */}
                <FormField
                  control={form.control}
                  name="hire_date"
                  render={({ field }) => (
                    <FormItem className="md:col-span-2">
                      <FormLabel>Hire Date</FormLabel>
                      <FormControl>
                        <Input
                          type="date"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              {/* Submit Button */}
              <div className="flex items-center justify-end gap-4 pt-6">
                <Button
                  type="button"
                  variant="outline"
                  onClick={handleBack}
                >
                  Cancel
                </Button>
                <Button
                  type="submit"
                  disabled={updateTeacherMutation.isPending}
                >
                  <Save className="h-4 w-4 mr-2" />
                  {updateTeacherMutation.isPending ? 'Saving...' : 'Save Changes'}
                </Button>
              </div>
            </form>
          </Form>
        </CardContent>
      </Card>
    </div>
  );
}
