#!/usr/bin/env node

/**
 * Simple Proxy Test - Check if Next.js rewrites are working
 */

import axios from 'axios';

async function testProxy() {
  console.log('🧪 Testing Next.js Proxy Configuration');
  console.log('=====================================');
  
  // Test if Next.js server is running
  try {
    console.log('\n1. Testing Next.js server...');
    const homeResponse = await axios.get('http://localhost:3000', { timeout: 5000 });
    console.log('✅ Next.js server is running');
  } catch (error) {
    console.log('❌ Next.js server not accessible');
    console.log('   Make sure: npm run dev is running on port 3000');
    return;
  }
  
  // Test proxy rewrite
  try {
    console.log('\n2. Testing proxy rewrite...');
    console.log('   Request: http://localhost:3000/api/v1/auth/me');
    console.log('   Should proxy to: http://127.0.0.1:8000/api/v1/auth/me');
    
    const proxyResponse = await axios.get('http://localhost:3000/api/v1/auth/me', { 
      timeout: 10000,
      validateStatus: () => true // Accept any status code
    });
    
    console.log(`   Status: ${proxyResponse.status}`);
    console.log(`   Response: ${JSON.stringify(proxyResponse.data).substring(0, 100)}...`);
    
    if (proxyResponse.status === 401) {
      console.log('✅ Proxy working! (401 = endpoint exists, needs auth)');
    } else if (proxyResponse.status === 404) {
      console.log('❌ Proxy not working (404 = endpoint not found)');
    } else {
      console.log(`⚠️  Unexpected status: ${proxyResponse.status}`);
    }
    
  } catch (error) {
    console.log('❌ Proxy test failed');
    console.log(`   Error: ${error.message}`);
    
    if (error.code === 'ECONNREFUSED') {
      console.log('   Issue: Connection refused - Next.js server not running');
    } else if (error.code === 'ENOTFOUND') {
      console.log('   Issue: DNS resolution failed');
    } else {
      console.log('   Issue: Network or configuration error');
    }
  }
}

testProxy().catch(console.error);
