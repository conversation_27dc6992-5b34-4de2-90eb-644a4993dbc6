'use client';

import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { AlertTriangle, ArrowLeft, RefreshCw } from 'lucide-react';
import Link from 'next/link';
import { useEffect } from 'react';

interface ModuleErrorProps {
  error: Error & { digest?: string };
  reset: () => void;
  moduleName: string;
  moduleIcon?: string;
  backHref?: string;
}

export function ModuleError({ 
  error, 
  reset, 
  moduleName, 
  moduleIcon = '📋',
  backHref = '/dashboard'
}: ModuleErrorProps) {
  useEffect(() => {
    console.error(`${moduleName} page error:`, error);
  }, [error, moduleName]);

  return (
    <div className='container mx-auto p-4 sm:p-6 lg:p-8'>
      <div className='flex items-center justify-between mb-6'>
        <Link href={backHref}>
          <Button variant="ghost" size="sm">
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Dashboard
          </Button>
        </Link>
      </div>

      <Card className="border-red-200 bg-red-50 max-w-2xl mx-auto">
        <CardHeader className="text-center">
          <div className="mx-auto w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mb-4">
            <AlertTriangle className="w-8 h-8 text-red-600" />
          </div>
          <CardTitle className="text-red-800 flex items-center justify-center">
            <span className="mr-2">{moduleIcon}</span>
            {moduleName} Page Error
          </CardTitle>
          <CardDescription className="text-red-600">
            We encountered an error while loading the {moduleName.toLowerCase()} page. This might be a temporary issue
            with the data connection.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Error details (development only) */}
          {process.env.NODE_ENV === 'development' && (
            <div className='bg-red-100 border border-red-300 rounded-lg p-4 text-left'>
              <h3 className='font-semibold text-red-800 mb-2'>Error Details:</h3>
              <p className='text-sm text-red-700 font-mono break-all'>{error.message}</p>
              {error.digest && <p className='text-xs text-red-600 mt-2'>Error ID: {error.digest}</p>}
            </div>
          )}

          {/* Action buttons */}
          <div className='flex flex-col sm:flex-row gap-3 justify-center'>
            <Button onClick={reset} variant="outline" className="border-red-300">
              <RefreshCw className="w-4 h-4 mr-2" />
              Try Again
            </Button>

            <Link href={backHref}>
              <Button variant="default">
                <ArrowLeft className="w-4 h-4 mr-2" />
                Back to Dashboard
              </Button>
            </Link>
          </div>

          {/* Help text */}
          <p className='text-sm text-red-600 text-center'>
            If the problem persists, please check your internet connection or contact support.
          </p>
        </CardContent>
      </Card>
    </div>
  );
}
