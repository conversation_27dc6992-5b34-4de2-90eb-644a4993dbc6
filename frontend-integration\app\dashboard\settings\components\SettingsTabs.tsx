'use client';

import {
  Bell,
  Building2,
  Calendar,
  CreditCard,
  GraduationCap,
  IdCard,
  Settings,
  Users,
} from 'lucide-react';
import { useEffect, useState } from 'react';

import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';

import { AcademicStructureForm } from './forms/AcademicStructureForm';
import { UserManagementForm } from './forms/UserManagementForm';
import { ExamTermsForm } from './forms/ExamTermsForm';
import { FeesForm } from './forms/FeesForm';
import { IDCardNoteForm } from './forms/IDCardNoteForm';
import { NotificationsForm } from './forms/NotificationsForm';
import { SchoolProfileForm } from './forms/SchoolProfileForm';
import { SystemSettingsForm } from './forms/SystemSettingsForm';

// Tab configuration
const SETTINGS_TABS = [
  {
    id: 'school-profile',
    label: 'School Profile',
    icon: Building2,
    description: 'Basic school information and branding',
    component: SchoolProfileForm,
  },
  {
    id: 'users-roles',
    label: 'Users & Roles',
    icon: Users,
    description: 'User permissions and role management',
    component: UserManagementForm,
  },
  {
    id: 'fees-billing',
    label: 'Fees & Billing',
    icon: CreditCard,
    description: 'Fee structure and payment settings',
    component: FeesForm,
  },
  {
    id: 'academic-structure',
    label: 'Academic Structure',
    icon: GraduationCap,
    description: 'Grade levels and academic calendar',
    component: AcademicStructureForm,
  },
  {
    id: 'exam-terms',
    label: 'Exam Terms',
    icon: Calendar,
    description: 'Examination periods and grading system',
    component: ExamTermsForm,
  },
  {
    id: 'notifications',
    label: 'Notifications',
    icon: Bell,
    description: 'Email, SMS and push notification settings',
    component: NotificationsForm,
  },
  {
    id: 'id-card-note',
    label: 'ID Card Note',
    icon: IdCard,
    description: 'ID card templates and notes configuration',
    component: IDCardNoteForm,
  },
  {
    id: 'system',
    label: 'System',
    icon: Settings,
    description: 'System-wide settings and preferences',
    component: SystemSettingsForm,
  },
] as const;

interface SettingsTabsProps {
  onDataChange: () => void;
  onNavigation: (url: string) => void;
}

/**
 * SettingsTabs Component
 *
 * Features:
 * - 8 settings tabs with URL hash sync
 * - Responsive tab layout
 * - Icon and description for each tab
 * - Keyboard navigation support
 * - Consistent styling with brand colors
 */
export function SettingsTabs({
  onDataChange: _onDataChange,
  onNavigation: _onNavigation,
}: SettingsTabsProps) {
  // Get initial tab from URL hash or default to first tab
  const getInitialTab = (): (typeof SETTINGS_TABS)[number]['id'] => {
    if (typeof window !== 'undefined') {
      const hash = window.location.hash.replace('#', '');
      return SETTINGS_TABS.find(tab => tab.id === hash)?.id || SETTINGS_TABS[0].id;
    }
    return SETTINGS_TABS[0].id;
  };

  const [activeTab, setActiveTab] = useState(getInitialTab);

  // Sync tab with URL hash
  useEffect(() => {
    const handleHashChange = () => {
      const hash = window.location.hash.replace('#', '');
      const validTab = SETTINGS_TABS.find(tab => tab.id === hash);
      if (validTab) {
        setActiveTab(validTab.id);
      }
    };

    window.addEventListener('hashchange', handleHashChange);
    return () => window.removeEventListener('hashchange', handleHashChange);
  }, []);

  // Handle tab change
  const handleTabChange = (tabId: string) => {
    const validTab = SETTINGS_TABS.find(tab => tab.id === tabId);
    if (validTab) {
      setActiveTab(validTab.id);

      // Update URL hash without triggering navigation
      if (typeof window !== 'undefined') {
        window.history.replaceState(null, '', `#${tabId}`);
      }
    }
  };

  return (
    <Tabs value={activeTab} onValueChange={handleTabChange} className='w-full'>
      {/* Tabs List - Scrollable on mobile */}
      <div className='border-b border-slate-200 bg-white rounded-t-2xl'>
        <TabsList className='w-full h-auto p-2 bg-transparent justify-start overflow-x-auto'>
          <div className='flex gap-1 min-w-max'>
            {SETTINGS_TABS.map(tab => {
              const Icon = tab.icon;
              const isActive = activeTab === tab.id;

              return (
                <TabsTrigger
                  key={tab.id}
                  value={tab.id}
                  className={`
                    flex items-center gap-2 px-4 py-3 rounded-xl text-sm font-medium
                    transition-all duration-200 whitespace-nowrap
                    ${
                      isActive
                        ? 'bg-gradient-to-r from-sky-50 to-violet-50 text-sky-800 shadow-sm border border-sky-100'
                        : 'text-slate-600 hover:text-slate-900 hover:bg-slate-50'
                    }
                  `}
                >
                  <Icon className={`h-4 w-4 ${isActive ? 'text-sky-600' : 'text-slate-500'}`} />
                  <span className='hidden sm:inline'>{tab.label}</span>
                  <span className='sm:hidden'>{tab.label.split(' ')[0]}</span>
                </TabsTrigger>
              );
            })}
          </div>
        </TabsList>
      </div>

      {/* Tab Content */}
      <div className='bg-white rounded-b-2xl'>
        {SETTINGS_TABS.map(tab => {
          const Icon = tab.icon;
          const Component = tab.component;

          return (
            <TabsContent key={tab.id} value={tab.id} className='mt-0 focus-visible:outline-none'>
              {/* Tab Header */}
              <div className='border-b border-slate-100 px-6 py-4'>
                <div className='flex items-center gap-3'>
                  <div className='p-2 bg-gradient-to-r from-sky-50 to-violet-50 rounded-lg'>
                    <Icon className='h-5 w-5 text-sky-600' />
                  </div>
                  <div>
                    <h2 className='text-xl font-semibold text-slate-900'>{tab.label}</h2>
                    <p className='text-sm text-slate-600 mt-1'>{tab.description}</p>
                  </div>
                  <Badge variant='outline' className='ml-auto'>
                    Admin Only
                  </Badge>
                </div>
              </div>

              {/* Tab Content */}
              <div className='min-h-[400px]'>
                <Component />
              </div>
            </TabsContent>
          );
        })}
      </div>
    </Tabs>
  );
}
