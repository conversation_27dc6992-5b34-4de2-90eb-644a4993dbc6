import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { ArrowLeft, BookOpen, Calendar, Clock, Edit, MapPin, User, Users } from 'lucide-react';
import Link from 'next/link';
import { notFound } from 'next/navigation';

// Mock exam data
const mockExams = [
  {
    id: 1,
    name: 'Mathematics Midterm',
    subject: 'Mathematics',
    grade: 'Grade 10',
    date: '2024-03-15',
    time: '09:00 AM',
    duration: '2 hours',
    totalMarks: 100,
    studentsCount: 45,
    teacher: 'Dr. <PERSON>',
    status: 'Scheduled',
    room: 'Room 101',
    instructions: 'Bring calculator and ruler. No mobile phones allowed.',
    createdAt: '2024-03-01T10:00:00Z',
    updatedAt: '2024-03-01T10:00:00Z',
  },
  {
    id: 2,
    name: 'Physics Final Exam',
    subject: 'Physics',
    grade: 'Grade 11',
    date: '2024-03-20',
    time: '10:00 AM',
    duration: '3 hours',
    totalMarks: 100,
    studentsCount: 38,
    teacher: 'Prof. <PERSON>',
    status: 'Scheduled',
    room: 'Room 205',
    instructions: 'Formula sheet will be provided. Bring scientific calculator.',
    createdAt: '2024-02-28T14:30:00Z',
    updatedAt: '2024-02-28T14:30:00Z',
  },
];

interface ExamDetailPageProps {
  params: {
    id: string;
  };
}

export default function ExamDetailPage({ params }: ExamDetailPageProps) {
  const exam = mockExams.find(e => e.id === parseInt(params.id));

  if (!exam) {
    notFound();
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Scheduled': return 'bg-blue-100 text-blue-800';
      case 'Ongoing': return 'bg-green-100 text-green-800';
      case 'Completed': return 'bg-gray-100 text-gray-800';
      case 'Cancelled': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className='container mx-auto p-4 sm:p-6 lg:p-8 max-w-4xl'>
      {/* Header */}
      <div className='flex items-center justify-between mb-8'>
        <div className='flex items-center gap-4'>
          <Link href='/dashboard/exams'>
            <Button variant='outline' size='sm'>
              <ArrowLeft className='w-4 h-4 mr-2' />
              Back to Exams
            </Button>
          </Link>
          <div>
            <h1 className='text-3xl font-bold text-gray-900 flex items-center gap-2'>
              <BookOpen className='w-8 h-8 text-blue-600' />
              Exam Details
            </h1>
            <p className='text-gray-600 mt-1'>View exam information and schedule</p>
          </div>
        </div>
        <Link href={`/dashboard/exams/${exam.id}/edit`}>
          <Button>
            <Edit className='w-4 h-4 mr-2' />
            Edit Exam
          </Button>
        </Link>
      </div>

      {/* Exam Details */}
      <div className='grid grid-cols-1 lg:grid-cols-3 gap-8'>
        <div className='lg:col-span-2'>
          <Card>
            <CardHeader>
              <div className='flex items-start justify-between'>
                <div className='space-y-2'>
                  <CardTitle className='text-2xl'>{exam.name}</CardTitle>
                  <div className='flex gap-2'>
                    <Badge variant='outline'>{exam.subject}</Badge>
                    <Badge variant='outline'>{exam.grade}</Badge>
                    <Badge className={getStatusColor(exam.status)}>
                      {exam.status}
                    </Badge>
                  </div>
                </div>
              </div>
            </CardHeader>
            <CardContent className='space-y-6'>
              <div className='grid grid-cols-1 sm:grid-cols-2 gap-6'>
                <div className='flex items-center gap-2'>
                  <Calendar className='w-5 h-5 text-blue-600' />
                  <div>
                    <p className='font-medium'>Date</p>
                    <p className='text-sm text-muted-foreground'>{exam.date}</p>
                  </div>
                </div>
                <div className='flex items-center gap-2'>
                  <Clock className='w-5 h-5 text-green-600' />
                  <div>
                    <p className='font-medium'>Time</p>
                    <p className='text-sm text-muted-foreground'>{exam.time}</p>
                  </div>
                </div>
                <div className='flex items-center gap-2'>
                  <MapPin className='w-5 h-5 text-red-600' />
                  <div>
                    <p className='font-medium'>Room</p>
                    <p className='text-sm text-muted-foreground'>{exam.room}</p>
                  </div>
                </div>
                <div className='flex items-center gap-2'>
                  <Users className='w-5 h-5 text-purple-600' />
                  <div>
                    <p className='font-medium'>Students</p>
                    <p className='text-sm text-muted-foreground'>{exam.studentsCount} enrolled</p>
                  </div>
                </div>
              </div>

              <Separator />

              <div>
                <h3 className='font-semibold mb-2 flex items-center gap-2'>
                  <User className='w-4 h-4' />
                  Teacher
                </h3>
                <p className='text-gray-700'>{exam.teacher}</p>
              </div>

              <div>
                <h3 className='font-semibold mb-2'>Instructions</h3>
                <p className='text-gray-700 leading-relaxed'>
                  {exam.instructions || 'No special instructions provided.'}
                </p>
              </div>

              <Separator />

              <div className='grid grid-cols-1 sm:grid-cols-2 gap-4'>
                <div>
                  <p className='font-medium'>Duration</p>
                  <p className='text-sm text-muted-foreground'>{exam.duration}</p>
                </div>
                <div>
                  <p className='font-medium'>Total Marks</p>
                  <p className='text-sm text-muted-foreground'>{exam.totalMarks}</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        <div className='space-y-6'>
          {/* Quick Stats */}
          <Card>
            <CardHeader>
              <CardTitle>Quick Stats</CardTitle>
            </CardHeader>
            <CardContent className='space-y-4'>
              <div className='text-center'>
                <div className='text-3xl font-bold text-blue-600'>
                  {exam.studentsCount}
                </div>
                <p className='text-sm text-muted-foreground'>Students Enrolled</p>
              </div>
              <div className='text-center'>
                <div className='text-3xl font-bold text-green-600'>
                  {exam.totalMarks}
                </div>
                <p className='text-sm text-muted-foreground'>Total Marks</p>
              </div>
              <div className='text-center'>
                <div className='text-2xl font-bold text-purple-600'>
                  {exam.duration}
                </div>
                <p className='text-sm text-muted-foreground'>Duration</p>
              </div>
            </CardContent>
          </Card>

          {/* Metadata */}
          <Card>
            <CardHeader>
              <CardTitle>Metadata</CardTitle>
            </CardHeader>
            <CardContent className='space-y-3'>
              <div>
                <p className='text-sm font-medium text-muted-foreground'>Created</p>
                <p className='text-sm'>{new Date(exam.createdAt).toLocaleString()}</p>
              </div>
              <div>
                <p className='text-sm font-medium text-muted-foreground'>Last Updated</p>
                <p className='text-sm'>{new Date(exam.updatedAt).toLocaleString()}</p>
              </div>
              <div>
                <p className='text-sm font-medium text-muted-foreground'>Exam ID</p>
                <p className='text-sm font-mono'>{exam.id}</p>
              </div>
            </CardContent>
          </Card>

          {/* Actions */}
          <Card>
            <CardHeader>
              <CardTitle>Actions</CardTitle>
            </CardHeader>
            <CardContent className='space-y-3'>
              <Button className='w-full' variant='outline'>
                View Student List
              </Button>
              <Button className='w-full' variant='outline'>
                Generate Report
              </Button>
              <Button className='w-full' variant='outline'>
                Send Notification
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
