# Set default behavior to automatically normalize line endings
* text=auto eol=lf

# Explicitly declare text files you want to always be normalized and converted
# to native line endings on checkout
*.js text eol=lf
*.jsx text eol=lf
*.ts text eol=lf
*.tsx text eol=lf
*.json text eol=lf
*.md text eol=lf
*.yml text eol=lf
*.yaml text eol=lf
*.css text eol=lf
*.scss text eol=lf
*.html text eol=lf
*.xml text eol=lf
*.svg text eol=lf
*.txt text eol=lf
*.sh text eol=lf

# Declare files that will always have CRLF line endings on checkout
*.bat text eol=crlf
*.cmd text eol=crlf
*.ps1 text eol=crlf

# Denote all files that are truly binary and should not be modified
*.png binary
*.jpg binary
*.jpeg binary
*.gif binary
*.ico binary
*.mov binary
*.mp4 binary
*.mp3 binary
*.flv binary
*.fla binary
*.swf binary
*.gz binary
*.zip binary
*.7z binary
*.ttf binary
*.eot binary
*.woff binary
*.woff2 binary
*.pyc binary
*.pdf binary
*.ez binary
*.bz2 binary
*.swp binary
*.webp binary
*.avif binary

# Archive files
*.tar binary
*.tgz binary
*.rar binary

# Font files
*.otf binary

# Document files
*.doc binary
*.docx binary
*.xls binary
*.xlsx binary
*.ppt binary
*.pptx binary

# Exclude files from exporting
.gitattributes export-ignore
.gitignore export-ignore
.github export-ignore
.vscode export-ignore
*.log export-ignore
*.tmp export-ignore
node_modules export-ignore
.next export-ignore
out export-ignore
build export-ignore
dist export-ignore
coverage export-ignore
.nyc_output export-ignore

# Language-specific settings
*.js linguist-language=JavaScript
*.jsx linguist-language=JavaScript
*.ts linguist-language=TypeScript
*.tsx linguist-language=TypeScript

# Mark vendor files
public/vendor/* linguist-vendored
node_modules/* linguist-vendored
*.min.js linguist-vendored
*.min.css linguist-vendored

# Documentation
*.md linguist-documentation
docs/* linguist-documentation
README* linguist-documentation
CHANGELOG* linguist-documentation
LICENSE* linguist-documentation
CONTRIBUTING* linguist-documentation

# Generated files
*.generated.* linguist-generated
*.auto.* linguist-generated
*-lock.json linguist-generated
yarn.lock linguist-generated
package-lock.json linguist-generated
pnpm-lock.yaml linguist-generated
