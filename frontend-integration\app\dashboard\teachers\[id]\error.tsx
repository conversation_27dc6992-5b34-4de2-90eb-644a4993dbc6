'use client';

/**
 * Teacher Detail Error Page
 *
 * Professional error boundary for teacher detail page
 * Provides recovery options and clear error messaging
 */

import { useEffect } from 'react';
import { AlertTriangle, ArrowLeft, RefreshCw } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';

interface TeacherDetailErrorProps {
  error: Error & { digest?: string };
  reset: () => void;
}

export default function TeacherDetailError({
  error,
  reset,
}: TeacherDetailErrorProps) {
  useEffect(() => {
    // Log the error to an error reporting service
    console.error('Teacher detail page error:', error);
  }, [error]);

  const handleGoBack = () => {
    window.history.back();
  };

  const handleGoToTeachers = () => {
    window.location.href = '/dashboard/teachers';
  };

  return (
    <div className="container mx-auto p-4 sm:p-6 lg:p-8">
      <div className="flex items-center gap-4 mb-6">
        <Button variant="ghost" size="sm" onClick={handleGoBack}>
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back
        </Button>
      </div>

      <Card>
        <CardContent className="flex flex-col items-center justify-center py-12">
          <AlertTriangle className="h-16 w-16 text-destructive mb-6" />
          
          <h2 className="text-2xl font-bold mb-2">Something went wrong</h2>
          
          <p className="text-muted-foreground text-center mb-6 max-w-md">
            We encountered an error while loading the teacher details. 
            This could be due to a network issue or the teacher may no longer exist.
          </p>

          <div className="flex items-center gap-4">
            <Button onClick={reset} variant="outline">
              <RefreshCw className="h-4 w-4 mr-2" />
              Try Again
            </Button>
            
            <Button onClick={handleGoToTeachers}>
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Teachers
            </Button>
          </div>

          {process.env.NODE_ENV === 'development' && (
            <details className="mt-6 p-4 bg-muted rounded-lg max-w-2xl w-full">
              <summary className="cursor-pointer font-medium mb-2">
                Error Details (Development)
              </summary>
              <pre className="text-sm text-muted-foreground whitespace-pre-wrap">
                {error.message}
                {error.stack && `\n\n${error.stack}`}
              </pre>
            </details>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
