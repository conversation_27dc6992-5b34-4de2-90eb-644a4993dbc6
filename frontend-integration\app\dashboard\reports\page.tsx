'use client';

/**
 * Reports Dashboard - Main Reports Hub
 *
 * Features:
 * - Overview of all available reports
 * - Quick access to different report categories
 * - Recent reports and analytics
 * - Export capabilities overview
 */

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { PageHeader } from '@/components/ui/page-header';
import {
  BarChart3,
  Calendar,
  CreditCard,
  Download,
  FileText,
  GraduationCap,
  TrendingUp,
  Users,
} from 'lucide-react';
import { useRouter } from 'next/navigation';

// Report categories with their respective routes
const reportCategories = [
  {
    id: 'students',
    title: 'Student Reports',
    description: 'Comprehensive student information, enrollment, and academic records',
    icon: Users,
    color: 'text-blue-600',
    bgColor: 'bg-blue-50',
    route: '/dashboard/reports/students',
    count: 450,
    lastUpdated: '2 hours ago',
  },
  {
    id: 'attendance',
    title: 'Attendance Reports',
    description: 'Daily, weekly, and monthly attendance tracking and analytics',
    icon: Calendar,
    color: 'text-green-600',
    bgColor: 'bg-green-50',
    route: '/dashboard/reports/attendance',
    count: 1250,
    lastUpdated: '1 hour ago',
  },
  {
    id: 'fees',
    title: 'Fee Reports',
    description: 'Financial records, payment tracking, and fee collection analytics',
    icon: CreditCard,
    color: 'text-purple-600',
    bgColor: 'bg-purple-50',
    route: '/dashboard/reports/fees',
    count: 890,
    lastUpdated: '3 hours ago',
  },
  {
    id: 'exams',
    title: 'Exam Reports',
    description: 'Examination schedules, results, and performance analytics',
    icon: FileText,
    color: 'text-orange-600',
    bgColor: 'bg-orange-50',
    route: '/dashboard/reports/exams',
    count: 125,
    lastUpdated: '4 hours ago',
  },
  {
    id: 'academic',
    title: 'Academic Performance',
    description: 'Grade analysis, subject performance, and academic trends',
    icon: GraduationCap,
    color: 'text-indigo-600',
    bgColor: 'bg-indigo-50',
    route: '/dashboard/reports/academic',
    count: 2340,
    lastUpdated: '1 hour ago',
  },
  {
    id: 'analytics',
    title: 'School Analytics',
    description: 'Overall school performance metrics and trend analysis',
    icon: TrendingUp,
    color: 'text-red-600',
    bgColor: 'bg-red-50',
    route: '/dashboard/reports/analytics',
    count: 45,
    lastUpdated: '30 minutes ago',
  },
];

// Recent exports/downloads
const recentExports = [
  {
    id: '1',
    name: 'Student List - Grade 10',
    type: 'CSV',
    date: '2024-03-01',
    size: '2.3 MB',
    status: 'Completed',
  },
  {
    id: '2',
    name: 'Attendance Report - February',
    type: 'PDF',
    date: '2024-02-29',
    size: '1.8 MB',
    status: 'Completed',
  },
  {
    id: '3',
    name: 'Fee Collection Summary',
    type: 'Excel',
    date: '2024-02-28',
    size: '3.1 MB',
    status: 'Completed',
  },
];

export default function ReportsPage() {
  const router = useRouter();

  const handleNavigateToReport = (route: string) => {
    router.push(route);
  };

  return (
    <div className='container mx-auto p-4 sm:p-6 lg:p-8 space-y-8'>
      {/* Page Header */}
      <PageHeader
        title='Reports & Analytics'
        description='Generate comprehensive reports and export data for analysis'
        icon={BarChart3}
        badge={{ label: 'All Reports', variant: 'outline' }}
      />

      {/* Quick Stats */}
      <div className='grid grid-cols-2 lg:grid-cols-4 gap-4'>
        <Card>
          <CardContent className='p-6'>
            <div className='flex items-center space-x-2'>
              <div className='p-2 rounded-lg bg-blue-50'>
                <FileText className='h-4 w-4 text-blue-600' />
              </div>
              <div>
                <p className='text-sm font-medium text-muted-foreground'>Total Reports</p>
                <p className='text-2xl font-bold'>24</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className='p-6'>
            <div className='flex items-center space-x-2'>
              <div className='p-2 rounded-lg bg-green-50'>
                <Download className='h-4 w-4 text-green-600' />
              </div>
              <div>
                <p className='text-sm font-medium text-muted-foreground'>This Month</p>
                <p className='text-2xl font-bold'>156</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className='p-6'>
            <div className='flex items-center space-x-2'>
              <div className='p-2 rounded-lg bg-purple-50'>
                <TrendingUp className='h-4 w-4 text-purple-600' />
              </div>
              <div>
                <p className='text-sm font-medium text-muted-foreground'>Data Points</p>
                <p className='text-2xl font-bold'>5.2K</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className='p-6'>
            <div className='flex items-center space-x-2'>
              <div className='p-2 rounded-lg bg-orange-50'>
                <BarChart3 className='h-4 w-4 text-orange-600' />
              </div>
              <div>
                <p className='text-sm font-medium text-muted-foreground'>Categories</p>
                <p className='text-2xl font-bold'>{reportCategories.length}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Report Categories */}
      <div>
        <h2 className='text-2xl font-bold mb-6'>Report Categories</h2>
        <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6'>
          {reportCategories.map(category => (
            <Card key={category.id} className='hover:shadow-md transition-shadow cursor-pointer'>
              <CardHeader>
                <div className='flex items-center justify-between'>
                  <div className={`p-3 rounded-lg ${category.bgColor}`}>
                    <category.icon className={`h-6 w-6 ${category.color}`} />
                  </div>
                  <Badge variant='outline'>{category.count} records</Badge>
                </div>
                <CardTitle className='text-lg'>{category.title}</CardTitle>
                <CardDescription>{category.description}</CardDescription>
              </CardHeader>
              <CardContent>
                <div className='flex items-center justify-between'>
                  <p className='text-sm text-muted-foreground'>Updated {category.lastUpdated}</p>
                  <Button
                    variant='outline'
                    size='sm'
                    onClick={() => handleNavigateToReport(category.route)}
                  >
                    View Reports
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>

      {/* Recent Exports */}
      <div>
        <h2 className='text-2xl font-bold mb-6'>Recent Exports</h2>
        <Card>
          <CardContent className='p-6'>
            <div className='space-y-4'>
              {recentExports.map(export_ => (
                <div key={export_.id} className='flex items-center justify-between p-4 border rounded-lg'>
                  <div className='flex items-center space-x-4'>
                    <div className='p-2 rounded-lg bg-gray-50'>
                      <Download className='h-4 w-4 text-gray-600' />
                    </div>
                    <div>
                      <p className='font-medium'>{export_.name}</p>
                      <p className='text-sm text-muted-foreground'>
                        {export_.type} • {export_.size} • {export_.date}
                      </p>
                    </div>
                  </div>
                  <div className='flex items-center space-x-2'>
                    <Badge variant='outline' className='text-green-600'>
                      {export_.status}
                    </Badge>
                    <Button variant='ghost' size='sm'>
                      <Download className='h-4 w-4' />
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
