/**
 * Fee Service
 * 
 * Handles all fee-related API calls
 */

import { apiUtils } from '../apiClient';
import type { 
  Fee, 
  FeeFilters, 
  FeeStats,
  PaginatedResponse 
} from '../../types';

export interface CreateFeeData {
  student_id: string;
  fee_type: 'TUITION' | 'TRANSPORT' | 'LIBRARY' | 'LABORATORY' | 'SPORTS' | 'OTHER';
  amount: number;
  due_date: string;
  discount?: number;
  notes?: string;
}

export interface UpdateFeeData extends Partial<CreateFeeData> {
  status?: 'PAID' | 'PENDING' | 'OVERDUE' | 'PARTIAL' | 'CANCELLED';
  paid_date?: string;
  payment_method?: 'CASH' | 'CARD' | 'BANK_TRANSFER' | 'CHEQUE' | 'ONLINE';
  transaction_id?: string;
  late_fee?: number;
}

export interface PaymentData {
  amount: number;
  payment_method: 'CASH' | 'CARD' | 'BANK_TRANSFER' | 'CHEQUE' | 'ONLINE';
  transaction_id?: string;
  payment_date: string;
  notes?: string;
}

export class FeeService {
  private static readonly BASE_URL = '/fees';

  static async getFees(query: FeeFilters = {}): Promise<PaginatedResponse<Fee>> {
    const params = new URLSearchParams();
    
    Object.entries(query).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        params.append(key, String(value));
      }
    });

    const url = `${this.BASE_URL}${params.toString() ? `?${params.toString()}` : ''}`;
    return apiUtils.get<PaginatedResponse<Fee>>(url);
  }

  static async getFee(id: string): Promise<Fee> {
    return apiUtils.get<Fee>(`${this.BASE_URL}/${id}`);
  }

  static async createFee(data: CreateFeeData): Promise<Fee> {
    return apiUtils.post<Fee>(this.BASE_URL, data);
  }

  static async updateFee(id: string, data: UpdateFeeData): Promise<Fee> {
    return apiUtils.patch<Fee>(`${this.BASE_URL}/${id}`, data);
  }

  static async deleteFee(id: string): Promise<void> {
    return apiUtils.delete<void>(`${this.BASE_URL}/${id}`);
  }

  static async getFeeStats(): Promise<FeeStats> {
    return apiUtils.get<FeeStats>(`${this.BASE_URL}/stats`);
  }

  static async processPayment(feeId: string, paymentData: PaymentData): Promise<Fee> {
    return apiUtils.post<Fee>(`${this.BASE_URL}/${feeId}/payment`, paymentData);
  }

  static async getOverdueFees(): Promise<Fee[]> {
    return apiUtils.get<Fee[]>(`${this.BASE_URL}/overdue`);
  }

  static async sendPaymentReminder(feeId: string): Promise<void> {
    return apiUtils.post<void>(`${this.BASE_URL}/${feeId}/reminder`);
  }

  static async generateInvoice(feeId: string): Promise<{ invoiceUrl: string }> {
    return apiUtils.post<{ invoiceUrl: string }>(`${this.BASE_URL}/${feeId}/invoice`);
  }

  static async getPaymentHistory(studentId: string): Promise<Array<{
    id: string;
    feeType: string;
    amount: number;
    paymentDate: string;
    paymentMethod: string;
    transactionId?: string;
    status: string;
  }>> {
    return apiUtils.get<Array<{
      id: string;
      feeType: string;
      amount: number;
      paymentDate: string;
      paymentMethod: string;
      transactionId?: string;
      status: string;
    }>>(`${this.BASE_URL}/student/${studentId}/history`);
  }
}

export const feeService = FeeService;
