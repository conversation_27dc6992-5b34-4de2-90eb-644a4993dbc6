/**
 * Class Schema - Zod Validation with React Hook Form
 * 
 * Features:
 * - Comprehensive validation rules
 * - Type-safe form handling
 * - Custom validation messages
 * - Conditional validation
 * - Academic year validation
 * - Capacity and enrollment validation
 * - Form state management
 */

import { z } from 'zod';
import { useForm, UseFormReturn } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';

// Base validation schemas
const classNameSchema = z
  .string()
  .min(1, 'Class name is required')
  .min(2, 'Class name must be at least 2 characters')
  .max(50, 'Class name must be less than 50 characters')
  .regex(
    /^[a-zA-Z0-9\s\-]+$/,
    'Only letters, numbers, spaces, and hyphens are allowed'
  );

const gradeSchema = z
  .string()
  .min(1, 'Grade is required')
  .max(10, 'Grade must be less than 10 characters');

const sectionSchema = z
  .string()
  .min(1, 'Section is required')
  .max(5, 'Section must be less than 5 characters')
  .regex(
    /^[A-Z0-9]+$/,
    'Section must contain only uppercase letters and numbers'
  );

const capacitySchema = z
  .number({
    required_error: 'Capacity is required',
    invalid_type_error: 'Capacity must be a number',
  })
  .min(1, 'Capacity must be at least 1')
  .max(100, 'Capacity cannot exceed 100 students');

const roomSchema = z
  .string()
  .max(20, 'Room number must be less than 20 characters')
  .regex(
    /^[a-zA-Z0-9\s\-\.]+$/,
    'Room number can only contain letters, numbers, spaces, hyphens, and periods'
  )
  .optional()
  .or(z.literal(''));

const scheduleSchema = z
  .string()
  .max(100, 'Schedule must be less than 100 characters')
  .optional()
  .or(z.literal(''));

const academicYearSchema = z
  .string()
  .min(1, 'Academic year is required')
  .regex(
    /^\d{4}-\d{4}$/,
    'Academic year must be in format YYYY-YYYY (e.g., 2024-2025)'
  )
  .refine((year) => {
    const [startYear, endYear] = year.split('-').map(Number);
    return endYear === startYear + 1;
  }, 'End year must be exactly one year after start year');

// Teacher ID validation
const teacherIdSchema = z
  .string()
  .min(1, 'Teacher is required');

// Status validation
const statusSchema = z.enum(['ACTIVE', 'INACTIVE'], {
  required_error: 'Status is required',
});

// Class creation schema
export const createClassSchema = z.object({
  // Basic Information
  name: classNameSchema,
  grade: gradeSchema,
  section: sectionSchema,
  capacity: capacitySchema,
  teacher_id: teacherIdSchema,
  academic_year: academicYearSchema,
  
  // Optional Information
  room: roomSchema,
  schedule: scheduleSchema,
  status: statusSchema.default('ACTIVE'),
  subjects: z.array(z.string()).optional(),
  class_monitor: z.string().optional(),
});

// Class update schema (all fields optional except ID constraints)
export const updateClassSchema = createClassSchema.partial();

// Class display schema (includes computed fields)
export const classSchema = createClassSchema.extend({
  id: z.string(),
  enrolled: z.number().min(0, 'Enrolled count must be non-negative').optional(),
  teacher_name: z.string().optional(),
  created_at: z.string().optional(),
  updated_at: z.string().optional(),
});

// Type exports
export type CreateClassInput = z.infer<typeof createClassSchema>;
export type UpdateClassInput = z.infer<typeof updateClassSchema>;
export type ClassData = z.infer<typeof classSchema>;

// Validation functions
export function validateClassCreate(data: unknown): {
  isValid: boolean;
  errors: string[];
  warnings: string[];
} {
  const result = createClassSchema.safeParse(data);
  
  if (result.success) {
    const warnings: string[] = [];
    
    // Check for potential issues
    if (result.data.capacity > 50) {
      warnings.push('Large class size may affect teaching quality');
    }
    
    if (!result.data.room) {
      warnings.push('No room assigned - remember to assign a classroom');
    }
    
    if (!result.data.schedule) {
      warnings.push('No schedule provided - add class schedule for better organization');
    }
    
    return {
      isValid: true,
      errors: [],
      warnings,
    };
  }
  
  return {
    isValid: false,
    errors: result.error.errors.map(err => `${err.path.join('.')}: ${err.message}`),
    warnings: [],
  };
}

export function validateClassUpdate(data: unknown): {
  isValid: boolean;
  errors: string[];
  warnings: string[];
} {
  const result = updateClassSchema.safeParse(data);
  
  if (result.success) {
    const warnings: string[] = [];
    
    // Check for potential issues in updates
    if (result.data.capacity && result.data.capacity > 50) {
      warnings.push('Large class size may affect teaching quality');
    }
    
    return {
      isValid: true,
      errors: [],
      warnings,
    };
  }
  
  return {
    isValid: false,
    errors: result.error.errors.map(err => `${err.path.join('.')}: ${err.message}`),
    warnings: [],
  };
}

// Utility functions
export function generateAcademicYear(): string {
  const currentYear = new Date().getFullYear();
  const currentMonth = new Date().getMonth();
  
  // Academic year typically starts in August/September
  if (currentMonth >= 7) {
    return `${currentYear}-${currentYear + 1}`;
  } else {
    return `${currentYear - 1}-${currentYear}`;
  }
}

export function validateClassCapacity(capacity: number, enrolled: number): boolean {
  return enrolled <= capacity;
}

export function calculateOccupancyRate(capacity: number, enrolled: number): number {
  if (capacity === 0) return 0;
  return Math.round((enrolled / capacity) * 100);
}

// Custom hook for class form
export function useClassForm(
  defaultValues?: Partial<CreateClassInput>,
  mode: 'create' | 'update' = 'create'
): UseFormReturn<CreateClassInput | UpdateClassInput> {
  const schema = mode === 'create' ? createClassSchema : updateClassSchema;
  
  return useForm({
    resolver: zodResolver(schema),
    defaultValues: {
      // Default values for create mode
      status: 'ACTIVE',
      academic_year: generateAcademicYear(),
      capacity: 30,
      subjects: [],
      ...defaultValues,
    } as any,
    mode: 'onChange',
    criteriaMode: 'all',
  });
}

// Form field validation helpers
export const classFormValidation = {
  name: {
    required: 'Class name is required',
    minLength: { value: 2, message: 'Class name must be at least 2 characters' },
    maxLength: { value: 50, message: 'Class name must be less than 50 characters' },
    pattern: {
      value: /^[a-zA-Z0-9\s\-]+$/,
      message: 'Only letters, numbers, spaces, and hyphens are allowed',
    },
  },
  grade: {
    required: 'Grade is required',
    maxLength: { value: 10, message: 'Grade must be less than 10 characters' },
  },
  section: {
    required: 'Section is required',
    maxLength: { value: 5, message: 'Section must be less than 5 characters' },
    pattern: {
      value: /^[A-Z0-9]+$/,
      message: 'Section must contain only uppercase letters and numbers',
    },
  },
  capacity: {
    required: 'Capacity is required',
    min: { value: 1, message: 'Capacity must be at least 1' },
    max: { value: 100, message: 'Capacity cannot exceed 100 students' },
  },
  teacher_id: {
    required: 'Teacher is required',
  },
  academic_year: {
    required: 'Academic year is required',
    pattern: {
      value: /^\d{4}-\d{4}$/,
      message: 'Academic year must be in format YYYY-YYYY',
    },
  },
};
