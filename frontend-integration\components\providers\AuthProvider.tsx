/**
 * Auth Provider Component
 * Initializes authentication state and provides loading states
 */

'use client';

import React from 'react';
import { useAuthInitializer } from '@/hooks/useAuthInitializer';

interface AuthProviderProps {
  children: React.ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const { isInitialized, isLoading } = useAuthInitializer();

  // Show loading spinner while auth is initializing
  if (!isInitialized && isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Initializing authentication...</p>
        </div>
      </div>
    );
  }

  return <>{children}</>;
};

export default AuthProvider;
