/**
 * Debug 500 Internal Server Error
 * 
 * This script helps identify the exact difference between the working curl request
 * and the failing frontend request by replicating both scenarios.
 */

const API_URL = 'http://127.0.0.1:8000';

// Test different request scenarios
async function debugRequests() {
  console.log('🔍 Debugging 500 Internal Server Error\n');
  
  // Test 1: Basic request (no auth) - like curl without token
  console.log('1. Testing basic request (no auth)...');
  try {
    const response = await fetch(`${API_URL}/api/v1/teachers/`);
    console.log(`   Status: ${response.status} ${response.statusText}`);
    
    if (response.ok) {
      const data = await response.json();
      console.log(`   Response: ${JSON.stringify(data).substring(0, 100)}...`);
    } else {
      const errorText = await response.text();
      console.log(`   Error: ${errorText.substring(0, 200)}...`);
    }
  } catch (error) {
    console.log(`   Network Error: ${error.message}`);
  }
  
  // Test 2: With fake token (to test auth handling)
  console.log('\n2. Testing with fake token...');
  try {
    const response = await fetch(`${API_URL}/api/v1/teachers/`, {
      headers: {
        'Authorization': 'Bearer fake-token-123',
        'Content-Type': 'application/json'
      }
    });
    console.log(`   Status: ${response.status} ${response.statusText}`);
    
    if (!response.ok) {
      const errorText = await response.text();
      console.log(`   Error: ${errorText.substring(0, 200)}...`);
    }
  } catch (error) {
    console.log(`   Network Error: ${error.message}`);
  }
  
  // Test 3: Different headers (to test header sensitivity)
  console.log('\n3. Testing with different headers...');
  const headerTests = [
    { name: 'No Content-Type', headers: {} },
    { name: 'Only Content-Type', headers: { 'Content-Type': 'application/json' } },
    { name: 'With User-Agent', headers: { 'Content-Type': 'application/json', 'User-Agent': 'Mozilla/5.0' } },
    { name: 'With Origin', headers: { 'Content-Type': 'application/json', 'Origin': 'http://localhost:3000' } },
    { name: 'With Referer', headers: { 'Content-Type': 'application/json', 'Referer': 'http://localhost:3000/dashboard/teachers' } }
  ];
  
  for (const test of headerTests) {
    try {
      const response = await fetch(`${API_URL}/api/v1/teachers/`, {
        headers: test.headers
      });
      console.log(`   ${test.name}: ${response.status} ${response.statusText}`);
    } catch (error) {
      console.log(`   ${test.name}: Network Error - ${error.message}`);
    }
  }
  
  // Test 4: Different HTTP methods
  console.log('\n4. Testing different HTTP methods...');
  const methods = ['GET', 'HEAD', 'OPTIONS'];
  
  for (const method of methods) {
    try {
      const response = await fetch(`${API_URL}/api/v1/teachers/`, {
        method,
        headers: { 'Content-Type': 'application/json' }
      });
      console.log(`   ${method}: ${response.status} ${response.statusText}`);
    } catch (error) {
      console.log(`   ${method}: Network Error - ${error.message}`);
    }
  }
  
  // Test 5: Test other endpoints to isolate the issue
  console.log('\n5. Testing other endpoints...');
  const endpoints = [
    '/api/v1/health',
    '/api/v1/teachers',  // without trailing slash
    '/api/v1/auth/login'
  ];
  
  for (const endpoint of endpoints) {
    try {
      const response = await fetch(`${API_URL}${endpoint}`);
      console.log(`   ${endpoint}: ${response.status} ${response.statusText}`);
    } catch (error) {
      console.log(`   ${endpoint}: Network Error - ${error.message}`);
    }
  }
  
  // Test 6: Simulate exact frontend request
  console.log('\n6. Simulating exact frontend request...');
  try {
    const response = await fetch(`${API_URL}/api/v1/teachers/`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'Origin': 'http://localhost:3000',
        'Referer': 'http://localhost:3000/dashboard/teachers',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
      }
    });
    
    console.log(`   Frontend simulation: ${response.status} ${response.statusText}`);
    
    if (!response.ok) {
      const errorText = await response.text();
      console.log(`   Error details: ${errorText}`);
    } else {
      const data = await response.json();
      console.log(`   Success: ${JSON.stringify(data)}`);
    }
  } catch (error) {
    console.log(`   Frontend simulation error: ${error.message}`);
  }
  
  console.log('\n📋 Summary:');
  console.log('- If basic request (test 1) works but frontend simulation (test 6) fails,');
  console.log('  the issue is likely with specific headers sent by the browser.');
  console.log('- If all requests fail with 500, the issue is in the backend endpoint itself.');
  console.log('- Check your FastAPI server logs for the exact error details.');
  console.log('- Common causes: database connection issues, missing environment variables,');
  console.log('  unhandled exceptions in the endpoint code.');
}

// Run the debug tests
debugRequests().catch(console.error);
