'use client';

import { ModuleError } from '@/components/ui/module-error';

interface StudentFeeErrorProps {
  error: Error & { digest?: string };
  reset: () => void;
}

export default function StudentFeeError({ error, reset }: StudentFeeErrorProps) {
  return (
    <ModuleError
      error={error}
      reset={reset}
      moduleName="Student Fee"
      moduleIcon="💰"
      backHref="/dashboard"
    />
  );
}
