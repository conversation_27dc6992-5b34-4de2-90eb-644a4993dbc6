'use client';

/**
 * Academic Performance Report Page - Comprehensive Academic Analytics
 *
 * Features:
 * - Student academic performance analysis
 * - Grade distribution and trends
 * - CSV, PDF, and Excel export
 * - Subject-wise performance metrics
 */

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { PageHeader } from '@/components/ui/page-header';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { bulkExport, EXPORT_CONFIGS, formatPercentageForExport } from '@/lib/export-utils';
import { Award, Download, FileSpreadsheet, FileText, GraduationCap, Search, TrendingUp } from 'lucide-react';
import { useState } from 'react';

// Mock academic performance data
const mockAcademicData = [
  {
    id: '1',
    studentName: '<PERSON>',
    studentId: 'STU001',
    grade: 'Grade 10',
    class: '10-A',
    subject: 'Mathematics',
    examType: 'Midterm',
    marks: 95,
    totalMarks: 100,
    percentage: 95,
    letterGrade: 'A+',
    gpa: 4.0,
    rank: 1,
    teacher: 'Dr. <PERSON>',
    term: 'Fall 2024',
  },
  {
    id: '2',
    studentName: 'Bob Smith',
    studentId: 'STU002',
    grade: 'Grade 10',
    class: '10-B',
    subject: 'Physics',
    examType: 'Final',
    marks: 87,
    totalMarks: 100,
    percentage: 87,
    letterGrade: 'B+',
    gpa: 3.3,
    rank: 5,
    teacher: 'Prof. Michael Chen',
    term: 'Fall 2024',
  },
  {
    id: '3',
    studentName: 'Carol Davis',
    studentId: 'STU003',
    grade: 'Grade 11',
    class: '11-A',
    subject: 'English',
    examType: 'Essay',
    marks: 92,
    totalMarks: 100,
    percentage: 92,
    letterGrade: 'A-',
    gpa: 3.7,
    rank: 2,
    teacher: 'Ms. Emily Davis',
    term: 'Fall 2024',
  },
  {
    id: '4',
    studentName: 'David Wilson',
    studentId: 'STU004',
    grade: 'Grade 11',
    class: '11-B',
    subject: 'Chemistry',
    examType: 'Lab Report',
    marks: 82,
    totalMarks: 100,
    percentage: 82,
    letterGrade: 'B',
    gpa: 3.0,
    rank: 8,
    teacher: 'Dr. Robert Wilson',
    term: 'Fall 2024',
  },
  {
    id: '5',
    studentName: 'Eva Brown',
    studentId: 'STU005',
    grade: 'Grade 12',
    class: '12-A',
    subject: 'Biology',
    examType: 'Quiz',
    marks: 48,
    totalMarks: 50,
    percentage: 96,
    letterGrade: 'A+',
    gpa: 4.0,
    rank: 1,
    teacher: 'Ms. Lisa Anderson',
    term: 'Fall 2024',
  },
];

const subjects = ['All Subjects', 'Mathematics', 'Physics', 'English', 'Chemistry', 'Biology', 'History'];
const grades = ['All Grades', 'Grade 9', 'Grade 10', 'Grade 11', 'Grade 12'];
const examTypes = ['All Types', 'Midterm', 'Final', 'Quiz', 'Essay', 'Lab Report', 'Project'];
const terms = ['All Terms', 'Fall 2024', 'Spring 2024', 'Summer 2024'];

export default function AcademicReportPage() {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedSubject, setSelectedSubject] = useState('All Subjects');
  const [selectedGrade, setSelectedGrade] = useState('All Grades');
  const [selectedExamType, setSelectedExamType] = useState('All Types');
  const [selectedTerm, setSelectedTerm] = useState('All Terms');
  const [isExporting, setIsExporting] = useState(false);

  // Filter academic data based on search and filters
  const filteredAcademicData = mockAcademicData.filter(record => {
    const matchesSearch = record.studentName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         record.studentId.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesSubject = selectedSubject === 'All Subjects' || record.subject === selectedSubject;
    const matchesGrade = selectedGrade === 'All Grades' || record.grade === selectedGrade;
    const matchesExamType = selectedExamType === 'All Types' || record.examType === selectedExamType;
    const matchesTerm = selectedTerm === 'All Terms' || record.term === selectedTerm;
    
    return matchesSearch && matchesSubject && matchesGrade && matchesExamType && matchesTerm;
  });

  // Calculate academic statistics
  const academicStats = {
    totalRecords: filteredAcademicData.length,
    averagePercentage: filteredAcademicData.length > 0
      ? Math.round(filteredAcademicData.reduce((sum, record) => sum + record.percentage, 0) / filteredAcademicData.length)
      : 0,
    averageGPA: filteredAcademicData.length > 0
      ? (filteredAcademicData.reduce((sum, record) => sum + record.gpa, 0) / filteredAcademicData.length).toFixed(2)
      : '0.00',
    topPerformers: filteredAcademicData.filter(r => r.percentage >= 90).length,
    passRate: filteredAcademicData.length > 0
      ? Math.round((filteredAcademicData.filter(r => r.percentage >= 60).length / filteredAcademicData.length) * 100)
      : 0,
  };

  // Export handlers
  const handleExport = async (format: 'csv' | 'pdf' | 'excel') => {
    setIsExporting(true);
    
    try {
      const config = {
        ...EXPORT_CONFIGS.academic,
        filename: `${EXPORT_CONFIGS.academic.filename}-${new Date().toISOString().split('T')[0]}`,
        transformations: {
          percentage: formatPercentageForExport,
        }
      };
      
      await bulkExport(filteredAcademicData, format, config);
    } catch (error) {
      console.error('Export failed:', error);
    } finally {
      setIsExporting(false);
    }
  };

  const getGradeColor = (grade: string) => {
    if (grade.startsWith('A')) return 'bg-green-100 text-green-800';
    if (grade.startsWith('B')) return 'bg-blue-100 text-blue-800';
    if (grade.startsWith('C')) return 'bg-yellow-100 text-yellow-800';
    if (grade.startsWith('D')) return 'bg-orange-100 text-orange-800';
    return 'bg-red-100 text-red-800';
  };

  const getPerformanceIcon = (percentage: number) => {
    if (percentage >= 90) return <Award className='w-4 h-4 text-green-600' />;
    if (percentage >= 80) return <TrendingUp className='w-4 h-4 text-blue-600' />;
    if (percentage >= 70) return <GraduationCap className='w-4 h-4 text-yellow-600' />;
    return <GraduationCap className='w-4 h-4 text-gray-600' />;
  };

  return (
    <div className='container mx-auto p-4 sm:p-6 lg:p-8 space-y-6'>
      {/* Page Header */}
      <PageHeader
        title='Academic Performance Report'
        description='Generate and export comprehensive academic performance analytics'
        icon={GraduationCap}
        badge={{ label: `${filteredAcademicData.length} records`, variant: 'outline' }}
      />

      {/* Academic Statistics */}
      <div className='grid grid-cols-2 lg:grid-cols-5 gap-4'>
        <Card>
          <CardContent className='p-4'>
            <div className='text-center'>
              <p className='text-2xl font-bold text-blue-600'>{academicStats.totalRecords}</p>
              <p className='text-sm text-muted-foreground'>Total Records</p>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className='p-4'>
            <div className='text-center'>
              <p className='text-2xl font-bold text-green-600'>{academicStats.averagePercentage}%</p>
              <p className='text-sm text-muted-foreground'>Avg Percentage</p>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className='p-4'>
            <div className='text-center'>
              <p className='text-2xl font-bold text-purple-600'>{academicStats.averageGPA}</p>
              <p className='text-sm text-muted-foreground'>Average GPA</p>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className='p-4'>
            <div className='text-center'>
              <p className='text-2xl font-bold text-orange-600'>{academicStats.topPerformers}</p>
              <p className='text-sm text-muted-foreground'>Top Performers</p>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className='p-4'>
            <div className='text-center'>
              <p className='text-2xl font-bold text-red-600'>{academicStats.passRate}%</p>
              <p className='text-sm text-muted-foreground'>Pass Rate</p>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Export Actions */}
      <Card>
        <CardContent className='p-6'>
          <div className='flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4'>
            <div>
              <h3 className='text-lg font-semibold mb-2'>Export Options</h3>
              <p className='text-sm text-muted-foreground'>
                Download academic performance data in your preferred format
              </p>
            </div>
            <div className='flex gap-2'>
              <Button
                variant='outline'
                onClick={() => handleExport('csv')}
                disabled={isExporting || filteredAcademicData.length === 0}
              >
                <FileText className='w-4 h-4 mr-2' />
                Export CSV
              </Button>
              <Button
                variant='outline'
                onClick={() => handleExport('excel')}
                disabled={isExporting || filteredAcademicData.length === 0}
              >
                <FileSpreadsheet className='w-4 h-4 mr-2' />
                Export Excel
              </Button>
              <Button
                variant='outline'
                onClick={() => handleExport('pdf')}
                disabled={isExporting || filteredAcademicData.length === 0}
              >
                <Download className='w-4 h-4 mr-2' />
                Export PDF
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Filters */}
      <Card>
        <CardContent className='p-6'>
          <div className='grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-5 gap-4'>
            {/* Search */}
            <div className='relative'>
              <Search className='absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4' />
              <Input
                placeholder='Search students...'
                value={searchTerm}
                onChange={e => setSearchTerm(e.target.value)}
                className='pl-10'
              />
            </div>

            {/* Subject Filter */}
            <Select value={selectedSubject} onValueChange={setSelectedSubject}>
              <SelectTrigger>
                <SelectValue placeholder='Select subject' />
              </SelectTrigger>
              <SelectContent>
                {subjects.map(subject => (
                  <SelectItem key={subject} value={subject}>
                    {subject}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            {/* Grade Filter */}
            <Select value={selectedGrade} onValueChange={setSelectedGrade}>
              <SelectTrigger>
                <SelectValue placeholder='Select grade' />
              </SelectTrigger>
              <SelectContent>
                {grades.map(grade => (
                  <SelectItem key={grade} value={grade}>
                    {grade}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            {/* Exam Type Filter */}
            <Select value={selectedExamType} onValueChange={setSelectedExamType}>
              <SelectTrigger>
                <SelectValue placeholder='Select exam type' />
              </SelectTrigger>
              <SelectContent>
                {examTypes.map(type => (
                  <SelectItem key={type} value={type}>
                    {type}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            {/* Term Filter */}
            <Select value={selectedTerm} onValueChange={setSelectedTerm}>
              <SelectTrigger>
                <SelectValue placeholder='Select term' />
              </SelectTrigger>
              <SelectContent>
                {terms.map(term => (
                  <SelectItem key={term} value={term}>
                    {term}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          
          <div className='mt-4 text-sm text-muted-foreground'>
            Showing {filteredAcademicData.length} academic performance records
          </div>
        </CardContent>
      </Card>

      {/* Academic Records */}
      <div className='space-y-4'>
        {filteredAcademicData.map(record => (
          <Card key={record.id} className='hover:shadow-md transition-shadow'>
            <CardContent className='p-6'>
              <div className='flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4'>
                <div className='flex-1'>
                  <div className='flex items-center gap-3 mb-2'>
                    {getPerformanceIcon(record.percentage)}
                    <h3 className='text-lg font-semibold'>{record.studentName}</h3>
                    <Badge className={getGradeColor(record.letterGrade)}>
                      {record.letterGrade}
                    </Badge>
                  </div>
                  <div className='grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-2 text-sm text-muted-foreground mb-3'>
                    <p>ID: {record.studentId}</p>
                    <p>Grade: {record.grade}</p>
                    <p>Class: {record.class}</p>
                    <p>Subject: {record.subject}</p>
                    <p>Exam: {record.examType}</p>
                    <p>Teacher: {record.teacher}</p>
                    <p>Term: {record.term}</p>
                    <p>Rank: #{record.rank}</p>
                  </div>
                  
                  <div className='grid grid-cols-2 lg:grid-cols-4 gap-4 pt-3 border-t'>
                    <div className='text-center'>
                      <p className='text-lg font-semibold text-blue-600'>{record.marks}/{record.totalMarks}</p>
                      <p className='text-xs text-muted-foreground'>Marks Obtained</p>
                    </div>
                    <div className='text-center'>
                      <p className='text-lg font-semibold text-green-600'>{record.percentage}%</p>
                      <p className='text-xs text-muted-foreground'>Percentage</p>
                    </div>
                    <div className='text-center'>
                      <p className='text-lg font-semibold text-purple-600'>{record.gpa}</p>
                      <p className='text-xs text-muted-foreground'>GPA</p>
                    </div>
                    <div className='text-center'>
                      <p className='text-lg font-semibold text-orange-600'>#{record.rank}</p>
                      <p className='text-xs text-muted-foreground'>Class Rank</p>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}

        {filteredAcademicData.length === 0 && (
          <div className='text-center py-12'>
            <GraduationCap className='w-16 h-16 text-gray-300 mx-auto mb-4' />
            <h3 className='text-lg font-medium text-gray-900 mb-2'>No academic records found</h3>
            <p className='text-gray-500'>
              Try adjusting your search or filter criteria
            </p>
          </div>
        )}
      </div>
    </div>
  );
}
