'use client';

import { ArrowLeft, Calendar, CalendarDays, Clock, Edit, MapPin, Users } from 'lucide-react';
import Link from 'next/link';
import { notFound } from 'next/navigation';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

// Mock data for demonstration
const mockEvents = [
  {
    id: 1,
    title: 'Annual Science Fair',
    description: 'Students showcase their innovative science projects and experiments.',
    date: '2024-03-15',
    time: '09:00 AM',
    endTime: '04:00 PM',
    location: 'Main Auditorium',
    category: 'Academic',
    status: 'Upcoming',
    organizer: 'Science Department',
    maxParticipants: 200,
    registeredParticipants: 145,
    requirements: 'All students must register by March 10th',
  },
  {
    id: 2,
    title: 'Parent-Teacher Conference',
    description: 'Individual meetings between parents and teachers to discuss student progress.',
    date: '2024-03-20',
    time: '02:00 PM',
    endTime: '06:00 PM',
    location: 'Classrooms',
    category: 'Meeting',
    status: 'Upcoming',
    organizer: 'Administration',
    maxParticipants: 100,
    registeredParticipants: 78,
    requirements: 'Parents must schedule appointments in advance',
  },
];

interface EventDetailPageProps {
  params: { id: string };
}

export default function EventDetailPage({ params }: EventDetailPageProps) {
  const event = mockEvents.find(e => e.id === parseInt(params.id));

  if (!event) {
    notFound();
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Upcoming':
        return 'bg-blue-100 text-blue-800';
      case 'Ongoing':
        return 'bg-green-100 text-green-800';
      case 'Completed':
        return 'bg-gray-100 text-gray-800';
      case 'Cancelled':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'Academic':
        return 'bg-purple-100 text-purple-800';
      case 'Sports':
        return 'bg-green-100 text-green-800';
      case 'Cultural':
        return 'bg-pink-100 text-pink-800';
      case 'Meeting':
        return 'bg-blue-100 text-blue-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className='container mx-auto p-4 sm:p-6 lg:p-8 max-w-4xl'>
      {/* Header */}
      <div className='flex items-center justify-between mb-8'>
        <div className='flex items-center gap-4'>
          <Link href='/dashboard/events'>
            <Button variant='outline' size='sm'>
              <ArrowLeft className='w-4 h-4 mr-2' />
              Back to Events
            </Button>
          </Link>
          <div>
            <h1 className='text-3xl font-bold text-gray-900 flex items-center gap-2'>
              <CalendarDays className='w-8 h-8 text-blue-600' />
              Event Details
            </h1>
            <p className='text-gray-600 mt-1'>View detailed event information</p>
          </div>
        </div>
        <Link href={`/dashboard/events/${event.id}/edit`}>
          <Button>
            <Edit className='w-4 h-4 mr-2' />
            Edit Event
          </Button>
        </Link>
      </div>

      <div className='grid grid-cols-1 lg:grid-cols-3 gap-8'>
        {/* Main Content */}
        <div className='lg:col-span-2 space-y-6'>
          {/* Basic Information */}
          <Card>
            <CardHeader>
              <CardTitle className='flex items-center gap-2'>
                <Calendar className='w-5 h-5' />
                Event Information
              </CardTitle>
            </CardHeader>
            <CardContent className='space-y-4'>
              <div>
                <h2 className='text-2xl font-bold text-gray-900 mb-2'>{event.title}</h2>
                <p className='text-gray-700 leading-relaxed'>{event.description}</p>
              </div>
              <div className='flex flex-wrap gap-2'>
                <Badge className={getCategoryColor(event.category)}>
                  {event.category}
                </Badge>
                <Badge className={getStatusColor(event.status)}>
                  {event.status}
                </Badge>
              </div>
            </CardContent>
          </Card>

          {/* Date & Time */}
          <Card>
            <CardHeader>
              <CardTitle className='flex items-center gap-2'>
                <Clock className='w-5 h-5' />
                Schedule
              </CardTitle>
            </CardHeader>
            <CardContent className='space-y-4'>
              <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
                <div>
                  <label className='text-sm font-medium text-gray-500'>Date</label>
                  <div className='flex items-center gap-2'>
                    <Calendar className='w-4 h-4 text-gray-500' />
                    <p className='text-lg font-semibold'>{event.date}</p>
                  </div>
                </div>
                <div>
                  <label className='text-sm font-medium text-gray-500'>Time</label>
                  <div className='flex items-center gap-2'>
                    <Clock className='w-4 h-4 text-gray-500' />
                    <p className='text-lg font-semibold'>{event.time} - {event.endTime}</p>
                  </div>
                </div>
                <div>
                  <label className='text-sm font-medium text-gray-500'>Location</label>
                  <div className='flex items-center gap-2'>
                    <MapPin className='w-4 h-4 text-gray-500' />
                    <p className='text-lg font-semibold'>{event.location}</p>
                  </div>
                </div>
                <div>
                  <label className='text-sm font-medium text-gray-500'>Organizer</label>
                  <p className='text-lg font-semibold'>{event.organizer}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Requirements */}
          {event.requirements && (
            <Card>
              <CardHeader>
                <CardTitle>Requirements</CardTitle>
              </CardHeader>
              <CardContent>
                <p className='text-gray-700'>{event.requirements}</p>
              </CardContent>
            </Card>
          )}
        </div>

        {/* Sidebar */}
        <div className='space-y-6'>
          {/* Participation Stats */}
          <Card>
            <CardHeader>
              <CardTitle className='flex items-center gap-2'>
                <Users className='w-5 h-5' />
                Participation
              </CardTitle>
            </CardHeader>
            <CardContent className='space-y-4'>
              <div className='text-center'>
                <div className='text-3xl font-bold text-blue-600'>
                  {event.registeredParticipants}
                </div>
                <p className='text-sm text-muted-foreground'>Registered</p>
              </div>
              <div className='text-center'>
                <div className='text-xl font-semibold text-gray-700'>
                  {event.maxParticipants}
                </div>
                <p className='text-sm text-muted-foreground'>Max Capacity</p>
              </div>
              <div className='w-full bg-gray-200 rounded-full h-2'>
                <div 
                  className='bg-blue-600 h-2 rounded-full' 
                  style={{ width: `${(event.registeredParticipants / event.maxParticipants) * 100}%` }}
                ></div>
              </div>
              <p className='text-sm text-center text-gray-600'>
                {Math.round((event.registeredParticipants / event.maxParticipants) * 100)}% Full
              </p>
            </CardContent>
          </Card>

          {/* Quick Actions */}
          <Card>
            <CardHeader>
              <CardTitle>Quick Actions</CardTitle>
            </CardHeader>
            <CardContent className='space-y-2'>
              <Button className='w-full' variant='outline'>
                View Participants
              </Button>
              <Button className='w-full' variant='outline'>
                Send Notification
              </Button>
              <Button className='w-full' variant='outline'>
                Export Details
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
