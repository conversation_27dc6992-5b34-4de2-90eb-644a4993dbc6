/** @type {import('next').NextConfig} */
const nextConfig = {
  reactStrictMode: true,
  poweredByHeader: false,

  // Proxy backend API to avoid cross-origin CSP issues
  async rewrites() {
    return [
      {
        source: "/api/:path*",
        destination: "http://127.0.0.1:8000/api/:path*", // ✅ Forward directly without /v1 duplication
      },
    ];
  },

  // Remove CSP headers that conflict with rewrites
  // Let Next.js handle this automatically
};

export default nextConfig;
