/**
 * Auth Initializer Hook
 * Automatically fetches user data when the app loads if a token exists
 */

'use client';

import { useEffect } from 'react';
import { useAuthStore } from '@/stores/authStore';

export const useAuthInitializer = () => {
  const { token, user, fetchUser, isLoading } = useAuthStore();

  useEffect(() => {
    // If we have a token but no user data, fetch it
    if (token && !user && !isLoading) {
      console.log('🔄 Auth initializer: Token found but no user data, fetching...');
      fetchUser();
    }
  }, [token, user, fetchUser, isLoading]);

  // Also check localStorage on mount (in case store hasn't hydrated yet)
  useEffect(() => {
    const checkStoredToken = () => {
      if (typeof window === 'undefined') return;
      
      const storedToken = localStorage.getItem(process.env.NEXT_PUBLIC_AUTH_COOKIE || 'access_token');
      const storedRole = localStorage.getItem(process.env.NEXT_PUBLIC_ROLE_KEY || 'role');
      
      // If we have stored auth data but store is empty, restore it
      if (storedToken && storedRole && !token) {
        console.log('🔄 Auth initializer: Restoring auth from localStorage');
        const { setAuth } = useAuthStore.getState();
        setAuth({ 
          token: storedToken, 
          role: storedRole as any,
          // fetchUser will be called automatically by setAuth
        });
      }
    };

    // Small delay to ensure store has hydrated
    const timer = setTimeout(checkStoredToken, 100);
    return () => clearTimeout(timer);
  }, [token]);

  return {
    isInitialized: !isLoading && (!!user || !token),
    isLoading,
    hasToken: !!token,
    hasUser: !!user,
  };
};

export default useAuthInitializer;
