/**
 * Application Constants for School Management System
 * 
 * This file contains all the constants used throughout the application
 * including API endpoints, user roles, status values, and configuration
 */

// API Configuration
export const API_CONFIG = {
  BASE_URL: process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000/api/v1',
  TIMEOUT: 30000,
  RETRY_ATTEMPTS: 3,
} as const;

// Application Routes
export const ROUTES = {
  HOME: '/',
  LOGIN: '/login',
  DASHBOARD: '/dashboard',
  
  // Student routes
  STUDENTS: '/dashboard/students',
  STUDENT_DETAIL: (id: string) => `/dashboard/students/${id}`,
  STUDENT_CREATE: '/dashboard/students/create',
  
  // Teacher routes
  TEACHERS: '/dashboard/teachers',
  TEACHER_DETAIL: (id: string) => `/dashboard/teachers/${id}`,
  TEACHER_CREATE: '/dashboard/teachers/create',
  
  // Class routes
  CLASSES: '/dashboard/classes',
  CLASS_DETAIL: (id: string) => `/dashboard/classes/${id}`,
  CLASS_CREATE: '/dashboard/classes/create',
  
  // Subject routes
  SUBJECTS: '/dashboard/subjects',
  SUBJECT_DETAIL: (id: string) => `/dashboard/subjects/${id}`,
  SUBJECT_CREATE: '/dashboard/subjects/create',
  
  // Attendance routes
  ATTENDANCE: '/dashboard/attendance',
  ATTENDANCE_TAKE: '/dashboard/attendance/take',
  ATTENDANCE_REPORTS: '/dashboard/attendance/reports',
  
  // Exam routes
  EXAMS: '/dashboard/exams',
  EXAM_DETAIL: (id: string) => `/dashboard/exams/${id}`,
  EXAM_CREATE: '/dashboard/exams/create',
  
  // Fee routes
  FEES: '/dashboard/fees',
  FEE_DETAIL: (id: string) => `/dashboard/fees/${id}`,
  FEE_CREATE: '/dashboard/fees/create',
  
  // Parent routes
  PARENTS: '/dashboard/parents',
  PARENT_DETAIL: (id: string) => `/dashboard/parents/${id}`,
  PARENT_CREATE: '/dashboard/parents/create',
  
  // Other routes
  ANNOUNCEMENTS: '/dashboard/announcements',
  EVENTS: '/dashboard/events',
  REPORTS: '/dashboard/reports',
  SETTINGS: '/dashboard/settings',
  PROFILE: '/dashboard/profile',
} as const;

// User Roles
export const USER_ROLES = {
  SUPER_ADMIN: 'SUPER_ADMIN',
  ADMIN: 'ADMIN',
  STAFF: 'STAFF',
  TEACHER: 'TEACHER',
  STUDENT: 'STUDENT',
  PARENT: 'PARENT',
} as const;

export type UserRole = typeof USER_ROLES[keyof typeof USER_ROLES];

// Student Status
export const STUDENT_STATUS = {
  ACTIVE: 'ACTIVE',
  INACTIVE: 'INACTIVE',
  GRADUATED: 'GRADUATED',
  TRANSFERRED: 'TRANSFERRED',
  SUSPENDED: 'SUSPENDED',
} as const;

export type StudentStatus = typeof STUDENT_STATUS[keyof typeof STUDENT_STATUS];

// Teacher Status
export const TEACHER_STATUS = {
  ACTIVE: 'ACTIVE',
  INACTIVE: 'INACTIVE',
  ON_LEAVE: 'ON_LEAVE',
  TERMINATED: 'TERMINATED',
} as const;

export type TeacherStatus = typeof TEACHER_STATUS[keyof typeof TEACHER_STATUS];

// Attendance Status
export const ATTENDANCE_STATUS = {
  PRESENT: 'PRESENT',
  ABSENT: 'ABSENT',
  LATE: 'LATE',
  EXCUSED: 'EXCUSED',
} as const;

export type AttendanceStatus = typeof ATTENDANCE_STATUS[keyof typeof ATTENDANCE_STATUS];

// Fee Status
export const FEE_STATUS = {
  PENDING: 'PENDING',
  PAID: 'PAID',
  OVERDUE: 'OVERDUE',
  CANCELLED: 'CANCELLED',
  PARTIAL: 'PARTIAL',
} as const;

export type FeeStatus = typeof FEE_STATUS[keyof typeof FEE_STATUS];

// Exam Types
export const EXAM_TYPES = {
  MIDTERM: 'MIDTERM',
  FINAL: 'FINAL',
  QUIZ: 'QUIZ',
  ASSIGNMENT: 'ASSIGNMENT',
  PROJECT: 'PROJECT',
} as const;

export type ExamType = typeof EXAM_TYPES[keyof typeof EXAM_TYPES];

// Grade Levels
export const GRADE_LEVELS = [
  'Pre-K',
  'Kindergarten',
  '1st Grade',
  '2nd Grade',
  '3rd Grade',
  '4th Grade',
  '5th Grade',
  '6th Grade',
  '7th Grade',
  '8th Grade',
  '9th Grade',
  '10th Grade',
  '11th Grade',
  '12th Grade',
] as const;

export type GradeLevel = typeof GRADE_LEVELS[number];

// Subject Categories
export const SUBJECT_CATEGORIES = {
  MATHEMATICS: 'MATHEMATICS',
  SCIENCE: 'SCIENCE',
  ENGLISH: 'ENGLISH',
  SOCIAL_STUDIES: 'SOCIAL_STUDIES',
  ARTS: 'ARTS',
  PHYSICAL_EDUCATION: 'PHYSICAL_EDUCATION',
  TECHNOLOGY: 'TECHNOLOGY',
  FOREIGN_LANGUAGE: 'FOREIGN_LANGUAGE',
  OTHER: 'OTHER',
} as const;

export type SubjectCategory = typeof SUBJECT_CATEGORIES[keyof typeof SUBJECT_CATEGORIES];

// Days of the Week
export const DAYS_OF_WEEK = [
  'MONDAY',
  'TUESDAY',
  'WEDNESDAY',
  'THURSDAY',
  'FRIDAY',
  'SATURDAY',
  'SUNDAY',
] as const;

export type DayOfWeek = typeof DAYS_OF_WEEK[number];

// Pagination
export const PAGINATION = {
  DEFAULT_PAGE_SIZE: 10,
  PAGE_SIZE_OPTIONS: [10, 25, 50, 100],
  MAX_PAGE_SIZE: 100,
} as const;

// Local Storage Keys
export const STORAGE_KEYS = {
  AUTH_TOKEN: 'auth_token',
  REFRESH_TOKEN: 'refresh_token',
  USER_DATA: 'user_data',
  THEME: 'theme',
  SIDEBAR_COLLAPSED: 'sidebar_collapsed',
} as const;

// Error Messages
export const ERROR_MESSAGES = {
  NETWORK_ERROR: 'Network error. Please check your connection.',
  UNAUTHORIZED: 'You are not authorized to perform this action.',
  FORBIDDEN: 'Access denied.',
  NOT_FOUND: 'The requested resource was not found.',
  SERVER_ERROR: 'Internal server error. Please try again later.',
  VALIDATION_ERROR: 'Please check your input and try again.',
  UNKNOWN_ERROR: 'An unexpected error occurred.',
} as const;

// Success Messages
export const SUCCESS_MESSAGES = {
  CREATED: 'Created successfully!',
  UPDATED: 'Updated successfully!',
  DELETED: 'Deleted successfully!',
  SAVED: 'Saved successfully!',
  SENT: 'Sent successfully!',
} as const;

// Colors for Charts and UI
export const COLORS = {
  PRIMARY: '#3B82F6',
  SECONDARY: '#10B981',
  ACCENT: '#F59E0B',
  DANGER: '#EF4444',
  WARNING: '#F59E0B',
  SUCCESS: '#10B981',
  INFO: '#3B82F6',
  CHART_COLORS: [
    '#3B82F6', // Blue
    '#10B981', // Green
    '#F59E0B', // Yellow
    '#EF4444', // Red
    '#8B5CF6', // Purple
    '#F97316', // Orange
    '#06B6D4', // Cyan
    '#84CC16', // Lime
  ],
} as const;

// TODO: Add more constants as needed
// Example: NOTIFICATION_TYPES, PERMISSION_LEVELS, etc.
