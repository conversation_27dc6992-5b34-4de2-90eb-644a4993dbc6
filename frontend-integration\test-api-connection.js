/**
 * Test API Connection to FastAPI Backend
 *
 * This script tests the connection to the FastAPI backend
 * to ensure the students module integration works correctly.
 */

const API_BASE_URL = 'http://localhost:8000/api/v1';
const API_BASE_URL_ALT = 'http://127.0.0.1:8000/api/v1';

async function testApiConnection() {
  console.log('🧪 Testing API Connection to FastAPI Backend...\n');

  // Test both localhost and 127.0.0.1 addresses
  const urls = [API_BASE_URL, API_BASE_URL_ALT];

  for (const baseUrl of urls) {
    console.log(`\n🔗 Testing with: ${baseUrl}`);

    try {
      // Test 1: Check if backend is running
      console.log('1️⃣ Testing backend availability...');
      const healthResponse = await fetch(`${baseUrl.replace('/api/v1', '')}/health`);
      if (healthResponse.ok) {
        console.log('✅ Backend is running and accessible');
      } else {
        console.log('⚠️ Backend responded but with status:', healthResponse.status);
      }
    } catch (error) {
      console.log('❌ Backend is not accessible:', error.message);
      continue; // Try next URL
    }

    try {
      // Test 2: Test students endpoint (without auth for now)
      console.log('2️⃣ Testing students endpoint...');
      const studentsResponse = await fetch(`${baseUrl}/students`);
      if (studentsResponse.ok) {
        const studentsData = await studentsResponse.json();
        console.log('✅ Students endpoint accessible');
        console.log('📊 Response structure:', Object.keys(studentsData));
        if (studentsData.items) {
          console.log(`📚 Found ${studentsData.items.length} students`);
        }
      } else {
        console.log('⚠️ Students endpoint responded with status:', studentsResponse.status);
        const errorData = await studentsResponse.text();
        console.log('📝 Error details:', errorData);
      }
    } catch (error) {
      console.log('❌ Students endpoint error:', error.message);
    }

    try {
      // Test 3: Test student creation (this will fail without auth, but we can see the error)
      console.log('3️⃣ Testing student creation endpoint...');
      const testStudentData = {
        admission_number: 'TEST001',
        name: 'Test',
        surname: 'Student',
        sex: 'male',
        class_id: 1,
        grade_id: 1,
        password: 'TestPassword123',
        parent_id: 'test-parent-id',
      };

      const createResponse = await fetch(`${baseUrl}/students/`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(testStudentData),
      });

      if (createResponse.ok) {
        console.log('✅ Student creation endpoint accessible');
      } else {
        console.log('⚠️ Student creation endpoint responded with status:', createResponse.status);
        const errorData = await createResponse.text();
        console.log('📝 Error details:', errorData);
      }
    } catch (error) {
      console.log('❌ Student creation endpoint error:', error.message);
    }

    // If we successfully connected to one URL, break
    break;
  }

  console.log('\n🎯 API Connection Test Complete!');
  console.log('\n📋 Next Steps:');
  console.log(
    '1. Ensure FastAPI backend is running on http://localhost:8000 or http://127.0.0.1:8000'
  );
  console.log('2. Check if authentication is required for students endpoints');
  console.log('3. Verify the API response structure matches frontend expectations');
  console.log('4. Restart Next.js dev server after CSP changes');
}

// Run the test
testApiConnection().catch(console.error);
