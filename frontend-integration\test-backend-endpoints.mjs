/**
 * Test Available Backend Endpoints
 * 
 * Discovers what endpoints are available on the FastAPI backend
 */

const API_URL = 'http://127.0.0.1:8000';

async function testEndpoint(path, method = 'GET', body = null) {
  try {
    const options = {
      method,
      headers: { 'Content-Type': 'application/json' }
    };
    
    if (body) {
      options.body = JSON.stringify(body);
    }
    
    const response = await fetch(`${API_URL}${path}`, options);
    
    let responseData;
    try {
      responseData = await response.json();
    } catch {
      responseData = await response.text();
    }
    
    return {
      status: response.status,
      statusText: response.statusText,
      data: responseData
    };
  } catch (error) {
    return {
      status: 'ERROR',
      statusText: error.message,
      data: null
    };
  }
}

async function discoverEndpoints() {
  console.log('🔍 Discovering available FastAPI endpoints...\n');
  
  const endpoints = [
    // Root endpoints
    { path: '/', name: 'Root' },
    { path: '/docs', name: 'Swagger Doc<PERSON>' },
    { path: '/openapi.json', name: 'OpenAPI Schema' },
    
    // Health endpoints
    { path: '/health', name: 'Health (no prefix)' },
    { path: '/api/health', name: 'Health (api prefix)' },
    { path: '/api/v1/health', name: 'Health (full prefix)' },
    
    // Teachers endpoints
    { path: '/teachers', name: 'Teachers (no prefix)' },
    { path: '/teachers/', name: 'Teachers (no prefix, trailing slash)' },
    { path: '/api/teachers', name: 'Teachers (api prefix)' },
    { path: '/api/teachers/', name: 'Teachers (api prefix, trailing slash)' },
    { path: '/api/v1/teachers', name: 'Teachers (full prefix)' },
    { path: '/api/v1/teachers/', name: 'Teachers (full prefix, trailing slash)' },
    
    // Auth endpoints
    { path: '/auth/login', name: 'Auth Login (no prefix)' },
    { path: '/api/auth/login', name: 'Auth Login (api prefix)' },
    { path: '/api/v1/auth/login', name: 'Auth Login (full prefix)' },
  ];
  
  const results = [];
  
  for (const endpoint of endpoints) {
    console.log(`Testing ${endpoint.name}: ${endpoint.path}`);
    const result = await testEndpoint(endpoint.path);
    
    const status = result.status;
    const icon = status === 200 ? '✅' : 
                 status === 401 || status === 422 ? '🔐' :
                 status === 404 ? '❌' : 
                 status === 500 ? '💥' : '⚠️';
    
    console.log(`  ${icon} ${status} ${result.statusText}`);
    
    if (status === 200 || status === 401 || status === 422) {
      results.push({
        ...endpoint,
        status,
        working: true,
        data: result.data
      });
    }
    
    // Small delay to avoid overwhelming the server
    await new Promise(resolve => setTimeout(resolve, 100));
  }
  
  console.log('\n📋 Summary of Working Endpoints:');
  
  if (results.length === 0) {
    console.log('❌ No working endpoints found');
    console.log('\n🔧 Troubleshooting:');
    console.log('1. Is your FastAPI server running?');
    console.log('2. Check the server logs for errors');
    console.log('3. Verify the server is listening on 127.0.0.1:8000');
    console.log('4. Try accessing http://127.0.0.1:8000/docs in your browser');
  } else {
    results.forEach(endpoint => {
      console.log(`✅ ${endpoint.name}: ${endpoint.path} (${endpoint.status})`);
    });
    
    console.log('\n🎯 Recommendations:');
    console.log('1. Add missing health endpoint: /api/v1/health');
    console.log('2. Add missing teachers endpoint: /api/v1/teachers/');
    console.log('3. Check your FastAPI main.py file');
    console.log('4. Restart your FastAPI server after adding endpoints');
  }
  
  // Test CORS
  console.log('\n🌐 Testing CORS...');
  try {
    const corsTest = await fetch(`${API_URL}/api/v1/auth/login`, {
      method: 'OPTIONS',
      headers: {
        'Origin': 'http://localhost:3000',
        'Access-Control-Request-Method': 'POST',
        'Access-Control-Request-Headers': 'Content-Type'
      }
    });
    
    if (corsTest.ok) {
      console.log('✅ CORS appears to be configured');
    } else {
      console.log('⚠️ CORS may not be configured properly');
    }
  } catch (error) {
    console.log('❌ CORS test failed:', error.message);
  }
}

// Run the discovery
discoverEndpoints();
