'use client';

import { zodResolver } from '@hookform/resolvers/zod';
import { Loader2, Save, X } from 'lucide-react';
import { useForm } from 'react-hook-form';

import {
  ClassCreateSchema,
  ClassUpdateSchema,
  type ClassCreate,
  type ClassUpdate,
} from '@/schemas/zodSchemas';

import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';

interface ClassFormProps {
  mode: 'create' | 'edit';
  initialData?: ClassUpdate;
  onSubmit: (data: ClassCreate | ClassUpdate) => Promise<void>;
  onCancel: () => void;
  isLoading?: boolean;
  teachers?: Array<{ id: string; name: string }>;
}

const STATUS_OPTIONS = [
  { value: 'ACTIVE', label: 'Active' },
  { value: 'INACTIVE', label: 'Inactive' },
] as const;

export function ClassForm({
  mode,
  initialData,
  onSubmit,
  onCancel,
  isLoading = false,
  teachers = [],
}: ClassFormProps) {
  const schema = mode === 'create' ? ClassCreateSchema : ClassUpdateSchema;

  const form = useForm<ClassCreate | ClassUpdate>({
    resolver: zodResolver(schema),
    defaultValues:
      mode === 'create'
        ? {
            name: '',
            grade: '',
            section: '',
            capacity: 30,
            teacher_id: '',
            room: '',
            schedule: '',
            academic_year: new Date().getFullYear().toString(),
            status: 'ACTIVE',
          }
        : initialData || {},
    mode: 'onChange',
  });

  const handleSubmit = async (data: ClassCreate | ClassUpdate) => {
    try {
      // Transform empty strings to null for proper API handling
      const transformedData = {
        ...data,
        room: data.room === '' ? null : data.room,
        schedule: data.schedule === '' ? null : data.schedule,
        class_monitor: data.class_monitor === '' ? null : data.class_monitor,
      };

      const validatedData = schema.parse(transformedData);
      await onSubmit(validatedData);
    } catch (error) {
      console.error('Form validation error:', error);
    }
  };

  return (
    <Card className='w-full max-w-2xl mx-auto'>
      <CardHeader>
        <CardTitle>{mode === 'create' ? 'Add New Class' : 'Edit Class'}</CardTitle>
        <CardDescription>
          {mode === 'create'
            ? 'Fill in the details to add a new class to the system.'
            : 'Update the class information below.'}
        </CardDescription>
      </CardHeader>

      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(handleSubmit)} className='space-y-6'>
            <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
              {/* Name Field */}
              <FormField
                control={form.control}
                name='name'
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Class Name *</FormLabel>
                    <FormControl>
                      <Input placeholder='Enter class name' {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Grade Field */}
              <FormField
                control={form.control}
                name='grade'
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Grade *</FormLabel>
                    <FormControl>
                      <Input placeholder='Enter grade' {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Section Field */}
              <FormField
                control={form.control}
                name='section'
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Section *</FormLabel>
                    <FormControl>
                      <Input placeholder='Enter section' {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Capacity Field */}
              <FormField
                control={form.control}
                name='capacity'
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Capacity *</FormLabel>
                    <FormControl>
                      <Input
                        type='number'
                        placeholder='Enter capacity'
                        {...field}
                        onChange={e => field.onChange(parseInt(e.target.value) || 0)}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Teacher Field */}
              <FormField
                control={form.control}
                name='teacher_id'
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Teacher *</FormLabel>
                    <Select onValueChange={field.onChange} value={field.value || ''}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder='Select teacher' />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {teachers.map(teacher => (
                          <SelectItem key={teacher.id} value={teacher.id}>
                            {teacher.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Room Field */}
              <FormField
                control={form.control}
                name='room'
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Room</FormLabel>
                    <FormControl>
                      <Input placeholder='Enter room number' {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Schedule Field */}
              <FormField
                control={form.control}
                name='schedule'
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Schedule</FormLabel>
                    <FormControl>
                      <Input placeholder='Enter schedule' {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Academic Year Field */}
              <FormField
                control={form.control}
                name='academic_year'
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Academic Year *</FormLabel>
                    <FormControl>
                      <Input placeholder='Enter academic year' {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Status Field */}
              <FormField
                control={form.control}
                name='status'
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Status</FormLabel>
                    <Select onValueChange={field.onChange} value={field.value || 'ACTIVE'}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder='Select status' />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {STATUS_OPTIONS.map(option => (
                          <SelectItem key={option.value} value={option.value}>
                            {option.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* Form Actions */}
            <div className='flex justify-end space-x-4 pt-6 border-t'>
              <Button type='button' variant='outline' onClick={onCancel} disabled={isLoading}>
                <X className='w-4 h-4 mr-2' />
                Cancel
              </Button>

              <Button type='submit' disabled={isLoading || !form.formState.isValid}>
                {isLoading ? (
                  <Loader2 className='w-4 h-4 mr-2 animate-spin' />
                ) : (
                  <Save className='w-4 h-4 mr-2' />
                )}
                {mode === 'create' ? 'Create Class' : 'Update Class'}
              </Button>
            </div>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
}

export default ClassForm;
