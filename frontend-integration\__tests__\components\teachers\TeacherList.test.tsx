import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import TeachersPage from '@/app/(dashboard)/teachers/page'
import { useQueryBase } from '@/hooks/useQueryBase'
import { mockTeachers } from '@/lib/mockTeachers'

// Mock the useQueryBase hook
jest.mock('@/hooks/useQueryBase')
const mockUseQueryBase = useQueryBase as jest.MockedFunction<typeof useQueryBase>

// Mock the API client
jest.mock('@/api/apiService', () => ({
  apiClient: {
    get: jest.fn(),
    post: jest.fn(),
    put: jest.fn(),
    delete: jest.fn(),
  },
}))

// Mock toast notifications
jest.mock('sonner', () => ({
  toast: {
    success: jest.fn(),
    error: jest.fn(),
  },
}))

// Test wrapper with providers
const TestWrapper = ({ children }: { children: React.ReactNode }) => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false },
    },
  })

  return (
    <QueryClientProvider client={queryClient}>
      {children}
    </QueryClientProvider>
  )
}

describe('TeacherList', () => {
  beforeEach(() => {
    // Mock successful data fetch
    mockUseQueryBase.mockReturnValue({
      data: mockTeachers.slice(0, 5), // Return first 5 teachers
      isLoading: false,
      error: null,
      refetch: jest.fn(),
      isRefetching: false,
      isSuccess: true,
      isError: false,
      isPending: false,
      isFetching: false,
      status: 'success' as const,
      dataUpdatedAt: Date.now(),
      errorUpdatedAt: 0,
      failureCount: 0,
      failureReason: null,
      fetchStatus: 'idle' as const,
      isInitialLoading: false,
      isLoadingError: false,
      isPaused: false,
      isPlaceholderData: false,
      isPreviousData: false,
      isRefetchError: false,
      isStale: false,
    })
  })

  afterEach(() => {
    jest.clearAllMocks()
  })

  it('renders teacher list with data', async () => {
    render(
      <TestWrapper>
        <TeachersPage />
      </TestWrapper>
    )

    // Wait for data to load
    await waitFor(() => {
      expect(screen.getByText(/teachers/i)).toBeInTheDocument()
    })

    // Check if teacher names are displayed
    expect(screen.getByText(/sarah johnson/i)).toBeInTheDocument()
    expect(screen.getByText(/michael chen/i)).toBeInTheDocument()
  })

  it('displays loading state initially', () => {
    // Mock loading state
    mockUseQueryBase.mockReturnValue({
      data: undefined,
      isLoading: true,
      error: null,
      refetch: jest.fn(),
      isRefetching: false,
      isSuccess: false,
      isError: false,
      isPending: true,
      isFetching: true,
      status: 'pending' as const,
      dataUpdatedAt: 0,
      errorUpdatedAt: 0,
      failureCount: 0,
      failureReason: null,
      fetchStatus: 'fetching' as const,
      isInitialLoading: true,
      isLoadingError: false,
      isPaused: false,
      isPlaceholderData: false,
      isPreviousData: false,
      isRefetchError: false,
      isStale: false,
    })

    render(
      <TestWrapper>
        <TeachersPage />
      </TestWrapper>
    )

    // Check for loading skeletons
    expect(screen.getByTestId('teachers-loading')).toBeInTheDocument()
  })

  it('displays error state when data fetch fails', () => {
    // Mock error state
    mockUseQueryBase.mockReturnValue({
      data: undefined,
      isLoading: false,
      error: new Error('Failed to fetch teachers'),
      refetch: jest.fn(),
      isRefetching: false,
      isSuccess: false,
      isError: true,
      isPending: false,
      isFetching: false,
      status: 'error' as const,
      dataUpdatedAt: 0,
      errorUpdatedAt: Date.now(),
      failureCount: 1,
      failureReason: new Error('Failed to fetch teachers'),
      fetchStatus: 'idle' as const,
      isInitialLoading: false,
      isLoadingError: true,
      isPaused: false,
      isPlaceholderData: false,
      isPreviousData: false,
      isRefetchError: false,
      isStale: false,
    })

    render(
      <TestWrapper>
        <TeachersPage />
      </TestWrapper>
    )

    // Check for error message
    expect(screen.getByText(/failed to fetch teachers/i)).toBeInTheDocument()
    expect(screen.getByRole('button', { name: /try again/i })).toBeInTheDocument()
  })

  it('filters teachers by search term', async () => {
    const user = userEvent.setup()
    
    render(
      <TestWrapper>
        <TeachersPage />
      </TestWrapper>
    )

    // Wait for initial render
    await waitFor(() => {
      expect(screen.getByText(/sarah johnson/i)).toBeInTheDocument()
    })

    // Find and use search input
    const searchInput = screen.getByPlaceholderText(/search teachers/i)
    await user.type(searchInput, 'sarah')

    // Check that only Sarah Johnson is visible
    await waitFor(() => {
      expect(screen.getByText(/sarah johnson/i)).toBeInTheDocument()
      expect(screen.queryByText(/michael chen/i)).not.toBeInTheDocument()
    })
  })

  it('filters teachers by department', async () => {
    const user = userEvent.setup()
    
    render(
      <TestWrapper>
        <TeachersPage />
      </TestWrapper>
    )

    // Wait for initial render
    await waitFor(() => {
      expect(screen.getByText(/teachers/i)).toBeInTheDocument()
    })

    // Find and use department filter
    const departmentFilter = screen.getByRole('combobox', { name: /department/i })
    await user.click(departmentFilter)
    
    // Select Mathematics department
    const mathOption = screen.getByRole('option', { name: /mathematics/i })
    await user.click(mathOption)

    // Check that only math teachers are visible
    await waitFor(() => {
      // Verify filtering logic (this would depend on your mock data)
      expect(screen.getByTestId('filtered-teachers')).toBeInTheDocument()
    })
  })

  it('filters teachers by status', async () => {
    const user = userEvent.setup()
    
    render(
      <TestWrapper>
        <TeachersPage />
      </TestWrapper>
    )

    // Wait for initial render
    await waitFor(() => {
      expect(screen.getByText(/teachers/i)).toBeInTheDocument()
    })

    // Find and use status filter
    const statusFilter = screen.getByRole('combobox', { name: /status/i })
    await user.click(statusFilter)
    
    // Select Active status
    const activeOption = screen.getByRole('option', { name: /active/i })
    await user.click(activeOption)

    // Check that only active teachers are visible
    await waitFor(() => {
      expect(screen.getByTestId('filtered-teachers')).toBeInTheDocument()
    })
  })

  it('displays teacher statistics correctly', async () => {
    render(
      <TestWrapper>
        <TeachersPage />
      </TestWrapper>
    )

    // Wait for stats to load
    await waitFor(() => {
      expect(screen.getByText(/total teachers/i)).toBeInTheDocument()
    })

    // Check for stat cards
    expect(screen.getByText(/active teachers/i)).toBeInTheDocument()
    expect(screen.getByText(/departments/i)).toBeInTheDocument()
  })

  it('handles pagination correctly', async () => {
    const user = userEvent.setup()
    
    // Mock more teachers for pagination
    mockUseQueryBase.mockReturnValue({
      data: mockTeachers, // Return all teachers
      isLoading: false,
      error: null,
      refetch: jest.fn(),
      isRefetching: false,
      isSuccess: true,
      isError: false,
      isPending: false,
      isFetching: false,
      status: 'success' as const,
      dataUpdatedAt: Date.now(),
      errorUpdatedAt: 0,
      failureCount: 0,
      failureReason: null,
      fetchStatus: 'idle' as const,
      isInitialLoading: false,
      isLoadingError: false,
      isPaused: false,
      isPlaceholderData: false,
      isPreviousData: false,
      isRefetchError: false,
      isStale: false,
    })

    render(
      <TestWrapper>
        <TeachersPage />
      </TestWrapper>
    )

    // Wait for initial render
    await waitFor(() => {
      expect(screen.getByText(/teachers/i)).toBeInTheDocument()
    })

    // Check for pagination controls
    const nextButton = screen.getByRole('button', { name: /next/i })
    expect(nextButton).toBeInTheDocument()

    // Click next page
    await user.click(nextButton)

    // Verify pagination state change
    await waitFor(() => {
      expect(screen.getByText(/page 2/i)).toBeInTheDocument()
    })
  })

  it('opens add teacher dialog', async () => {
    const user = userEvent.setup()
    
    render(
      <TestWrapper>
        <TeachersPage />
      </TestWrapper>
    )

    // Find and click add teacher button
    const addButton = screen.getByRole('button', { name: /add teacher/i })
    await user.click(addButton)

    // Check that dialog opens
    await waitFor(() => {
      expect(screen.getByRole('dialog')).toBeInTheDocument()
      expect(screen.getByText(/add new teacher/i)).toBeInTheDocument()
    })
  })

  it('handles teacher card actions', async () => {
    const user = userEvent.setup()
    
    render(
      <TestWrapper>
        <TeachersPage />
      </TestWrapper>
    )

    // Wait for teachers to load
    await waitFor(() => {
      expect(screen.getByText(/sarah johnson/i)).toBeInTheDocument()
    })

    // Find teacher card actions
    const teacherCard = screen.getByTestId('teacher-card-teacher_001')
    const editButton = screen.getByRole('button', { name: /edit/i })
    
    // Click edit button
    await user.click(editButton)

    // Verify edit action (this would depend on your implementation)
    await waitFor(() => {
      expect(screen.getByRole('dialog')).toBeInTheDocument()
    })
  })

  it('toggles between list and grid view', async () => {
    const user = userEvent.setup()
    
    render(
      <TestWrapper>
        <TeachersPage />
      </TestWrapper>
    )

    // Wait for initial render
    await waitFor(() => {
      expect(screen.getByText(/teachers/i)).toBeInTheDocument()
    })

    // Find view toggle buttons
    const gridViewButton = screen.getByRole('button', { name: /grid view/i })
    const listViewButton = screen.getByRole('button', { name: /list view/i })

    // Toggle to grid view
    await user.click(gridViewButton)
    
    // Check that view changed
    await waitFor(() => {
      expect(screen.getByTestId('teachers-grid-view')).toBeInTheDocument()
    })

    // Toggle back to list view
    await user.click(listViewButton)
    
    // Check that view changed back
    await waitFor(() => {
      expect(screen.getByTestId('teachers-list-view')).toBeInTheDocument()
    })
  })

  it('exports teacher data', async () => {
    const user = userEvent.setup()
    
    render(
      <TestWrapper>
        <TeachersPage />
      </TestWrapper>
    )

    // Wait for initial render
    await waitFor(() => {
      expect(screen.getByText(/teachers/i)).toBeInTheDocument()
    })

    // Find and click export button
    const exportButton = screen.getByRole('button', { name: /export/i })
    await user.click(exportButton)

    // Verify export functionality (this would depend on your implementation)
    // For now, just check that the button is clickable
    expect(exportButton).toBeInTheDocument()
  })
})
