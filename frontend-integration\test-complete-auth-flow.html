<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Complete Auth Flow Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .success { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background-color: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .warning { background-color: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background-color: #0056b3; }
        button:disabled { background-color: #6c757d; cursor: not-allowed; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 4px; overflow-x: auto; font-size: 12px; }
        .step { margin: 20px 0; padding: 15px; border-left: 4px solid #007bff; background: #f8f9fa; }
        .step h3 { margin-top: 0; color: #007bff; }
    </style>
</head>
<body>
    <h1>🔐 Complete Teachers Auth & Create Flow Test</h1>
    
    <div class="info">
        <strong>Test Credentials:</strong> admin / admin123<br>
        <strong>Expected Role:</strong> SUPER_ADMIN<br>
        <strong>Test Flow:</strong> Login → Verify Token → Test Teachers Module → Create Teacher
    </div>

    <div class="step">
        <h3>Step 1: Authentication Status</h3>
        <div id="auth-status"></div>
        <button onclick="checkAuthStatus()">Check Auth Status</button>
        <button onclick="clearAuth()">Clear Auth Data</button>
    </div>

    <div class="step">
        <h3>Step 2: Login Test</h3>
        <div id="login-test"></div>
        <button onclick="testLogin()">Test Login (admin/admin123)</button>
    </div>

    <div class="step">
        <h3>Step 3: JWT Token Verification</h3>
        <div id="token-verification"></div>
        <button onclick="verifyToken()">Verify JWT Token</button>
    </div>

    <div class="step">
        <h3>Step 4: API Authentication Test</h3>
        <div id="api-auth-test"></div>
        <button onclick="testApiAuth()">Test API with Bearer Token</button>
    </div>

    <div class="step">
        <h3>Step 5: Teachers Module Access</h3>
        <div id="teachers-access"></div>
        <button onclick="testTeachersAccess()">Test Teachers Page Access</button>
    </div>

    <div class="step">
        <h3>Step 6: Teacher Creation Test</h3>
        <div id="teacher-creation"></div>
        <button onclick="testTeacherCreation()">Test Teacher Creation</button>
    </div>

    <div class="step">
        <h3>Step 7: Complete Flow Test</h3>
        <div id="complete-flow"></div>
        <button onclick="runCompleteFlow()" id="complete-flow-btn">Run Complete Flow Test</button>
    </div>

    <script>
        let testResults = {
            auth: false,
            login: false,
            token: false,
            api: false,
            teachers: false,
            create: false
        };

        function updateStatus(elementId, html) {
            document.getElementById(elementId).innerHTML = html;
        }

        function checkAuthStatus() {
            const token = localStorage.getItem('access_token');
            const role = localStorage.getItem('user_role');
            const cookies = document.cookie.split(';').map(c => c.trim());
            const authCookie = cookies.find(c => c.startsWith('access_token='));
            
            let html = '<h4>Authentication Status:</h4>';
            html += `<div class="${token ? 'success' : 'error'}">JWT Token: ${token ? '✅ Present' : '❌ Missing'}</div>`;
            html += `<div class="${role ? 'success' : 'error'}">User Role: ${role || '❌ Missing'}</div>`;
            html += `<div class="${authCookie ? 'success' : 'warning'}">Auth Cookie: ${authCookie ? '✅ Present' : '⚠️ Missing'}</div>`;
            
            if (token) {
                try {
                    const payload = JSON.parse(atob(token.split('.')[1]));
                    const isExpired = payload.exp && payload.exp < Date.now() / 1000;
                    html += `<div class="${isExpired ? 'error' : 'success'}">Token Status: ${isExpired ? '❌ Expired' : '✅ Valid'}</div>`;
                    html += `<div class="info">Token Role: ${payload.role || 'Not specified'}</div>`;
                } catch (e) {
                    html += '<div class="error">❌ Invalid token format</div>';
                }
            }
            
            testResults.auth = !!token && !!role;
            updateStatus('auth-status', html);
        }

        function clearAuth() {
            localStorage.removeItem('access_token');
            localStorage.removeItem('user_role');
            localStorage.removeItem('user_id');
            document.cookie = 'access_token=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;';
            updateStatus('auth-status', '<div class="info">✅ Authentication data cleared</div>');
            testResults.auth = false;
        }

        async function testLogin() {
            updateStatus('login-test', '<div class="info">🔄 Testing login...</div>');
            
            try {
                const response = await fetch('/api/auth/login', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        username: 'admin',
                        password: 'admin123'
                    })
                });
                
                let html = '<h4>Login Test Results:</h4>';
                html += `<div class="${response.ok ? 'success' : 'error'}">Status: ${response.status} ${response.statusText}</div>`;
                
                if (response.ok) {
                    const data = await response.json();
                    html += '<div class="success">✅ Login successful</div>';
                    html += `<pre>${JSON.stringify(data, null, 2)}</pre>`;
                    
                    // Store token (simulating what the app would do)
                    if (data.access_token) {
                        localStorage.setItem('access_token', data.access_token);
                        
                        // Decode and store role
                        try {
                            const payload = JSON.parse(atob(data.access_token.split('.')[1]));
                            if (payload.role) {
                                localStorage.setItem('user_role', payload.role);
                            }
                        } catch (e) {
                            console.warn('Failed to decode token:', e);
                        }
                        
                        testResults.login = true;
                        html += '<div class="success">✅ Token stored successfully</div>';
                    }
                } else {
                    const errorText = await response.text();
                    html += `<div class="error">❌ Login failed: ${errorText}</div>`;
                    testResults.login = false;
                }
                
                updateStatus('login-test', html);
            } catch (error) {
                updateStatus('login-test', `<div class="error">❌ Login test failed: ${error.message}</div>`);
                testResults.login = false;
            }
        }

        function verifyToken() {
            const token = localStorage.getItem('access_token');
            
            if (!token) {
                updateStatus('token-verification', '<div class="error">❌ No token found. Please login first.</div>');
                testResults.token = false;
                return;
            }
            
            try {
                const payload = JSON.parse(atob(token.split('.')[1]));
                const isExpired = payload.exp && payload.exp < Date.now() / 1000;
                const isAdmin = payload.role === 'ADMIN' || payload.role === 'SUPER_ADMIN';
                
                let html = '<h4>JWT Token Verification:</h4>';
                html += `<div class="${isExpired ? 'error' : 'success'}">Expiration: ${isExpired ? '❌ Expired' : '✅ Valid'}</div>`;
                html += `<div class="${isAdmin ? 'success' : 'error'}">Admin Role: ${isAdmin ? '✅ Yes' : '❌ No'}</div>`;
                html += `<div class="info">Role: ${payload.role || 'Not specified'}</div>`;
                html += `<div class="info">User ID: ${payload.sub || payload.user_id || 'Not specified'}</div>`;
                
                if (payload.exp) {
                    const expDate = new Date(payload.exp * 1000);
                    html += `<div class="info">Expires: ${expDate.toLocaleString()}</div>`;
                }
                
                html += '<h5>Full Payload:</h5>';
                html += `<pre>${JSON.stringify(payload, null, 2)}</pre>`;
                
                testResults.token = !isExpired && isAdmin;
                updateStatus('token-verification', html);
            } catch (error) {
                updateStatus('token-verification', `<div class="error">❌ Token verification failed: ${error.message}</div>`);
                testResults.token = false;
            }
        }

        async function testApiAuth() {
            const token = localStorage.getItem('access_token');
            
            if (!token) {
                updateStatus('api-auth-test', '<div class="error">❌ No token found. Please login first.</div>');
                testResults.api = false;
                return;
            }
            
            updateStatus('api-auth-test', '<div class="info">🔄 Testing API authentication...</div>');
            
            try {
                const response = await fetch('/api/v1/teachers', {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    }
                });
                
                let html = '<h4>API Authentication Test:</h4>';
                html += `<div class="${response.ok ? 'success' : 'error'}">Status: ${response.status} ${response.statusText}</div>`;
                
                if (response.ok) {
                    const data = await response.json();
                    html += '<div class="success">✅ API authentication successful</div>';
                    html += `<div class="info">Teachers found: ${Array.isArray(data) ? data.length : 'N/A'}</div>`;
                    testResults.api = true;
                } else {
                    const errorText = await response.text();
                    html += `<div class="error">❌ API authentication failed: ${errorText}</div>`;
                    testResults.api = false;
                }
                
                updateStatus('api-auth-test', html);
            } catch (error) {
                updateStatus('api-auth-test', `<div class="error">❌ API test failed: ${error.message}</div>`);
                testResults.api = false;
            }
        }

        async function testTeachersAccess() {
            updateStatus('teachers-access', '<div class="info">🔄 Testing teachers page access...</div>');
            
            try {
                const response = await fetch('/dashboard/teachers', {
                    method: 'GET',
                    credentials: 'include'
                });
                
                let html = '<h4>Teachers Page Access Test:</h4>';
                html += `<div class="${response.ok ? 'success' : 'error'}">Status: ${response.status} ${response.statusText}</div>`;
                
                if (response.ok) {
                    html += '<div class="success">✅ Teachers page accessible</div>';
                    html += '<div class="info">Page loaded successfully</div>';
                    testResults.teachers = true;
                } else if (response.status === 302 || response.status === 307) {
                    html += '<div class="warning">⚠️ Redirected (likely to login)</div>';
                    testResults.teachers = false;
                } else {
                    html += `<div class="error">❌ Access denied or error</div>`;
                    testResults.teachers = false;
                }
                
                updateStatus('teachers-access', html);
            } catch (error) {
                updateStatus('teachers-access', `<div class="error">❌ Teachers access test failed: ${error.message}</div>`);
                testResults.teachers = false;
            }
        }

        async function testTeacherCreation() {
            const token = localStorage.getItem('access_token');
            
            if (!token) {
                updateStatus('teacher-creation', '<div class="error">❌ No token found. Please login first.</div>');
                testResults.create = false;
                return;
            }
            
            updateStatus('teacher-creation', '<div class="info">🔄 Testing teacher creation...</div>');
            
            const testTeacher = {
                name: `Test Teacher ${Date.now()}`,
                subject: 'Test Subject',
                email: `test${Date.now()}@example.com`,
                department: 'Test Department',
                status: 'ACTIVE'
            };
            
            try {
                const response = await fetch('/api/v1/teachers', {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(testTeacher)
                });
                
                let html = '<h4>Teacher Creation Test:</h4>';
                html += `<div class="${response.ok ? 'success' : 'error'}">Status: ${response.status} ${response.statusText}</div>`;
                
                if (response.ok) {
                    const createdTeacher = await response.json();
                    html += '<div class="success">✅ Teacher created successfully</div>';
                    html += `<div class="info">Teacher ID: ${createdTeacher.id || 'N/A'}</div>`;
                    html += `<div class="info">Teacher Name: ${createdTeacher.name || 'N/A'}</div>`;
                    html += '<h5>Created Teacher:</h5>';
                    html += `<pre>${JSON.stringify(createdTeacher, null, 2)}</pre>`;
                    testResults.create = true;
                } else {
                    const errorText = await response.text();
                    html += `<div class="error">❌ Teacher creation failed: ${errorText}</div>`;
                    testResults.create = false;
                }
                
                updateStatus('teacher-creation', html);
            } catch (error) {
                updateStatus('teacher-creation', `<div class="error">❌ Teacher creation test failed: ${error.message}</div>`);
                testResults.create = false;
            }
        }

        async function runCompleteFlow() {
            const btn = document.getElementById('complete-flow-btn');
            btn.disabled = true;
            btn.textContent = 'Running Complete Flow...';
            
            updateStatus('complete-flow', '<div class="info">🔄 Running complete authentication and teachers flow test...</div>');
            
            // Clear previous results
            testResults = { auth: false, login: false, token: false, api: false, teachers: false, create: false };
            
            // Step 1: Clear auth data
            clearAuth();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            // Step 2: Test login
            await testLogin();
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            // Step 3: Verify token
            verifyToken();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            // Step 4: Test API auth
            await testApiAuth();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            // Step 5: Test teachers access
            await testTeachersAccess();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            // Step 6: Test teacher creation
            await testTeacherCreation();
            
            // Generate final report
            const totalTests = Object.keys(testResults).length;
            const passedTests = Object.values(testResults).filter(Boolean).length;
            const success = passedTests === totalTests;
            
            let html = '<h4>Complete Flow Test Results:</h4>';
            html += `<div class="${success ? 'success' : 'error'}">Overall Result: ${success ? '✅ ALL TESTS PASSED' : '❌ SOME TESTS FAILED'}</div>`;
            html += `<div class="info">Tests Passed: ${passedTests}/${totalTests}</div>`;
            
            html += '<h5>Individual Test Results:</h5>';
            html += `<div class="${testResults.auth ? 'success' : 'error'}">Auth Status: ${testResults.auth ? '✅' : '❌'}</div>`;
            html += `<div class="${testResults.login ? 'success' : 'error'}">Login: ${testResults.login ? '✅' : '❌'}</div>`;
            html += `<div class="${testResults.token ? 'success' : 'error'}">Token Verification: ${testResults.token ? '✅' : '❌'}</div>`;
            html += `<div class="${testResults.api ? 'success' : 'error'}">API Authentication: ${testResults.api ? '✅' : '❌'}</div>`;
            html += `<div class="${testResults.teachers ? 'success' : 'error'}">Teachers Access: ${testResults.teachers ? '✅' : '❌'}</div>`;
            html += `<div class="${testResults.create ? 'success' : 'error'}">Teacher Creation: ${testResults.create ? '✅' : '❌'}</div>`;
            
            if (success) {
                html += '<div class="success"><strong>🎉 Teachers Auth & Create Flow is working perfectly!</strong></div>';
            } else {
                html += '<div class="error"><strong>⚠️ Some issues found. Check individual test results above.</strong></div>';
            }
            
            updateStatus('complete-flow', html);
            
            btn.disabled = false;
            btn.textContent = 'Run Complete Flow Test';
        }

        // Auto-run auth status check on page load
        window.onload = function() {
            checkAuthStatus();
        };
    </script>
</body>
</html>
