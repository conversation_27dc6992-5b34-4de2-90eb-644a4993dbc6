'use client';

import { BookOpen, Calendar, Clock, GraduationCap, Users } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { useState } from 'react';

import { SuperAdminDebugger } from '@/components/debug/SuperAdminDebugger';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { ModulePageLayout } from '@/components/ui/module-page-layout';
import { useAuth } from '@/hooks/useAuth';
import { useClasses, useClassStats } from '@/hooks/useClasses';
import { canCreateClass, canEditClass, canViewClass } from '@/lib/permissions';

export default function ClassesPage() {
  const router = useRouter();
  const { user } = useAuth();
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('All');
  const [gradeFilter, setGradeFilter] = useState('All');

  const { data: classesData, isLoading } = useClasses({
    search: searchTerm || undefined,
    status: statusFilter !== 'All' ? (statusFilter as 'ACTIVE' | 'INACTIVE') : undefined,
    grade: gradeFilter !== 'All' ? gradeFilter : undefined,
  });

  const { data: stats } = useClassStats();

  const handleViewClass = (classId: string) => {
    if (canViewClass(user?.role)) {
      router.push(`/dashboard/classes/${classId}`);
    }
  };

  const handleEditClass = (classId: string) => {
    if (canEditClass(user?.role)) {
      router.push(`/dashboard/classes/${classId}/edit`);
    }
  };

  const statsCards = [
    {
      title: 'Total Classes',
      value: stats?.total?.toString() || '0',
      icon: BookOpen,
      color: 'text-blue-600',
      bgColor: 'bg-blue-50',
      change: '+2%',
      changeType: 'positive' as const,
    },
    {
      title: 'Active Classes',
      value: stats?.active?.toString() || '0',
      icon: GraduationCap,
      color: 'text-green-600',
      bgColor: 'bg-green-50',
      change: '+5%',
      changeType: 'positive' as const,
    },
    {
      title: 'Total Students',
      value: stats?.totalStudents?.toString() || '0',
      icon: Users,
      color: 'text-purple-600',
      bgColor: 'bg-purple-50',
      change: '+3%',
      changeType: 'positive' as const,
    },
    {
      title: 'Average Capacity',
      value: stats?.averageCapacity?.toString() || '0',
      icon: Users,
      color: 'text-orange-600',
      bgColor: 'bg-orange-50',
      change: '+1%',
      changeType: 'positive' as const,
    },
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'ACTIVE':
        return 'text-green-600 bg-green-50';
      case 'INACTIVE':
        return 'text-red-600 bg-red-50';
      default:
        return 'text-gray-600 bg-gray-50';
    }
  };

  const filters = [
    {
      label: 'Status',
      options: [
        { label: 'All Status', value: 'All' },
        { label: 'Active', value: 'ACTIVE' },
        { label: 'Inactive', value: 'INACTIVE' },
      ],
      value: statusFilter,
      onChange: setStatusFilter,
    },
    {
      label: 'Grade',
      options: [
        { label: 'All Grades', value: 'All' },
        { label: 'Grade 1', value: '1' },
        { label: 'Grade 2', value: '2' },
        { label: 'Grade 3', value: '3' },
        { label: 'Grade 4', value: '4' },
        { label: 'Grade 5', value: '5' },
      ],
      value: gradeFilter,
      onChange: setGradeFilter,
    },
  ];

  return (
    <>
      <SuperAdminDebugger />
      <ModulePageLayout
        title='Class Management'
        description='Manage class schedules, subjects, and assignments'
        icon={BookOpen}
        statsCards={statsCards}
        searchPlaceholder='Search classes...'
        filters={filters}
        createRoute={canCreateClass(user?.role) ? '/dashboard/classes/create' : undefined}
        createLabel='Create Class'
        searchValue={searchTerm}
        onSearchChange={setSearchTerm}
        totalItems={classesData?.total || 0}
        filteredItems={classesData?.data?.length || 0}
        isLoading={isLoading}
      >
        <div className='grid grid-cols-1 gap-4'>
          {classesData?.data?.map(classItem => (
            <Card key={classItem.id} className='hover:shadow-md transition-shadow'>
              <CardContent className='p-6'>
                <div className='flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4'>
                  <div className='flex items-start space-x-4'>
                    <div className='w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center'>
                      <BookOpen className='w-6 h-6 text-blue-600' />
                    </div>
                    <div className='flex-1'>
                      <div className='flex items-center gap-2 mb-2'>
                        <h3 className='font-semibold'>{classItem.name}</h3>
                        <Badge variant='outline' className={getStatusColor(classItem.status)}>
                          {classItem.status}
                        </Badge>
                      </div>
                      <p className='text-sm text-muted-foreground mb-2'>
                        Teacher: {classItem.teacher_name || 'Not assigned'}
                      </p>
                      <div className='flex flex-wrap gap-4 text-sm text-muted-foreground'>
                        <div className='flex items-center'>
                          <Calendar className='w-4 h-4 mr-1' />
                          {classItem.schedule || 'No schedule'}
                        </div>
                        <div className='flex items-center'>
                          <Users className='w-4 h-4 mr-1' />
                          {classItem.enrolled || 0}/{classItem.capacity} students
                        </div>
                        <div className='flex items-center'>
                          <Clock className='w-4 h-4 mr-1' />
                          {classItem.room || 'No room assigned'}
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className='flex flex-col sm:flex-row items-start sm:items-center gap-4'>
                    <div className='text-center sm:text-right'>
                      <p className='font-semibold'>Grade {classItem.grade}</p>
                      <p className='text-sm text-muted-foreground'>{classItem.academic_year}</p>
                    </div>

                    <div className='flex space-x-2'>
                      {canViewClass(user?.role) && (
                        <Button
                          variant='outline'
                          size='sm'
                          onClick={() => handleViewClass(classItem.id)}
                        >
                          View
                        </Button>
                      )}
                      {canEditClass(user?.role) && (
                        <Button
                          variant='outline'
                          size='sm'
                          onClick={() => handleEditClass(classItem.id)}
                        >
                          Edit
                        </Button>
                      )}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {(!classesData?.data || classesData.data.length === 0) && !isLoading && (
          <Card>
            <CardContent className='flex flex-col items-center justify-center py-16'>
              <div className='text-6xl mb-4'>📚</div>
              <h3 className='text-lg font-semibold mb-2'>No Classes Found</h3>
              <p className='text-muted-foreground text-center mb-4'>
                {searchTerm || statusFilter !== 'All' || gradeFilter !== 'All'
                  ? 'No classes match your current filters.'
                  : 'There are no classes created yet.'}
              </p>
              {canCreateClass(user?.role) && (
                <Button onClick={() => router.push('/dashboard/classes/create')}>
                  <BookOpen className='mr-2 h-4 w-4' />
                  Create First Class
                </Button>
              )}
            </CardContent>
          </Card>
        )}
      </ModulePageLayout>
    </>
  );
}
