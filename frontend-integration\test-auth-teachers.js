/**
 * Test Auth Flow for Teachers CRUD
 *
 * This script tests the complete auth flow:
 * 1. <PERSON><PERSON> with credentials
 * 2. Store JWT token
 * 3. Test teachers CRUD operations
 */

const API_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000/api/v1';

// Simple auth helper (matches lib/auth.ts)
async function login(username, password) {
  console.log('🔐 Testing login...');

  const response = await fetch(`${API_URL}/auth/login`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ username, password }),
  });

  if (!response.ok) {
    throw new Error(`Login failed: ${response.status} ${response.statusText}`);
  }

  const data = await response.json();
  console.log('✅ Login successful, token received');

  return data;
}

function getAuthHeader(token) {
  return token ? { Authorization: `Bearer ${token}` } : {};
}

// Test teachers CRUD operations
async function testTeachersCRUD(token) {
  console.log('📚 Testing Teachers CRUD operations...');

  // Test GET /teachers
  console.log('1. Testing GET /teachers');
  const teachersResponse = await fetch(`${API_URL}/teachers`, {
    headers: getAuthHeader(token),
  });

  if (!teachersResponse.ok) {
    throw new Error(`GET /teachers failed: ${teachersResponse.status}`);
  }

  const teachers = await teachersResponse.json();
  console.log(
    `✅ GET /teachers successful, found ${
      Array.isArray(teachers) ? teachers.length : 'unknown'
    } teachers`
  );

  // Test POST /teachers (create)
  console.log('2. Testing POST /teachers (create)');
  const newTeacher = {
    name: 'Test Teacher',
    email: '<EMAIL>',
    subject: 'Mathematics',
    department: 'Science',
    status: 'ACTIVE',
  };

  const createResponse = await fetch(`${API_URL}/teachers`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      ...getAuthHeader(token),
    },
    body: JSON.stringify(newTeacher),
  });

  if (!createResponse.ok) {
    console.warn(
      `⚠️ POST /teachers failed: ${createResponse.status} (this might be expected if you don't have admin permissions)`
    );
    return;
  }

  const createdTeacher = await createResponse.json();
  console.log('✅ POST /teachers successful, teacher created:', createdTeacher.id);

  // Test GET /teachers/:id
  console.log('3. Testing GET /teachers/:id');
  const teacherResponse = await fetch(`${API_URL}/teachers/${createdTeacher.id}`, {
    headers: getAuthHeader(token),
  });

  if (teacherResponse.ok) {
    console.log('✅ GET /teachers/:id successful');
  } else {
    console.warn(`⚠️ GET /teachers/:id failed: ${teacherResponse.status}`);
  }

  // Test PUT /teachers/:id (update)
  console.log('4. Testing PUT /teachers/:id (update)');
  const updateData = { ...newTeacher, name: 'Updated Test Teacher' };
  const updateResponse = await fetch(`${API_URL}/teachers/${createdTeacher.id}`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
      ...getAuthHeader(token),
    },
    body: JSON.stringify(updateData),
  });

  if (updateResponse.ok) {
    console.log('✅ PUT /teachers/:id successful');
  } else {
    console.warn(`⚠️ PUT /teachers/:id failed: ${updateResponse.status}`);
  }

  // Test DELETE /teachers/:id
  console.log('5. Testing DELETE /teachers/:id');
  const deleteResponse = await fetch(`${API_URL}/teachers/${createdTeacher.id}`, {
    method: 'DELETE',
    headers: getAuthHeader(token),
  });

  if (deleteResponse.ok) {
    console.log('✅ DELETE /teachers/:id successful');
  } else {
    console.warn(`⚠️ DELETE /teachers/:id failed: ${deleteResponse.status}`);
  }
}

// Main test function
async function runAuthTest() {
  try {
    console.log('🚀 Starting Auth + Teachers CRUD Test');
    console.log('API URL:', API_URL);

    // You'll need to provide actual credentials
    const username = process.env.TEST_USERNAME || 'admin';
    const password = process.env.TEST_PASSWORD || 'admin123';

    console.log(`Using credentials: ${username} / ${'*'.repeat(password.length)}`);

    // Test login
    const loginData = await login(username, password);
    const token = loginData.access_token;

    if (!token) {
      throw new Error('No access_token in login response');
    }

    console.log('🔑 Token received, length:', token.length);

    // Test teachers CRUD
    await testTeachersCRUD(token);

    console.log('🎉 All tests completed successfully!');
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    process.exit(1);
  }
}

// Run the test
runAuthTest();
