/**
 * Store Middleware Collection
 *
 * Reusable middleware for Zustand stores with best practices:
 * - Logging middleware for debugging
 * - Performance monitoring middleware
 * - Error handling middleware
 * - Persistence middleware with encryption
 * - Real-time sync middleware
 * - Validation middleware
 */

import { StateCreator } from 'zustand';

// Types for middleware
type Logger = {
  log: (...args: any[]) => void;
  warn: (...args: any[]) => void;
  error: (...args: any[]) => void;
};

interface LoggingOptions {
  enabled?: boolean;
  logger?: Logger;
  collapsed?: boolean;
  predicate?: (state: any, action: string) => boolean;
  actionTransformer?: (action: string) => string;
  stateTransformer?: (state: any) => any;
}

interface PerformanceOptions {
  enabled?: boolean;
  threshold?: number; // milliseconds
  logger?: Logger;
}

interface ValidationOptions<T> {
  enabled?: boolean;
  validator?: (state: T) => string[] | null;
  onValidationError?: (errors: string[]) => void;
}

// Logging Middleware
export const loggingMiddleware =
  <T>(options: LoggingOptions = {}) =>
  (config: StateCreator<T, [], [], T>): StateCreator<T, [], [], T> =>
  (set, get, api) => {
    const {
      enabled = process.env.NODE_ENV === 'development',
      logger = console,
      collapsed = true,
      predicate = () => true,
      actionTransformer = (action: string) => action,
      stateTransformer = (state: any) => state,
    } = options;

    if (!enabled) {
      return config(set, get, api);
    }

    const loggedSet: typeof set = (partial, replace) => {
      const prevState = get();
      const startTime = performance.now();

      set(partial, replace as false | undefined);

      const nextState = get();
      const endTime = performance.now();
      const duration = endTime - startTime;

      // Determine action name
      let actionName = 'setState';
      if (typeof partial === 'function') {
        actionName = partial.name || 'anonymous';
      }

      const transformedAction = actionTransformer(actionName);

      if (predicate(nextState, transformedAction)) {
        const groupName = `🏪 Store Action: ${transformedAction} (${duration.toFixed(2)}ms)`;

        if (collapsed) {
          logger.log(`%c${groupName}`, 'color: #9E9E9E; font-weight: bold');
          logger.log('Previous State:', stateTransformer(prevState));
          logger.log('Next State:', stateTransformer(nextState));
        } else {
          console.group(groupName);
          logger.log('Previous State:', stateTransformer(prevState));
          logger.log('Next State:', stateTransformer(nextState));
          console.groupEnd();
        }
      }
    };

    return config(loggedSet, get, api);
  };

// Performance Monitoring Middleware
export const performanceMiddleware =
  <T>(options: PerformanceOptions = {}) =>
  (config: StateCreator<T, [], [], T>): StateCreator<T, [], [], T> =>
  (set, get, api) => {
    const {
      enabled = process.env.NODE_ENV === 'development',
      threshold = 10, // 10ms threshold
      logger = console,
    } = options;

    if (!enabled) {
      return config(set, get, api);
    }

    const performanceSet: typeof set = (partial, replace) => {
      const startTime = performance.now();

      set(partial, replace as false | undefined);

      const endTime = performance.now();
      const duration = endTime - startTime;

      if (duration > threshold) {
        let actionName = 'setState';
        if (typeof partial === 'function') {
          actionName = partial.name || 'anonymous';
        }

        logger.warn(
          `⚠️ Slow store update detected: ${actionName} took ${duration.toFixed(
            2
          )}ms (threshold: ${threshold}ms)`
        );
      }
    };

    return config(performanceSet, get, api);
  };

// Error Handling Middleware
export const errorHandlingMiddleware =
  <T>(onError?: (error: Error, state: T) => void) =>
  (config: StateCreator<T, [], [], T>): StateCreator<T, [], [], T> =>
  (set, get, api) => {
    const errorHandlingSet: typeof set = (partial, replace) => {
      try {
        set(partial, replace as false | undefined);
      } catch (error) {
        const currentState = get();
        console.error('Store update error:', error);

        if (onError && error instanceof Error) {
          onError(error, currentState);
        }

        // Re-throw to maintain error propagation
        throw error;
      }
    };

    return config(errorHandlingSet, get, api);
  };

// Validation Middleware
export const validationMiddleware =
  <T>(options: ValidationOptions<T> = {}) =>
  (config: StateCreator<T, [], [], T>): StateCreator<T, [], [], T> =>
  (set, get, api) => {
    const {
      enabled = process.env.NODE_ENV === 'development',
      validator,
      onValidationError,
    } = options;

    if (!enabled || !validator) {
      return config(set, get, api);
    }

    const validatingSet: typeof set = (partial, replace) => {
      set(partial, replace as false | undefined);

      const newState = get();
      const errors = validator(newState);

      if (errors && errors.length > 0) {
        console.error('State validation errors:', errors);

        if (onValidationError) {
          onValidationError(errors);
        }
      }
    };

    return config(validatingSet, get, api);
  };

// Persistence Middleware with Encryption (placeholder)
export const encryptedPersistenceMiddleware =
  <T>(name: string, encryptionKey?: string) =>
  (config: StateCreator<T, [], [], T>): StateCreator<T, [], [], T> =>
  (set, get, api) => {
    // Simple encryption/decryption (in production, use proper crypto)
    const encrypt = (data: string): string => {
      if (!encryptionKey) return data;
      // Placeholder encryption - use proper crypto in production
      return btoa(data);
    };

    const decrypt = (data: string): string => {
      if (!encryptionKey) return data;
      try {
        return atob(data);
      } catch {
        return data;
      }
    };

    // Load initial state from encrypted storage
    const loadState = (): Partial<T> | null => {
      try {
        const stored = localStorage.getItem(name);
        if (!stored) return null;

        const decrypted = decrypt(stored);
        return JSON.parse(decrypted);
      } catch (error) {
        console.warn(`Failed to load encrypted state for ${name}:`, error);
        return null;
      }
    };

    // Save state to encrypted storage
    const saveState = (state: T): void => {
      try {
        const serialized = JSON.stringify(state);
        const encrypted = encrypt(serialized);
        localStorage.setItem(name, encrypted);
      } catch (error) {
        console.warn(`Failed to save encrypted state for ${name}:`, error);
      }
    };

    // Initialize with stored state
    const initialState = loadState();

    const persistingSet: typeof set = (partial, replace) => {
      set(partial, replace as false | undefined);

      // Save state after update
      const newState = get();
      saveState(newState);
    };

    const store = config(persistingSet, get, api);

    // Apply initial state if available
    if (initialState) {
      set(initialState as any, true);
    }

    return store;
  };

// Real-time Sync Middleware (placeholder for WebSocket integration)
export const realtimeSyncMiddleware =
  <T>(
    options: {
      enabled?: boolean;
      endpoint?: string;
      onConnect?: () => void;
      onDisconnect?: () => void;
      onMessage?: (data: any) => void;
    } = {}
  ) =>
  (config: StateCreator<T, [], [], T>): StateCreator<T, [], [], T> =>
  (set, get, api) => {
    const { enabled = false, endpoint, onConnect, onDisconnect, onMessage } = options;

    if (!enabled || !endpoint) {
      return config(set, get, api);
    }

    // Placeholder for WebSocket connection
    let ws: WebSocket | null = null;

    const connect = () => {
      try {
        ws = new WebSocket(endpoint);

        ws.onopen = () => {
          console.log(`WebSocket connected to ${endpoint}`);
          onConnect?.();
        };

        ws.onclose = () => {
          console.log(`WebSocket disconnected from ${endpoint}`);
          onDisconnect?.();

          // Attempt reconnection after 5 seconds
          setTimeout(connect, 5000);
        };

        ws.onmessage = event => {
          try {
            const data = JSON.parse(event.data);
            onMessage?.(data);

            // Apply remote state updates
            if (data.type === 'state_update' && data.payload) {
              set(data.payload, false);
            }
          } catch (error) {
            console.error('WebSocket message parsing error:', error);
          }
        };

        ws.onerror = error => {
          console.error('WebSocket error:', error);
        };
      } catch (error) {
        console.error('WebSocket connection error:', error);
      }
    };

    // Start connection
    connect();

    const syncingSet: typeof set = (partial, replace) => {
      set(partial, replace as false | undefined);

      // Send state updates to server (if needed)
      if (ws && ws.readyState === WebSocket.OPEN) {
        const newState = get();
        ws.send(
          JSON.stringify({
            type: 'state_update',
            payload: newState,
            timestamp: Date.now(),
          })
        );
      }
    };

    return config(syncingSet, get, api);
  };

// Compose multiple middleware
export const composeMiddleware = <T>(
  ...middlewares: Array<(config: StateCreator<T, [], [], T>) => StateCreator<T, [], [], T>>
) => {
  return (config: StateCreator<T, [], [], T>): StateCreator<T, [], [], T> => {
    return middlewares.reduceRight((acc, middleware) => middleware(acc), config);
  };
};

// Predefined middleware combinations
export const developmentMiddleware = <T>(storeName: string) =>
  composeMiddleware<T>(
    loggingMiddleware({ enabled: true }),
    performanceMiddleware({ enabled: true, threshold: 5 }),
    errorHandlingMiddleware(),
    validationMiddleware({ enabled: true })
  );

export const productionMiddleware = <T>(storeName: string) =>
  composeMiddleware<T>(errorHandlingMiddleware(), performanceMiddleware({ enabled: false }));

// Store factory with automatic middleware application
export const createStoreWithMiddleware = <T>(
  storeName: string,
  config: StateCreator<T, [], [], T>,
  options: {
    logging?: LoggingOptions;
    performance?: PerformanceOptions;
    validation?: ValidationOptions<T>;
    encryption?: { key?: string };
    realtime?: { endpoint?: string };
  } = {}
) => {
  const middleware = composeMiddleware<T>(
    loggingMiddleware(options.logging),
    performanceMiddleware(options.performance),
    errorHandlingMiddleware(),
    validationMiddleware(options.validation),
    ...(options.encryption?.key
      ? [encryptedPersistenceMiddleware(storeName, options.encryption.key) as any]
      : []),
    ...(options.realtime?.endpoint
      ? [realtimeSyncMiddleware({ enabled: true, endpoint: options.realtime.endpoint })]
      : [])
  );

  return middleware(config);
};
