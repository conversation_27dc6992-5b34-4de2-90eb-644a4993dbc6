# FastAPI Backend - Database Error Fix

from fastapi import <PERSON><PERSON><PERSON>, HTTPException
from fastapi.middleware.cors import CORSMiddleware
import logging
import traceback

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000", "http://127.0.0.1:3000"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Health endpoint
@app.get("/api/v1/health")
async def health_check():
    """Health check endpoint"""
    try:
        # Test database connection here if you have one
        # db_status = await test_database_connection()
        
        return {
            "status": "healthy",
            "message": "API is running",
            "database": "connected"  # Update based on actual DB test
        }
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        raise HTTPException(status_code=500, detail="Health check failed")

# Teachers endpoint with proper error handling
@app.get("/api/v1/teachers/")
async def get_teachers():
    """Get all teachers with robust error handling"""
    try:
        logger.info("Fetching teachers from database...")
        
        # TODO: Replace with your actual database query
        # Example database query patterns:
        
        # Option 1: If using SQLAlchemy
        # teachers = db.query(Teacher).all()
        # return [teacher.to_dict() for teacher in teachers]
        
        # Option 2: If using raw SQL
        # cursor.execute("SELECT * FROM teachers")
        # teachers = cursor.fetchall()
        # return teachers
        
        # Option 3: If using async database
        # teachers = await database.fetch_all("SELECT * FROM teachers")
        # return teachers
        
        # For now, return mock data to test the endpoint
        mock_teachers = [
            {
                "id": "1",
                "name": "John Doe",
                "email": "<EMAIL>",
                "subject": "Mathematics",
                "department": "Science",
                "status": "ACTIVE"
            },
            {
                "id": "2", 
                "name": "Jane Smith",
                "email": "<EMAIL>",
                "subject": "English",
                "department": "Literature",
                "status": "ACTIVE"
            }
        ]
        
        logger.info(f"Successfully fetched {len(mock_teachers)} teachers")
        return mock_teachers
        
    except Exception as e:
        # Log the full error for debugging
        logger.error(f"Database error in get_teachers: {e}")
        logger.error(f"Traceback: {traceback.format_exc()}")
        
        # Return empty array instead of 500 error for better UX
        logger.warning("Returning empty array due to database error")
        return []
        
        # Alternative: Return 500 with detailed error (for debugging)
        # raise HTTPException(
        #     status_code=500, 
        #     detail=f"Database error: {str(e)}"
        # )

# Database connection test function (implement based on your DB)
async def test_database_connection():
    """Test database connectivity"""
    try:
        # TODO: Implement actual database connection test
        # Examples:
        
        # For PostgreSQL with asyncpg:
        # await database.execute("SELECT 1")
        
        # For SQLAlchemy:
        # db.execute("SELECT 1").fetchone()
        
        # For now, assume connection is working
        return True
        
    except Exception as e:
        logger.error(f"Database connection test failed: {e}")
        raise e

# Additional debugging endpoint
@app.get("/api/v1/debug/database")
async def debug_database():
    """Debug database connection issues"""
    try:
        # Test basic database operations
        connection_status = await test_database_connection()
        
        return {
            "database_connected": connection_status,
            "message": "Database connection successful"
        }
        
    except Exception as e:
        return {
            "database_connected": False,
            "error": str(e),
            "message": "Database connection failed"
        }

# Error handler for unhandled exceptions
@app.exception_handler(Exception)
async def global_exception_handler(request, exc):
    """Global exception handler for debugging"""
    logger.error(f"Unhandled exception: {exc}")
    logger.error(f"Request: {request.method} {request.url}")
    logger.error(f"Traceback: {traceback.format_exc()}")
    
    return HTTPException(
        status_code=500,
        detail="Internal server error - check server logs"
    )
