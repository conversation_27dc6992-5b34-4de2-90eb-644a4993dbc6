# FastAPI Backend - Add these to your main.py file

from fastapi import <PERSON><PERSON><PERSON>, HTTPException, Depends
from fastapi.middleware.cors import CORSMiddleware
from typing import List, Optional
import logging

# If you don't have an app instance, create one:
# app = FastAPI(title="School Management API", version="1.0.0")

# CRITICAL: Add CORS middleware BEFORE routes (if not already added)
app.add_middleware(
    CORSMiddleware,
    allow_origins=[
        "http://localhost:3000",
        "http://127.0.0.1:3000",
        "http://localhost:3001",  # In case you use different port
    ],
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"],
    allow_headers=["*"],
)

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Health Check Endpoint
@app.get("/api/v1/health")
async def health_check():
    """Health check endpoint for frontend connectivity testing"""
    return {
        "status": "healthy",
        "message": "School Management API is running",
        "version": "1.0.0"
    }

# Teachers Endpoints
@app.get("/api/v1/teachers/")
async def get_teachers():
    """Get all teachers - returns empty array if none found (no 404)"""
    try:
        # TODO: Replace with your actual database query
        # Example:
        # teachers = await db.query("SELECT * FROM teachers")
        # return teachers

        # For now, return empty array (prevents 404 errors)
        logger.info("Fetching teachers list")
        teachers = []  # Mock empty list

        return teachers

    except Exception as e:
        logger.error(f"Error fetching teachers: {e}")
        # Return empty array instead of raising error
        return []

@app.get("/api/v1/teachers/{teacher_id}")
async def get_teacher(teacher_id: str):
    """Get single teacher by ID"""
    try:
        # TODO: Replace with your actual database query
        # teacher = await db.query("SELECT * FROM teachers WHERE id = ?", teacher_id)
        # if not teacher:
        #     raise HTTPException(status_code=404, detail="Teacher not found")
        # return teacher

        # Mock response for now
        return {
            "id": teacher_id,
            "name": "Mock Teacher",
            "email": "<EMAIL>",
            "subject": "Mathematics",
            "department": "Science",
            "status": "ACTIVE"
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error fetching teacher {teacher_id}: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")

@app.post("/api/v1/teachers/")
async def create_teacher(teacher_data: dict):
    """Create new teacher"""
    try:
        # TODO: Add validation and database insertion
        # validated_data = TeacherCreate(**teacher_data)
        # new_teacher = await db.insert_teacher(validated_data)
        # return new_teacher

        # Mock response for now
        logger.info(f"Creating teacher: {teacher_data.get('name', 'Unknown')}")

        # Return the created teacher with an ID
        created_teacher = {
            "id": "mock-id-123",
            **teacher_data,
            "created_at": "2024-01-01T00:00:00Z",
            "updated_at": "2024-01-01T00:00:00Z"
        }

        return created_teacher

    except Exception as e:
        logger.error(f"Error creating teacher: {e}")
        raise HTTPException(status_code=500, detail="Failed to create teacher")

@app.put("/api/v1/teachers/{teacher_id}")
async def update_teacher(teacher_id: str, teacher_data: dict):
    """Update existing teacher"""
    try:
        # TODO: Add validation and database update
        # validated_data = TeacherUpdate(**teacher_data)
        # updated_teacher = await db.update_teacher(teacher_id, validated_data)
        # return updated_teacher

        # Mock response for now
        logger.info(f"Updating teacher {teacher_id}")

        updated_teacher = {
            "id": teacher_id,
            **teacher_data,
            "updated_at": "2024-01-01T00:00:00Z"
        }

        return updated_teacher

    except Exception as e:
        logger.error(f"Error updating teacher {teacher_id}: {e}")
        raise HTTPException(status_code=500, detail="Failed to update teacher")

@app.delete("/api/v1/teachers/{teacher_id}")
async def delete_teacher(teacher_id: str):
    """Delete teacher"""
    try:
        # TODO: Add database deletion
        # await db.delete_teacher(teacher_id)

        # Mock response for now
        logger.info(f"Deleting teacher {teacher_id}")

        return {"message": f"Teacher {teacher_id} deleted successfully"}

    except Exception as e:
        logger.error(f"Error deleting teacher {teacher_id}: {e}")
        raise HTTPException(status_code=500, detail="Failed to delete teacher")

# Classes Endpoints
@app.get("/api/v1/classes/")
async def get_classes():
    """Get all classes - returns empty array if none found (no 404)"""
    try:
        # TODO: Replace with your actual database query
        # Example:
        # classes = await db.query("SELECT * FROM classes")
        # return classes

        # For now, return empty array (prevents 404 errors)
        logger.info("Fetching classes list")
        classes = []  # Mock empty list

        return classes

    except Exception as e:
        logger.error(f"Error fetching classes: {e}")
        # Return empty array instead of raising error
        return []

@app.get("/api/v1/classes/{class_id}")
async def get_class(class_id: str):
    """Get single class by ID"""
    try:
        # TODO: Replace with your actual database query
        # class_obj = await db.query("SELECT * FROM classes WHERE id = ?", class_id)
        # if not class_obj:
        #     raise HTTPException(status_code=404, detail="Class not found")
        # return class_obj

        # Mock response for now
        return {
            "id": class_id,
            "name": "Mock Class 1A",
            "grade": "1",
            "section": "A",
            "capacity": 30,
            "enrolled": 25,
            "teacher_id": "teacher-123",
            "teacher_name": "John Doe",
            "room": "Room 101",
            "schedule": "Mon-Fri 9:00-15:00",
            "academic_year": "2024-2025",
            "status": "ACTIVE"
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error fetching class {class_id}: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")

@app.post("/api/v1/classes/")
async def create_class(class_data: dict):
    """Create new class"""
    try:
        # TODO: Add validation and database insertion
        # validated_data = ClassCreate(**class_data)
        # new_class = await db.insert_class(validated_data)
        # return new_class

        # Mock response for now
        logger.info(f"Creating class: {class_data.get('name', 'Unknown')}")

        # Return the created class with an ID
        created_class = {
            "id": "mock-class-123",
            **class_data,
            "enrolled": 0,  # New class starts with 0 enrollment
            "created_at": "2024-01-01T00:00:00Z",
            "updated_at": "2024-01-01T00:00:00Z"
        }

        return created_class

    except Exception as e:
        logger.error(f"Error creating class: {e}")
        raise HTTPException(status_code=500, detail="Failed to create class")

@app.put("/api/v1/classes/{class_id}")
async def update_class(class_id: str, class_data: dict):
    """Update existing class"""
    try:
        # TODO: Add validation and database update
        # validated_data = ClassUpdate(**class_data)
        # updated_class = await db.update_class(class_id, validated_data)
        # return updated_class

        # Mock response for now
        logger.info(f"Updating class {class_id}")

        updated_class = {
            "id": class_id,
            **class_data,
            "updated_at": "2024-01-01T00:00:00Z"
        }

        return updated_class

    except Exception as e:
        logger.error(f"Error updating class {class_id}: {e}")
        raise HTTPException(status_code=500, detail="Failed to update class")

@app.delete("/api/v1/classes/{class_id}")
async def delete_class(class_id: str):
    """Delete class"""
    try:
        # TODO: Add database deletion
        # await db.delete_class(class_id)

        # Mock response for now
        logger.info(f"Deleting class {class_id}")

        return {"message": f"Class {class_id} deleted successfully"}

    except Exception as e:
        logger.error(f"Error deleting class {class_id}: {e}")
        raise HTTPException(status_code=500, detail="Failed to delete class")

@app.get("/api/v1/classes/stats")
async def get_class_stats():
    """Get class statistics - accepts optional query parameters"""
    try:
        # TODO: Replace with your actual database query
        # Example:
        # stats = await db.query("SELECT COUNT(*) as total, SUM(enrolled) as total_students FROM classes")
        # active_count = await db.query("SELECT COUNT(*) FROM classes WHERE status = 'ACTIVE'")
        # inactive_count = await db.query("SELECT COUNT(*) FROM classes WHERE status = 'INACTIVE'")
        # avg_capacity = await db.query("SELECT AVG(capacity) FROM classes")

        # Mock response for now - return realistic data
        logger.info("Fetching class statistics")
        stats = {
            "total": 12,
            "active": 10,
            "inactive": 2,
            "totalStudents": 285,
            "averageCapacity": 30
        }

        return stats

    except Exception as e:
        logger.error(f"Error fetching class stats: {e}")
        # Return empty stats instead of raising error
        return {
            "total": 0,
            "active": 0,
            "inactive": 0,
            "totalStudents": 0,
            "averageCapacity": 0
        }

@app.get("/api/v1/teachers/stats")
async def get_teacher_stats():
    """Get teacher statistics - mirrors classes/stats response schema"""
    try:
        # TODO: Replace with your actual database query
        # Example:
        # stats = await db.query("SELECT COUNT(*) as total FROM teachers")
        # active_count = await db.query("SELECT COUNT(*) FROM teachers WHERE status = 'ACTIVE'")
        # inactive_count = await db.query("SELECT COUNT(*) FROM teachers WHERE status = 'INACTIVE'")
        # departments = await db.query("SELECT COUNT(DISTINCT department) FROM teachers")
        # avg_experience = await db.query("SELECT AVG(years_experience) FROM teachers")

        # Mock response for now - return realistic data
        logger.info("Fetching teacher statistics")
        stats = {
            "total": 25,
            "active": 23,
            "inactive": 2,
            "departments": 8,
            "averageExperience": 7
        }

        return stats

    except Exception as e:
        logger.error(f"Error fetching teacher stats: {e}")
        # Return empty stats instead of raising error
        return {
            "total": 0,
            "active": 0,
            "inactive": 0,
            "departments": 0,
            "averageExperience": 0
        }

# Optional: Add a root endpoint
@app.get("/")
async def root():
    return {"message": "School Management API", "docs": "/docs"}

# Optional: Add API info endpoint
@app.get("/api/v1/info")
async def api_info():
    return {
        "name": "School Management API",
        "version": "1.0.0",
        "endpoints": {
            "health": "/api/v1/health",
            "teachers": "/api/v1/teachers/",
            "classes": "/api/v1/classes/",
            "auth": "/api/v1/auth/login"
        }
    }
