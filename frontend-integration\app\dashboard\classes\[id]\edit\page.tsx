'use client';

import { Arrow<PERSON>ef<PERSON>, AlertTriangle } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { useEffect } from 'react';

import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { ClassForm } from '@/components/forms/ClassForm';
import { useClass, useUpdateClass } from '@/hooks/useClasses';
import { useTeachers } from '@/hooks/useTeachers';
import { useAuth } from '@/hooks/useAuth';
import { useToast } from '@/hooks/use-toast';
import { canEditClass } from '@/lib/permissions';
import type { ClassUpdate } from '@/schemas/zodSchemas';

export default function EditClassPage({ params }: { params: { id: string } }) {
  const router = useRouter();
  const { user } = useAuth();
  const { toast } = useToast();
  const { data: classData, isLoading: isLoadingClass } = useClass(params.id);
  const { data: teachersData } = useTeachers();
  const updateClassMutation = useUpdateClass();

  // Check permissions
  useEffect(() => {
    if (!canEditClass(user?.role)) {
      toast({
        title: 'Access Denied',
        description: 'Only administrators can edit classes',
        variant: 'destructive',
      });
      router.push('/dashboard/classes');
    }
  }, [user?.role, router, toast]);

  const handleSubmit = async (data: ClassUpdate) => {
    try {
      await updateClassMutation.mutateAsync({ id: params.id, data });
      toast({
        title: 'Success',
        description: 'Class updated successfully',
      });
      router.push(`/dashboard/classes/${params.id}`);
    } catch (error) {
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to update class',
        variant: 'destructive',
      });
    }
  };

  const handleCancel = () => {
    router.push(`/dashboard/classes/${params.id}`);
  };

  if (!canEditClass(user?.role)) {
    return (
      <div className='container mx-auto p-4 sm:p-6 lg:p-8'>
        <Card>
          <CardContent className='flex flex-col items-center justify-center py-12'>
            <AlertTriangle className='h-16 w-16 text-destructive mb-6' />
            <h2 className='text-2xl font-bold mb-2'>Access Denied</h2>
            <p className='text-muted-foreground text-center mb-6 max-w-md'>
              Only administrators can edit classes.
            </p>
            <Button onClick={() => router.push('/dashboard/classes')}>
              <ArrowLeft className='h-4 w-4 mr-2' />
              Back to Classes
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (isLoadingClass) {
    return <div className='p-6'>Loading...</div>;
  }

  if (!classData) {
    return <div className='p-6'>Class not found</div>;
  }

  const teachers = teachersData?.data?.map(teacher => ({
    id: teacher.id,
    name: teacher.name,
  })) || [];

  return (
    <div className='container mx-auto p-4 sm:p-6 lg:p-8 space-y-6'>
      <div className='flex items-center gap-4'>
        <Button variant='ghost' size='sm' onClick={handleCancel}>
          <ArrowLeft className='h-4 w-4 mr-2' />
          Back to Class
        </Button>
        <div>
          <h1 className='text-2xl sm:text-3xl font-bold tracking-tight'>Edit Class</h1>
          <p className='text-muted-foreground'>Update class information</p>
        </div>
      </div>

      <ClassForm
        mode="edit"
        initialData={classData}
        onSubmit={handleSubmit}
        onCancel={handleCancel}
        isLoading={updateClassMutation.isPending}
        teachers={teachers}
      />
    </div>
  );
}