import { Card, CardContent } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';

/**
 * Teachers Page Loading Component
 *
 * Shows loading state while teachers data is being fetched
 */
export default function TeachersLoading() {
  return (
    <div className='container mx-auto p-4 sm:p-6 lg:p-8 space-y-6'>
      {/* Header Skeleton */}
      <div className='flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4'>
        <div>
          <Skeleton className='h-8 w-48 mb-2' />
          <Skeleton className='h-4 w-64' />
        </div>
        <Skeleton className='h-10 w-32' />
      </div>

      {/* Stats Cards Skeleton */}
      <div className='grid grid-cols-2 lg:grid-cols-4 gap-4'>
        {Array.from({ length: 4 }).map((_, i) => (
          <Card key={i}>
            <CardContent className='p-6'>
              <div className='flex items-center space-x-2'>
                <Skeleton className='h-8 w-8 rounded-lg' />
                <div>
                  <Skeleton className='h-4 w-20 mb-1' />
                  <Skeleton className='h-6 w-12' />
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Filters Skeleton */}
      <Card>
        <CardContent className='p-6'>
          <div className='grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4'>
            {Array.from({ length: 4 }).map((_, i) => (
              <Skeleton key={i} className='h-10 w-full' />
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Teachers Grid Skeleton */}
      <div className='grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4'>
        {Array.from({ length: 8 }).map((_, i) => (
          <Card key={i}>
            <CardContent className='p-6'>
              <div className='flex items-center space-x-3 mb-4'>
                <Skeleton className='h-12 w-12 rounded-full' />
                <div>
                  <Skeleton className='h-4 w-24 mb-1' />
                  <Skeleton className='h-3 w-16' />
                </div>
              </div>
              <Skeleton className='h-3 w-full mb-2' />
              <Skeleton className='h-3 w-3/4 mb-4' />
              <div className='flex space-x-2'>
                <Skeleton className='h-8 w-16' />
                <Skeleton className='h-8 w-16' />
                <Skeleton className='h-8 w-16' />
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Pagination Skeleton */}
      <div className='flex flex-col sm:flex-row items-center justify-between gap-4'>
        <Skeleton className='h-4 w-48' />
        <div className='flex items-center space-x-2'>
          <Skeleton className='h-8 w-20' />
          <div className='flex space-x-1'>
            {Array.from({ length: 3 }).map((_, i) => (
              <Skeleton key={i} className='h-8 w-8' />
            ))}
          </div>
          <Skeleton className='h-8 w-16' />
        </div>
      </div>
    </div>
  );
}
