/**
 * Hydration-Safe Store Hook
 * 
 * Prevents hydration mismatches by ensuring client-side state
 * matches server-side state during SSR/hydration
 */

import { useEffect, useState } from 'react';

/**
 * Hook to safely use Zustand stores during hydration
 * 
 * This prevents hydration mismatches by:
 * 1. Returning undefined during SSR
 * 2. Returning the actual store value after hydration
 * 3. Providing a fallback value during the hydration process
 */
export function useHydrateStore<T, F>(
  store: (callback: (state: T) => unknown) => unknown,
  callback: (state: T) => F,
  fallback?: F
): F | undefined {
  const result = store(callback) as F;
  const [hydrated, setHydrated] = useState(false);

  useEffect(() => {
    setHydrated(true);
  }, []);

  // During SSR and before hydration, return fallback or undefined
  if (!hydrated) {
    return fallback;
  }

  // After hydration, return the actual store value
  return result;
}

/**
 * Hook specifically for auth store hydration
 * Prevents auth-related hydration mismatches
 */
export function useHydrateAuth<T>(
  store: (callback: (state: any) => unknown) => unknown,
  callback: (state: any) => T,
  fallback: T
): T {
  const [hydrated, setHydrated] = useState(false);
  const result = store(callback) as T;

  useEffect(() => {
    setHydrated(true);
  }, []);

  // Always return fallback during SSR and initial hydration
  if (!hydrated) {
    return fallback;
  }

  return result;
}

/**
 * Hook for client-only components
 * Returns true only after hydration is complete
 */
export function useIsClient(): boolean {
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true);
  }, []);

  return isClient;
}

/**
 * Hook for localStorage access that's hydration-safe
 */
export function useHydrateLocalStorage<T>(
  key: string,
  fallback: T
): [T, (value: T) => void] {
  const [hydrated, setHydrated] = useState(false);
  const [value, setValue] = useState<T>(fallback);

  useEffect(() => {
    setHydrated(true);
    
    // Only access localStorage after hydration
    if (typeof window !== 'undefined') {
      try {
        const stored = localStorage.getItem(key);
        if (stored !== null) {
          setValue(JSON.parse(stored));
        }
      } catch (error) {
        console.warn(`Error reading localStorage key "${key}":`, error);
      }
    }
  }, [key]);

  const setStoredValue = (newValue: T) => {
    setValue(newValue);
    
    if (hydrated && typeof window !== 'undefined') {
      try {
        localStorage.setItem(key, JSON.stringify(newValue));
      } catch (error) {
        console.warn(`Error setting localStorage key "${key}":`, error);
      }
    }
  };

  // Return fallback during SSR and initial hydration
  if (!hydrated) {
    return [fallback, setStoredValue];
  }

  return [value, setStoredValue];
}

/**
 * Hook for theme-related hydration issues
 * Prevents flash of wrong theme
 */
export function useHydrateTheme(): {
  theme: string | undefined;
  isHydrated: boolean;
} {
  const [theme, setTheme] = useState<string | undefined>(undefined);
  const [isHydrated, setIsHydrated] = useState(false);

  useEffect(() => {
    // Check for theme in localStorage or system preference
    const storedTheme = localStorage.getItem('theme');
    const systemTheme = window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
    
    setTheme(storedTheme || systemTheme);
    setIsHydrated(true);
  }, []);

  return { theme, isHydrated };
}

/**
 * Hook for preventing layout shift during hydration
 * Useful for components that depend on window size
 */
export function useHydrateWindowSize(): {
  width: number;
  height: number;
  isHydrated: boolean;
} {
  const [windowSize, setWindowSize] = useState({
    width: 0,
    height: 0,
  });
  const [isHydrated, setIsHydrated] = useState(false);

  useEffect(() => {
    function handleResize() {
      setWindowSize({
        width: window.innerWidth,
        height: window.innerHeight,
      });
    }

    // Set initial size and mark as hydrated
    handleResize();
    setIsHydrated(true);

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  return {
    ...windowSize,
    isHydrated,
  };
}

/**
 * Hook for hydration-safe media queries
 */
export function useHydrateMediaQuery(query: string): {
  matches: boolean;
  isHydrated: boolean;
} {
  const [matches, setMatches] = useState(false);
  const [isHydrated, setIsHydrated] = useState(false);

  useEffect(() => {
    const mediaQuery = window.matchMedia(query);
    
    setMatches(mediaQuery.matches);
    setIsHydrated(true);

    const handler = (event: MediaQueryListEvent) => {
      setMatches(event.matches);
    };

    mediaQuery.addEventListener('change', handler);
    return () => mediaQuery.removeEventListener('change', handler);
  }, [query]);

  return { matches, isHydrated };
}

/**
 * Generic hook for any client-side only value
 */
export function useClientOnly<T>(
  clientValue: () => T,
  serverValue: T
): T {
  const [value, setValue] = useState<T>(serverValue);
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true);
    setValue(clientValue());
  }, []);

  return isClient ? value : serverValue;
}

/**
 * Hook for preventing hydration mismatches with dates
 */
export function useHydrateDate(date?: Date): {
  date: Date | undefined;
  isHydrated: boolean;
} {
  const [hydratedDate, setHydratedDate] = useState<Date | undefined>(undefined);
  const [isHydrated, setIsHydrated] = useState(false);

  useEffect(() => {
    setHydratedDate(date);
    setIsHydrated(true);
  }, [date]);

  return {
    date: isHydrated ? hydratedDate : undefined,
    isHydrated,
  };
}

/**
 * Hook for hydration-safe random values
 * Prevents mismatches when using Math.random() or similar
 */
export function useHydrateRandom(seed?: number): {
  random: number;
  isHydrated: boolean;
} {
  const [random, setRandom] = useState(0);
  const [isHydrated, setIsHydrated] = useState(false);

  useEffect(() => {
    // Use seed if provided, otherwise use Math.random()
    const value = seed !== undefined ? seed : Math.random();
    setRandom(value);
    setIsHydrated(true);
  }, [seed]);

  return { random, isHydrated };
}
