import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { ArrowLeft, BookOpen, Calendar, CheckCircle, Clock, Edit, User, Users, XCircle } from 'lucide-react';
import Link from 'next/link';
import { notFound } from 'next/navigation';

// Mock attendance data
const mockAttendance = [
  {
    id: 1,
    studentName: '<PERSON>',
    studentId: 'STU001',
    grade: 'Grade 10',
    date: '2024-03-15',
    status: 'Present',
    timeIn: '08:00 AM',
    timeOut: '03:30 PM',
    subject: 'Mathematics',
    teacher: 'Dr. <PERSON>',
    remarks: '',
    createdAt: '2024-03-15T08:00:00Z',
    updatedAt: '2024-03-15T08:00:00Z',
  },
  {
    id: 2,
    studentName: '<PERSON>',
    studentId: 'STU002',
    grade: 'Grade 9',
    date: '2024-03-15',
    status: 'Absent',
    timeIn: '',
    timeOut: '',
    subject: 'Physics',
    teacher: 'Prof. <PERSON>',
    remarks: 'Sick leave',
    createdAt: '2024-03-15T08:00:00Z',
    updatedAt: '2024-03-15T08:00:00Z',
  },
];

interface AttendanceDetailPageProps {
  params: {
    id: string;
  };
}

export default function AttendanceDetailPage({ params }: AttendanceDetailPageProps) {
  const attendance = mockAttendance.find(a => a.id === parseInt(params.id));

  if (!attendance) {
    notFound();
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Present': return 'bg-green-100 text-green-800';
      case 'Absent': return 'bg-red-100 text-red-800';
      case 'Late': return 'bg-yellow-100 text-yellow-800';
      case 'Excused': return 'bg-blue-100 text-blue-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'Present': return <CheckCircle className='w-8 h-8 text-green-600' />;
      case 'Absent': return <XCircle className='w-8 h-8 text-red-600' />;
      case 'Late': return <Clock className='w-8 h-8 text-yellow-600' />;
      case 'Excused': return <CheckCircle className='w-8 h-8 text-blue-600' />;
      default: return <Clock className='w-8 h-8 text-gray-600' />;
    }
  };

  return (
    <div className='container mx-auto p-4 sm:p-6 lg:p-8 max-w-4xl'>
      {/* Header */}
      <div className='flex items-center justify-between mb-8'>
        <div className='flex items-center gap-4'>
          <Link href='/dashboard/attendance'>
            <Button variant='outline' size='sm'>
              <ArrowLeft className='w-4 h-4 mr-2' />
              Back to Attendance
            </Button>
          </Link>
          <div>
            <h1 className='text-3xl font-bold text-gray-900 flex items-center gap-2'>
              <Users className='w-8 h-8 text-blue-600' />
              Attendance Details
            </h1>
            <p className='text-gray-600 mt-1'>View detailed attendance record information</p>
          </div>
        </div>
        <Link href={`/dashboard/attendance/${attendance.id}/edit`}>
          <Button>
            <Edit className='w-4 h-4 mr-2' />
            Edit Record
          </Button>
        </Link>
      </div>

      {/* Attendance Details */}
      <div className='grid grid-cols-1 lg:grid-cols-3 gap-8'>
        <div className='lg:col-span-2'>
          <Card>
            <CardHeader>
              <div className='flex items-start justify-between'>
                <div className='space-y-2'>
                  <CardTitle className='text-2xl flex items-center gap-2'>
                    <User className='w-6 h-6' />
                    {attendance.studentName}
                  </CardTitle>
                  <div className='flex gap-2'>
                    <Badge variant='outline'>{attendance.studentId}</Badge>
                    <Badge variant='outline'>{attendance.grade}</Badge>
                    <Badge className={getStatusColor(attendance.status)}>
                      {attendance.status}
                    </Badge>
                  </div>
                </div>
                <div className='text-center'>
                  {getStatusIcon(attendance.status)}
                </div>
              </div>
            </CardHeader>
            <CardContent className='space-y-6'>
              {/* Date and Subject Information */}
              <div className='grid grid-cols-1 sm:grid-cols-2 gap-6'>
                <div className='flex items-center gap-2'>
                  <Calendar className='w-5 h-5 text-blue-600' />
                  <div>
                    <p className='font-medium'>Date</p>
                    <p className='text-sm text-muted-foreground'>{attendance.date}</p>
                  </div>
                </div>
                <div className='flex items-center gap-2'>
                  <BookOpen className='w-5 h-5 text-green-600' />
                  <div>
                    <p className='font-medium'>Subject</p>
                    <p className='text-sm text-muted-foreground'>{attendance.subject}</p>
                  </div>
                </div>
              </div>

              <Separator />

              {/* Time Information */}
              {attendance.status === 'Present' || attendance.status === 'Late' ? (
                <div className='space-y-4'>
                  <h3 className='font-semibold flex items-center gap-2'>
                    <Clock className='w-4 h-4' />
                    Time Details
                  </h3>
                  
                  <div className='grid grid-cols-1 sm:grid-cols-2 gap-4'>
                    <div className='p-4 bg-green-50 rounded-lg'>
                      <div className='text-center'>
                        <div className='text-2xl font-bold text-green-600'>
                          {attendance.timeIn || 'N/A'}
                        </div>
                        <p className='text-sm text-muted-foreground'>Time In</p>
                      </div>
                    </div>
                    <div className='p-4 bg-blue-50 rounded-lg'>
                      <div className='text-center'>
                        <div className='text-2xl font-bold text-blue-600'>
                          {attendance.timeOut || 'N/A'}
                        </div>
                        <p className='text-sm text-muted-foreground'>Time Out</p>
                      </div>
                    </div>
                  </div>
                </div>
              ) : (
                <div className='text-center py-8 bg-gray-50 rounded-lg'>
                  <XCircle className='w-16 h-16 text-gray-400 mx-auto mb-4' />
                  <p className='text-gray-600'>No time records for absent status</p>
                </div>
              )}

              <Separator />

              {/* Teacher Information */}
              <div>
                <h3 className='font-semibold mb-2 flex items-center gap-2'>
                  <User className='w-4 h-4' />
                  Teacher
                </h3>
                <p className='text-gray-700'>{attendance.teacher}</p>
              </div>

              {/* Remarks */}
              {attendance.remarks && (
                <>
                  <Separator />
                  <div>
                    <h3 className='font-semibold mb-2'>Remarks</h3>
                    <p className='text-gray-700 leading-relaxed'>
                      {attendance.remarks}
                    </p>
                  </div>
                </>
              )}
            </CardContent>
          </Card>
        </div>

        <div className='space-y-6'>
          {/* Status Summary */}
          <Card>
            <CardHeader>
              <CardTitle className='flex items-center gap-2'>
                <CheckCircle className='w-5 h-5' />
                Status Summary
              </CardTitle>
            </CardHeader>
            <CardContent className='space-y-4'>
              <div className='text-center'>
                <div className={`inline-flex items-center px-4 py-2 rounded-full text-lg font-bold ${getStatusColor(attendance.status)}`}>
                  {attendance.status}
                </div>
                <p className='text-sm text-muted-foreground mt-2'>Attendance Status</p>
              </div>
              {attendance.status === 'Present' && (
                <div className='text-center'>
                  <div className='text-2xl font-bold text-green-600'>
                    ✓
                  </div>
                  <p className='text-sm text-muted-foreground'>Full Day Present</p>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Metadata */}
          <Card>
            <CardHeader>
              <CardTitle>Record Information</CardTitle>
            </CardHeader>
            <CardContent className='space-y-3'>
              <div>
                <p className='text-sm font-medium text-muted-foreground'>Recorded</p>
                <p className='text-sm'>{new Date(attendance.createdAt).toLocaleString()}</p>
              </div>
              <div>
                <p className='text-sm font-medium text-muted-foreground'>Last Updated</p>
                <p className='text-sm'>{new Date(attendance.updatedAt).toLocaleString()}</p>
              </div>
              <div>
                <p className='text-sm font-medium text-muted-foreground'>Record ID</p>
                <p className='text-sm font-mono'>{attendance.id}</p>
              </div>
            </CardContent>
          </Card>

          {/* Actions */}
          <Card>
            <CardHeader>
              <CardTitle>Actions</CardTitle>
            </CardHeader>
            <CardContent className='space-y-3'>
              <Button className='w-full' variant='outline'>
                Generate Report
              </Button>
              <Button className='w-full' variant='outline'>
                Notify Parent
              </Button>
              <Button className='w-full' variant='outline'>
                View History
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
