/**
 * Student Detail Loading Component
 *
 * Professional loading skeleton for the student detail page
 */

import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';

export default function StudentDetailLoading() {
  return (
    <div className='container mx-auto p-4 sm:p-6 lg:p-8 space-y-8'>
      {/* Header Skeleton */}
      <div className='flex items-center justify-between'>
        <div className='flex items-center space-x-4'>
          <Skeleton className='h-9 w-20' />
          <div>
            <Skeleton className='h-8 w-48 mb-2' />
            <Skeleton className='h-4 w-32' />
          </div>
        </div>

        <div className='flex items-center space-x-2'>
          <Skeleton className='h-9 w-20' />
          <Skeleton className='h-9 w-24' />
        </div>
      </div>

      {/* Student Profile Card Skeleton */}
      <Card className='border-0 shadow-lg'>
        <CardContent className='p-6'>
          <div className='flex flex-col md:flex-row md:items-center space-y-4 md:space-y-0 md:space-x-6'>
            <Skeleton className='w-24 h-24 rounded-full mx-auto md:mx-0' />

            <div className='flex-1 text-center md:text-left space-y-2'>
              <Skeleton className='h-8 w-48 mx-auto md:mx-0' />
              <Skeleton className='h-4 w-32 mx-auto md:mx-0' />
              <Skeleton className='h-6 w-20 mx-auto md:mx-0' />
            </div>

            <div className='grid grid-cols-2 gap-4 text-center'>
              <div>
                <Skeleton className='h-8 w-12 mx-auto mb-1' />
                <Skeleton className='h-3 w-8 mx-auto' />
              </div>
              <div>
                <Skeleton className='h-8 w-12 mx-auto mb-1' />
                <Skeleton className='h-3 w-16 mx-auto' />
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Tabs Skeleton */}
      <div className='space-y-6'>
        {/* Tab Navigation Skeleton */}
        <div className='flex space-x-1 bg-gray-100 p-1 rounded-lg'>
          {Array.from({ length: 4 }).map((_, i) => (
            <Skeleton key={i} className='h-10 flex-1' />
          ))}
        </div>

        {/* Tab Content Skeleton */}
        <div className='grid grid-cols-1 lg:grid-cols-2 gap-6'>
          {/* Left Column */}
          <Card>
            <CardHeader>
              <div className='flex items-center gap-2'>
                <Skeleton className='w-5 h-5' />
                <Skeleton className='h-6 w-40' />
              </div>
            </CardHeader>
            <CardContent className='space-y-4'>
              <div className='grid grid-cols-2 gap-4'>
                <div>
                  <Skeleton className='h-4 w-24 mb-1' />
                  <Skeleton className='h-5 w-32' />
                </div>
                <div>
                  <Skeleton className='h-4 w-28 mb-1' />
                  <Skeleton className='h-5 w-28' />
                </div>
              </div>
              <div>
                <Skeleton className='h-4 w-16 mb-1' />
                <Skeleton className='h-5 w-full' />
              </div>
            </CardContent>
          </Card>

          {/* Right Column */}
          <Card>
            <CardHeader>
              <div className='flex items-center gap-2'>
                <Skeleton className='w-5 h-5' />
                <Skeleton className='h-6 w-36' />
              </div>
            </CardHeader>
            <CardContent>
              <div className='space-y-3'>
                {Array.from({ length: 4 }).map((_, i) => (
                  <div key={i} className='flex items-center space-x-3 p-2 rounded-lg bg-gray-50'>
                    <Skeleton className='w-2 h-2 rounded-full' />
                    <div className='flex-1'>
                      <Skeleton className='h-4 w-48 mb-1' />
                      <Skeleton className='h-3 w-24' />
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Additional Content Skeleton */}
        <Card>
          <CardHeader>
            <div className='flex items-center gap-2'>
              <Skeleton className='w-5 h-5' />
              <Skeleton className='h-6 w-44' />
            </div>
            <Skeleton className='h-4 w-64' />
          </CardHeader>
          <CardContent>
            <div className='space-y-4'>
              {Array.from({ length: 5 }).map((_, i) => (
                <div key={i} className='flex items-center justify-between p-4 border rounded-lg'>
                  <div className='flex-1'>
                    <div className='flex items-center justify-between mb-2'>
                      <Skeleton className='h-5 w-24' />
                      <Skeleton className='h-5 w-8' />
                    </div>
                    <Skeleton className='h-2 w-full mb-1' />
                    <Skeleton className='h-3 w-20' />
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
