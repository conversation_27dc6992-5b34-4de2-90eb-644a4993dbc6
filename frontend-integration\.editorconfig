# EditorConfig is awesome: https://EditorConfig.org

# top-most EditorConfig file
root = true

# All files
[*]
charset = utf-8
end_of_line = lf
insert_final_newline = true
trim_trailing_whitespace = true
indent_style = space
indent_size = 2

# JavaScript, TypeScript, JSX, TSX
[*.{js,jsx,ts,tsx}]
indent_style = space
indent_size = 2
max_line_length = 100

# JSON files
[*.json]
indent_style = space
indent_size = 2

# YAML files
[*.{yml,yaml}]
indent_style = space
indent_size = 2

# Markdown files
[*.md]
trim_trailing_whitespace = false
max_line_length = off

# Package.json
[package.json]
indent_style = space
indent_size = 2

# Configuration files
[*.{config,conf}.{js,ts}]
indent_style = space
indent_size = 2

# CSS, SCSS, LESS
[*.{css,scss,less}]
indent_style = space
indent_size = 2

# HTML files
[*.html]
indent_style = space
indent_size = 2

# XML files
[*.xml]
indent_style = space
indent_size = 2

# Shell scripts
[*.sh]
indent_style = space
indent_size = 2

# Batch files
[*.bat]
end_of_line = crlf

# PowerShell files
[*.ps1]
end_of_line = crlf

# Makefiles
[Makefile]
indent_style = tab

# Docker files
[Dockerfile*]
indent_style = space
indent_size = 2

# Environment files
[.env*]
insert_final_newline = true
trim_trailing_whitespace = true
