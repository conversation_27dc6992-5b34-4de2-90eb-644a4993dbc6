'use client';

import React, { useState } from 'react';
import { Bell, Mail, MessageSquare, Smartphone, Send, Eye, Edit2 } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useToast } from '@/hooks/use-toast';

interface NotificationChannel {
  type: 'EMAIL' | 'SMS' | 'IN_APP' | 'PUSH';
  enabled: boolean;
  icon: React.ComponentType<any>;
  label: string;
  description: string;
}

interface NotificationEvent {
  id: string;
  name: string;
  description: string;
  enabled: boolean;
  channels: string[];
  template: string;
  variables: string[];
}

// Mock data
const mockChannels: NotificationChannel[] = [
  {
    type: 'EMAIL',
    enabled: true,
    icon: Mail,
    label: 'Email',
    description: 'Send notifications via email',
  },
  {
    type: 'SMS',
    enabled: false,
    icon: MessageSquare,
    label: 'SMS',
    description: 'Send notifications via SMS',
  },
  {
    type: 'IN_APP',
    enabled: true,
    icon: Bell,
    label: 'In-App',
    description: 'Show notifications in the application',
  },
  {
    type: 'PUSH',
    enabled: true,
    icon: Smartphone,
    label: 'Push',
    description: 'Send push notifications to mobile devices',
  },
];

const mockEvents: NotificationEvent[] = [
  {
    id: '1',
    name: 'Student Absence',
    description: 'Notify parents when student is absent',
    enabled: true,
    channels: ['EMAIL', 'SMS'],
    template: 'Dear {parentName}, your child {studentName} from {class} was absent today ({date}). Please contact the school if this is unexpected.',
    variables: ['parentName', 'studentName', 'class', 'date'],
  },
  {
    id: '2',
    name: 'Fee Due Reminder',
    description: 'Remind parents about pending fee payments',
    enabled: true,
    channels: ['EMAIL', 'IN_APP'],
    template: 'Dear {parentName}, the fee payment for {studentName} ({class}) is due on {dueDate}. Amount: {amount}. Please make the payment to avoid late fees.',
    variables: ['parentName', 'studentName', 'class', 'dueDate', 'amount'],
  },
  {
    id: '3',
    name: 'Exam Schedule',
    description: 'Notify about upcoming exams',
    enabled: true,
    channels: ['EMAIL', 'IN_APP', 'PUSH'],
    template: 'Dear {studentName}, your {examName} exam for {subject} is scheduled on {examDate} at {examTime}. Please be prepared.',
    variables: ['studentName', 'examName', 'subject', 'examDate', 'examTime'],
  },
];

interface NotificationsFormProps {
  onDataChange?: () => void;
}

/**
 * NotificationsForm Component
 * 
 * Features:
 * - Channel toggles (Email/SMS/In-App/Push)
 * - Event management with template editor
 * - Template variables with preview
 * - Send test functionality
 */
export function NotificationsForm({ onDataChange }: NotificationsFormProps) {
  const { toast } = useToast();
  const [channels, setChannels] = useState<NotificationChannel[]>(mockChannels);
  const [events, setEvents] = useState<NotificationEvent[]>(mockEvents);
  const [editingEvent, setEditingEvent] = useState<NotificationEvent | null>(null);
  const [previewData, setPreviewData] = useState<Record<string, string>>({});
  const [testEmail, setTestEmail] = useState('');

  // Handle channel toggle
  const handleChannelToggle = (channelType: string, enabled: boolean) => {
    setChannels(prev => prev.map(channel =>
      channel.type === channelType ? { ...channel, enabled } : channel
    ));
    onDataChange?.();
    
    toast({
      title: `${channelType} ${enabled ? 'enabled' : 'disabled'}`,
      description: `${channelType} notifications have been ${enabled ? 'enabled' : 'disabled'}.`,
    });
  };

  // Handle event toggle
  const handleEventToggle = (eventId: string, enabled: boolean) => {
    setEvents(prev => prev.map(event =>
      event.id === eventId ? { ...event, enabled } : event
    ));
    onDataChange?.();
  };

  // Handle event channel toggle
  const handleEventChannelToggle = (eventId: string, channelType: string, enabled: boolean) => {
    setEvents(prev => prev.map(event => {
      if (event.id === eventId) {
        const channels = enabled
          ? [...event.channels, channelType]
          : event.channels.filter(c => c !== channelType);
        return { ...event, channels };
      }
      return event;
    }));
  };

  // Handle template update
  const handleTemplateUpdate = (template: string) => {
    if (!editingEvent) return;
    
    setEvents(prev => prev.map(event =>
      event.id === editingEvent.id ? { ...event, template } : event
    ));
  };

  // Generate preview text
  const generatePreview = (template: string, variables: string[]) => {
    let preview = template;
    variables.forEach(variable => {
      const value = previewData[variable] || `{${variable}}`;
      preview = preview.replace(new RegExp(`{${variable}}`, 'g'), value);
    });
    return preview;
  };

  // Handle send test
  const handleSendTest = async (event: NotificationEvent) => {
    if (!testEmail) {
      toast({
        title: 'Email required',
        description: 'Please enter an email address to send test notification.',
        variant: 'destructive',
      });
      return;
    }

    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    toast({
      title: 'Test sent',
      description: `Test notification for "${event.name}" sent to ${testEmail}.`,
    });
  };

  // Get channel icon
  const getChannelIcon = (channelType: string) => {
    const channel = channels.find(c => c.type === channelType);
    if (!channel) return Bell;
    return channel.icon;
  };

  return (
    <div className="p-6">
      {/* Header */}
      <div className="mb-6">
        <div className="flex items-center gap-3 mb-2">
          <div className="p-2 bg-gradient-to-r from-sky-50 to-violet-50 rounded-lg">
            <Bell className="h-6 w-6 text-sky-600" />
          </div>
          <div>
            <h2 className="text-2xl font-bold text-slate-900">Notifications</h2>
            <p className="text-slate-600">Configure notification channels and event templates</p>
          </div>
        </div>
      </div>

      <Tabs defaultValue="channels" className="space-y-6">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="channels">Channels</TabsTrigger>
          <TabsTrigger value="events">Events</TabsTrigger>
          <TabsTrigger value="templates">Templates</TabsTrigger>
        </TabsList>

        {/* Channels Tab */}
        <TabsContent value="channels">
          <Card>
            <CardHeader>
              <CardTitle>Notification Channels</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {channels.map((channel) => {
                  const Icon = channel.icon;
                  return (
                    <div key={channel.type} className="flex items-center justify-between p-4 border rounded-lg">
                      <div className="flex items-center gap-3">
                        <div className={`p-2 rounded-lg ${
                          channel.enabled ? 'bg-green-100' : 'bg-gray-100'
                        }`}>
                          <Icon className={`h-5 w-5 ${
                            channel.enabled ? 'text-green-600' : 'text-gray-500'
                          }`} />
                        </div>
                        <div>
                          <h4 className="font-medium">{channel.label}</h4>
                          <p className="text-sm text-slate-600">{channel.description}</p>
                        </div>
                      </div>
                      <Switch
                        checked={channel.enabled}
                        onCheckedChange={(enabled) => handleChannelToggle(channel.type, enabled)}
                      />
                    </div>
                  );
                })}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Events Tab */}
        <TabsContent value="events">
          <Card>
            <CardHeader>
              <CardTitle>Notification Events</CardTitle>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Event</TableHead>
                    <TableHead>Channels</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {events.map((event) => (
                    <TableRow key={event.id}>
                      <TableCell>
                        <div>
                          <h4 className="font-medium">{event.name}</h4>
                          <p className="text-sm text-slate-600">{event.description}</p>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex gap-1">
                          {event.channels.map((channelType) => {
                            const Icon = getChannelIcon(channelType);
                            return (
                              <Badge key={channelType} variant="outline" className="text-xs">
                                <Icon className="h-3 w-3 mr-1" />
                                {channelType}
                              </Badge>
                            );
                          })}
                        </div>
                      </TableCell>
                      <TableCell>
                        <Switch
                          checked={event.enabled}
                          onCheckedChange={(enabled) => handleEventToggle(event.id, enabled)}
                        />
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-1">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => setEditingEvent(event)}
                          >
                            <Edit2 className="h-4 w-4" />
                          </Button>
                          <Dialog>
                            <DialogTrigger asChild>
                              <Button variant="ghost" size="sm">
                                <Send className="h-4 w-4" />
                              </Button>
                            </DialogTrigger>
                            <DialogContent>
                              <DialogHeader>
                                <DialogTitle>Send Test Notification</DialogTitle>
                              </DialogHeader>
                              <div className="space-y-4">
                                <div className="space-y-2">
                                  <Label>Test Email</Label>
                                  <Input
                                    type="email"
                                    value={testEmail}
                                    onChange={(e) => setTestEmail(e.target.value)}
                                    placeholder="Enter email address"
                                  />
                                </div>
                                <div className="space-y-2">
                                  <Label>Preview</Label>
                                  <div className="p-3 bg-slate-50 rounded-lg text-sm">
                                    {generatePreview(event.template, event.variables)}
                                  </div>
                                </div>
                                <div className="flex justify-end gap-2">
                                  <Button variant="outline">Cancel</Button>
                                  <Button onClick={() => handleSendTest(event)}>
                                    Send Test
                                  </Button>
                                </div>
                              </div>
                            </DialogContent>
                          </Dialog>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Templates Tab */}
        <TabsContent value="templates">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Template Editor */}
            <Card>
              <CardHeader>
                <CardTitle>Template Editor</CardTitle>
              </CardHeader>
              <CardContent>
                {editingEvent ? (
                  <div className="space-y-4">
                    <div>
                      <h4 className="font-medium mb-2">{editingEvent.name}</h4>
                      <p className="text-sm text-slate-600 mb-4">{editingEvent.description}</p>
                    </div>
                    
                    <div className="space-y-2">
                      <Label>Template</Label>
                      <Textarea
                        value={editingEvent.template}
                        onChange={(e) => handleTemplateUpdate(e.target.value)}
                        rows={6}
                        placeholder="Enter notification template..."
                      />
                    </div>

                    <div className="space-y-2">
                      <Label>Available Variables</Label>
                      <div className="flex flex-wrap gap-1">
                        {editingEvent.variables.map((variable) => (
                          <Badge key={variable} variant="secondary" className="text-xs">
                            {`{${variable}}`}
                          </Badge>
                        ))}
                      </div>
                    </div>

                    <div className="space-y-2">
                      <Label>Channels</Label>
                      <div className="space-y-2">
                        {channels.filter(c => c.enabled).map((channel) => {
                          const Icon = channel.icon;
                          return (
                            <div key={channel.type} className="flex items-center justify-between">
                              <div className="flex items-center gap-2">
                                <Icon className="h-4 w-4" />
                                <span className="text-sm">{channel.label}</span>
                              </div>
                              <Switch
                                checked={editingEvent.channels.includes(channel.type)}
                                onCheckedChange={(enabled) => 
                                  handleEventChannelToggle(editingEvent.id, channel.type, enabled)
                                }
                              />
                            </div>
                          );
                        })}
                      </div>
                    </div>
                  </div>
                ) : (
                  <div className="text-center py-8 text-slate-500">
                    <Edit2 className="h-12 w-12 mx-auto mb-4 text-slate-300" />
                    <p>Select an event to edit its template</p>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Preview */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Eye className="h-5 w-5" />
                  Preview
                </CardTitle>
              </CardHeader>
              <CardContent>
                {editingEvent ? (
                  <div className="space-y-4">
                    <div className="space-y-3">
                      <Label>Sample Data</Label>
                      {editingEvent.variables.map((variable) => (
                        <div key={variable} className="space-y-1">
                          <Label className="text-xs">{variable}</Label>
                          <Input
                            size="sm"
                            value={previewData[variable] || ''}
                            onChange={(e) => setPreviewData(prev => ({
                              ...prev,
                              [variable]: e.target.value
                            }))}
                            placeholder={`Enter ${variable}`}
                          />
                        </div>
                      ))}
                    </div>

                    <div className="space-y-2">
                      <Label>Preview Output</Label>
                      <div className="p-4 bg-slate-50 rounded-lg border-l-4 border-sky-500">
                        <p className="text-sm whitespace-pre-wrap">
                          {generatePreview(editingEvent.template, editingEvent.variables)}
                        </p>
                      </div>
                    </div>
                  </div>
                ) : (
                  <div className="text-center py-8 text-slate-500">
                    <Eye className="h-12 w-12 mx-auto mb-4 text-slate-300" />
                    <p>Template preview will appear here</p>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}
