/**
 * Session Login Route Handler
 * 
 * Sets httpOnly cookie with JW<PERSON> token for secure authentication
 */

import { NextRequest, NextResponse } from 'next/server';
import { cookies } from 'next/headers';

interface LoginRequest {
  token: string;
}

export async function POST(request: NextRequest) {
  try {
    const body: LoginRequest = await request.json();
    
    if (!body.token) {
      return NextResponse.json(
        { detail: 'Token is required' },
        { status: 400 }
      );
    }
    
    // Parse token to get expiry (optional)
    let maxAge = 24 * 60 * 60; // Default 24 hours
    
    try {
      const tokenParts = body.token.split('.');
      if (tokenParts.length === 3) {
        const payload = JSON.parse(atob(tokenParts[1]));
        if (payload.exp) {
          const expiryTime = payload.exp * 1000; // Convert to milliseconds
          const currentTime = Date.now();
          maxAge = Math.max(0, Math.floor((expiryTime - currentTime) / 1000));
        }
      }
    } catch (error) {
      console.warn('Failed to parse token expiry, using default:', error);
    }
    
    // Set secure httpOnly cookie
    const cookieStore = cookies();
    cookieStore.set('auth_token', body.token, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax',
      path: '/',
      maxAge,
    });
    
    return new NextResponse(null, { status: 204 });
    
  } catch (error) {
    console.error('Session login error:', error);
    return NextResponse.json(
      { detail: 'Failed to set session' },
      { status: 500 }
    );
  }
}
