/**
 * Application Routes Constants
 *
 * Centralized route definitions for:
 * - Type-safe navigation
 * - Route permissions
 * - Breadcrumb generation
 * - Menu structure
 * - Dynamic route building
 */

// ===== ROUTE DEFINITIONS =====

export const ROUTES = {
  // Public routes
  HOME: '/',
  ABOUT: '/about',
  CONTACT: '/contact',

  // Authentication routes
  AUTH: {
    LOGIN: '/login',
    REGISTER: '/register',
    FORGOT_PASSWORD: '/forgot-password',
    RESET_PASSWORD: '/reset-password',
    VERIFY_EMAIL: '/verify-email',
  },

  // Dashboard routes
  DASHBOARD: {
    ROOT: '/dashboard',
    OVERVIEW: '/dashboard',
    PROFILE: '/dashboard/profile',
    SETTINGS: '/dashboard/settings',
    NOTIFICATIONS: '/dashboard/notifications',
  },

  // Student management
  STUDENTS: {
    ROOT: '/dashboard/students',
    LIST: '/dashboard/students',
    CREATE: '/dashboard/students/create',
    DETAIL: (id: string) => `/dashboard/students/${id}`,
    EDIT: (id: string) => `/dashboard/students/${id}/edit`,
    GRADES: (id: string) => `/dashboard/students/${id}/grades`,
    ATTENDANCE: (id: string) => `/dashboard/students/${id}/attendance`,
    REPORTS: (id: string) => `/dashboard/students/${id}/reports`,
  },

  // Teacher management
  TEACHERS: {
    ROOT: '/dashboard/teachers',
    LIST: '/dashboard/teachers',
    CREATE: '/dashboard/teachers/create',
    DETAIL: (id: string) => `/dashboard/teachers/${id}`,
    EDIT: (id: string) => `/dashboard/teachers/${id}/edit`,
    SCHEDULE: (id: string) => `/dashboard/teachers/${id}/schedule`,
    PERFORMANCE: (id: string) => `/dashboard/teachers/${id}/performance`,
    CLASSES: (id: string) => `/dashboard/teachers/${id}/classes`,
  },

  // Class management
  CLASSES: {
    ROOT: '/dashboard/classes',
    LIST: '/dashboard/classes',
    CREATE: '/dashboard/classes/create',
    DETAIL: (id: string) => `/dashboard/classes/${id}`,
    EDIT: (id: string) => `/dashboard/classes/${id}/edit`,
    ROSTER: (id: string) => `/dashboard/classes/${id}/roster`,
    SCHEDULE: (id: string) => `/dashboard/classes/${id}/schedule`,
    ASSIGNMENTS: (id: string) => `/dashboard/classes/${id}/assignments`,
  },

  // Attendance management
  ATTENDANCE: {
    ROOT: '/dashboard/attendance',
    DAILY: '/dashboard/attendance/daily',
    REPORTS: '/dashboard/attendance/reports',
    ANALYTICS: '/dashboard/attendance/analytics',
    MARK: '/dashboard/attendance/mark',
    HISTORY: '/dashboard/attendance/history',
  },

  // Grade management
  GRADES: {
    ROOT: '/dashboard/grades',
    GRADEBOOK: '/dashboard/grades/gradebook',
    ASSIGNMENTS: '/dashboard/grades/assignments',
    REPORTS: '/dashboard/grades/reports',
    ANALYTICS: '/dashboard/grades/analytics',
    TRANSCRIPTS: '/dashboard/grades/transcripts',
  },

  // Fee management
  FEES: {
    ROOT: '/dashboard/fees',
    OVERVIEW: '/dashboard/fees',
    PAYMENTS: '/dashboard/fees/payments',
    INVOICES: '/dashboard/fees/invoices',
    REPORTS: '/dashboard/fees/reports',
    SETTINGS: '/dashboard/fees/settings',
  },

  // Parent portal
  PARENTS: {
    ROOT: '/dashboard/parents',
    LIST: '/dashboard/parents',
    DETAIL: (id: string) => `/dashboard/parents/${id}`,
    COMMUNICATIONS: '/dashboard/parents/communications',
    MEETINGS: '/dashboard/parents/meetings',
  },

  // Communication
  COMMUNICATIONS: {
    ROOT: '/dashboard/communications',
    ANNOUNCEMENTS: '/dashboard/communications/announcements',
    MESSAGES: '/dashboard/communications/messages',
    NOTIFICATIONS: '/dashboard/communications/notifications',
    EVENTS: '/dashboard/communications/events',
  },

  // Reports and analytics
  REPORTS: {
    ROOT: '/dashboard/reports',
    ACADEMIC: '/dashboard/reports/academic',
    ATTENDANCE: '/dashboard/reports/attendance',
    FINANCIAL: '/dashboard/reports/financial',
    CUSTOM: '/dashboard/reports/custom',
    EXPORTS: '/dashboard/reports/exports',
  },

  // System administration
  ADMIN: {
    ROOT: '/dashboard/admin',
    USERS: '/dashboard/admin/users',
    ROLES: '/dashboard/admin/roles',
    PERMISSIONS: '/dashboard/admin/permissions',
    SYSTEM: '/dashboard/admin/system',
    LOGS: '/dashboard/admin/logs',
    BACKUP: '/dashboard/admin/backup',
  },
} as const;

// ===== ROUTE PERMISSIONS =====

export const ROUTE_PERMISSIONS = {
  [ROUTES.DASHBOARD.ROOT]: ['read:dashboard'],
  [ROUTES.STUDENTS.ROOT]: ['read:students'],
  [ROUTES.STUDENTS.CREATE]: ['create:students'],
  [ROUTES.TEACHERS.ROOT]: ['read:teachers'],
  [ROUTES.TEACHERS.CREATE]: ['create:teachers'],
  [ROUTES.CLASSES.ROOT]: ['read:classes'],
  [ROUTES.CLASSES.CREATE]: ['create:classes'],
  [ROUTES.ATTENDANCE.ROOT]: ['read:attendance'],
  [ROUTES.GRADES.ROOT]: ['read:grades'],
  [ROUTES.FEES.ROOT]: ['read:fees'],
  [ROUTES.PARENTS.ROOT]: ['read:parents'],
  [ROUTES.COMMUNICATIONS.ROOT]: ['read:communications'],
  [ROUTES.REPORTS.ROOT]: ['read:reports'],
  [ROUTES.ADMIN.ROOT]: ['admin:access'],
} as const;

// ===== ROLE-BASED ROUTES =====

export const ROLE_ROUTES = {
  ADMIN: [
    ROUTES.DASHBOARD.ROOT,
    ROUTES.STUDENTS.ROOT,
    ROUTES.TEACHERS.ROOT,
    ROUTES.CLASSES.ROOT,
    ROUTES.ATTENDANCE.ROOT,
    ROUTES.GRADES.ROOT,
    ROUTES.FEES.ROOT,
    ROUTES.PARENTS.ROOT,
    ROUTES.COMMUNICATIONS.ROOT,
    ROUTES.REPORTS.ROOT,
    ROUTES.ADMIN.ROOT,
  ],
  TEACHER: [
    ROUTES.DASHBOARD.ROOT,
    ROUTES.STUDENTS.ROOT,
    ROUTES.CLASSES.ROOT,
    ROUTES.ATTENDANCE.ROOT,
    ROUTES.GRADES.ROOT,
    ROUTES.COMMUNICATIONS.ROOT,
  ],
  STUDENT: [
    ROUTES.DASHBOARD.ROOT,
    ROUTES.GRADES.ROOT,
    ROUTES.ATTENDANCE.ROOT,
    ROUTES.COMMUNICATIONS.ROOT,
  ],
  PARENT: [
    ROUTES.DASHBOARD.ROOT,
    ROUTES.STUDENTS.ROOT,
    ROUTES.GRADES.ROOT,
    ROUTES.ATTENDANCE.ROOT,
    ROUTES.COMMUNICATIONS.ROOT,
    ROUTES.FEES.ROOT,
  ],
} as const;

// ===== BREADCRUMB CONFIGURATION =====

export const BREADCRUMB_CONFIG = {
  [ROUTES.DASHBOARD.ROOT]: { label: 'Dashboard', icon: 'Home' },
  [ROUTES.STUDENTS.ROOT]: { label: 'Students', icon: 'Users' },
  [ROUTES.STUDENTS.CREATE]: { label: 'Add Student', icon: 'UserPlus' },
  [ROUTES.TEACHERS.ROOT]: { label: 'Teachers', icon: 'GraduationCap' },
  [ROUTES.TEACHERS.CREATE]: { label: 'Add Teacher', icon: 'UserPlus' },
  [ROUTES.CLASSES.ROOT]: { label: 'Classes', icon: 'BookOpen' },
  [ROUTES.CLASSES.CREATE]: { label: 'Create Class', icon: 'Plus' },
  [ROUTES.ATTENDANCE.ROOT]: { label: 'Attendance', icon: 'Calendar' },
  [ROUTES.GRADES.ROOT]: { label: 'Grades', icon: 'Award' },
  [ROUTES.FEES.ROOT]: { label: 'Fees', icon: 'DollarSign' },
  [ROUTES.PARENTS.ROOT]: { label: 'Parents', icon: 'Users' },
  [ROUTES.COMMUNICATIONS.ROOT]: { label: 'Communications', icon: 'MessageSquare' },
  [ROUTES.REPORTS.ROOT]: { label: 'Reports', icon: 'BarChart3' },
  [ROUTES.ADMIN.ROOT]: { label: 'Administration', icon: 'Settings' },
} as const;

// ===== NAVIGATION MENU STRUCTURE =====

export const NAVIGATION_MENU = [
  {
    label: 'Dashboard',
    href: ROUTES.DASHBOARD.ROOT,
    icon: 'Home',
    roles: ['ADMIN', 'TEACHER', 'STUDENT', 'PARENT'],
  },
  {
    label: 'Academic',
    icon: 'BookOpen',
    roles: ['ADMIN', 'TEACHER'],
    children: [
      {
        label: 'Students',
        href: ROUTES.STUDENTS.ROOT,
        icon: 'Users',
        roles: ['ADMIN', 'TEACHER'],
      },
      {
        label: 'Teachers',
        href: ROUTES.TEACHERS.ROOT,
        icon: 'GraduationCap',
        roles: ['ADMIN'],
      },
      {
        label: 'Classes',
        href: ROUTES.CLASSES.ROOT,
        icon: 'BookOpen',
        roles: ['ADMIN', 'TEACHER'],
      },
    ],
  },
  {
    label: 'Assessment',
    icon: 'Award',
    roles: ['ADMIN', 'TEACHER', 'STUDENT', 'PARENT'],
    children: [
      {
        label: 'Attendance',
        href: ROUTES.ATTENDANCE.ROOT,
        icon: 'Calendar',
        roles: ['ADMIN', 'TEACHER', 'STUDENT', 'PARENT'],
      },
      {
        label: 'Grades',
        href: ROUTES.GRADES.ROOT,
        icon: 'Award',
        roles: ['ADMIN', 'TEACHER', 'STUDENT', 'PARENT'],
      },
    ],
  },
  {
    label: 'Management',
    icon: 'Settings',
    roles: ['ADMIN'],
    children: [
      {
        label: 'Fees',
        href: ROUTES.FEES.ROOT,
        icon: 'DollarSign',
        roles: ['ADMIN', 'PARENT'],
      },
      {
        label: 'Parents',
        href: ROUTES.PARENTS.ROOT,
        icon: 'Users',
        roles: ['ADMIN', 'TEACHER'],
      },
      {
        label: 'Communications',
        href: ROUTES.COMMUNICATIONS.ROOT,
        icon: 'MessageSquare',
        roles: ['ADMIN', 'TEACHER'],
      },
    ],
  },
  {
    label: 'Reports',
    href: ROUTES.REPORTS.ROOT,
    icon: 'BarChart3',
    roles: ['ADMIN', 'TEACHER'],
  },
  {
    label: 'Administration',
    href: ROUTES.ADMIN.ROOT,
    icon: 'Shield',
    roles: ['ADMIN'],
  },
] as const;

// ===== UTILITY FUNCTIONS =====

export const routeUtils = {
  // Check if user has access to route
  hasAccess: (route: string, userRole: string, userPermissions: string[] = []): boolean => {
    const roleRoutes = ROLE_ROUTES[userRole as keyof typeof ROLE_ROUTES];
    if (!roleRoutes) {
      return false;
    }

    // Check role-based access
    const hasRoleAccess = roleRoutes.some(allowedRoute => route.startsWith(allowedRoute));

    if (!hasRoleAccess) {
      return false;
    }

    // Check permission-based access
    const requiredPermissions = ROUTE_PERMISSIONS[route as keyof typeof ROUTE_PERMISSIONS];
    if (requiredPermissions) {
      return requiredPermissions.some(permission => userPermissions.includes(permission));
    }

    return true;
  },

  // Generate breadcrumbs for a route
  generateBreadcrumbs: (
    pathname: string
  ): Array<{ label: string; href: string; icon?: string }> => {
    const segments = pathname.split('/').filter(Boolean);
    const breadcrumbs = [];
    let currentPath = '';

    for (const segment of segments) {
      currentPath += `/${segment}`;
      const config = BREADCRUMB_CONFIG[currentPath as keyof typeof BREADCRUMB_CONFIG];

      if (config) {
        breadcrumbs.push({
          label: config.label,
          href: currentPath,
          icon: config.icon,
        });
      } else {
        // Handle dynamic routes
        const dynamicLabel = segment.charAt(0).toUpperCase() + segment.slice(1);
        breadcrumbs.push({
          label: dynamicLabel,
          href: currentPath,
        });
      }
    }

    return breadcrumbs;
  },

  // Get filtered navigation menu based on user role
  getNavigationMenu: (userRole: string) => {
    return NAVIGATION_MENU.filter(item => item.roles.includes(userRole as any)).map(item => {
      const newItem = { ...item } as any;
      if ('children' in newItem && newItem.children) {
        newItem.children = newItem.children.filter((child: any) =>
          child.roles.includes(userRole as any)
        );
      }
      return newItem;
    }) as any;
  },

  // Check if route is active
  isActiveRoute: (currentPath: string, routePath: string): boolean => {
    if (routePath === ROUTES.DASHBOARD.ROOT) {
      return currentPath === routePath;
    }
    return currentPath.startsWith(routePath);
  },

  // Get parent route
  getParentRoute: (pathname: string): string => {
    const segments = pathname.split('/').filter(Boolean);
    if (segments.length <= 2) {
      return ROUTES.DASHBOARD.ROOT;
    }

    segments.pop();
    return `/${segments.join('/')}`;
  },
};

// Export route builder helpers
export const buildRoute = {
  student: {
    detail: (id: string) => ROUTES.STUDENTS.DETAIL(id),
    edit: (id: string) => ROUTES.STUDENTS.EDIT(id),
    grades: (id: string) => ROUTES.STUDENTS.GRADES(id),
    attendance: (id: string) => ROUTES.STUDENTS.ATTENDANCE(id),
  },
  teacher: {
    detail: (id: string) => ROUTES.TEACHERS.DETAIL(id),
    edit: (id: string) => ROUTES.TEACHERS.EDIT(id),
    schedule: (id: string) => ROUTES.TEACHERS.SCHEDULE(id),
    performance: (id: string) => ROUTES.TEACHERS.PERFORMANCE(id),
  },
  class: {
    detail: (id: string) => ROUTES.CLASSES.DETAIL(id),
    edit: (id: string) => ROUTES.CLASSES.EDIT(id),
    roster: (id: string) => ROUTES.CLASSES.ROSTER(id),
    schedule: (id: string) => ROUTES.CLASSES.SCHEDULE(id),
  },
};

export default ROUTES;
