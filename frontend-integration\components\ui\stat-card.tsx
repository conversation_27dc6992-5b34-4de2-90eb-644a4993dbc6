/**
 * Stat Card Component
 * 
 * Reusable statistics card with consistent styling and animations
 */

import { ReactNode } from 'react';
import { LucideIcon, TrendingUp, TrendingDown } from 'lucide-react';

import { Card, CardContent } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';

export interface StatCardProps {
  title: string;
  value: string | number;
  subtitle?: string;
  icon: LucideIcon;
  trend?: {
    value: string;
    direction: 'up' | 'down';
  };
  progress?: {
    value: number;
    max?: number;
    label?: string;
  };
  color: 'blue' | 'green' | 'purple' | 'orange' | 'indigo' | 'red' | 'yellow' | 'pink' | 'cyan';
  className?: string;
  onClick?: () => void;
}

const colorVariants = {
  blue: {
    gradient: 'from-blue-50 to-blue-100',
    text: 'text-blue-700',
    valueText: 'text-blue-900',
    icon: 'bg-blue-500',
    progress: 'bg-blue-200',
    progressText: 'text-blue-600',
  },
  green: {
    gradient: 'from-green-50 to-green-100',
    text: 'text-green-700',
    valueText: 'text-green-900',
    icon: 'bg-green-500',
    progress: 'bg-green-200',
    progressText: 'text-green-600',
  },
  purple: {
    gradient: 'from-purple-50 to-purple-100',
    text: 'text-purple-700',
    valueText: 'text-purple-900',
    icon: 'bg-purple-500',
    progress: 'bg-purple-200',
    progressText: 'text-purple-600',
  },
  orange: {
    gradient: 'from-orange-50 to-orange-100',
    text: 'text-orange-700',
    valueText: 'text-orange-900',
    icon: 'bg-orange-500',
    progress: 'bg-orange-200',
    progressText: 'text-orange-600',
  },
  indigo: {
    gradient: 'from-indigo-50 to-indigo-100',
    text: 'text-indigo-700',
    valueText: 'text-indigo-900',
    icon: 'bg-indigo-500',
    progress: 'bg-indigo-200',
    progressText: 'text-indigo-600',
  },
  red: {
    gradient: 'from-red-50 to-red-100',
    text: 'text-red-700',
    valueText: 'text-red-900',
    icon: 'bg-red-500',
    progress: 'bg-red-200',
    progressText: 'text-red-600',
  },
  yellow: {
    gradient: 'from-yellow-50 to-yellow-100',
    text: 'text-yellow-700',
    valueText: 'text-yellow-900',
    icon: 'bg-yellow-500',
    progress: 'bg-yellow-200',
    progressText: 'text-yellow-600',
  },
  pink: {
    gradient: 'from-pink-50 to-pink-100',
    text: 'text-pink-700',
    valueText: 'text-pink-900',
    icon: 'bg-pink-500',
    progress: 'bg-pink-200',
    progressText: 'text-pink-600',
  },
  cyan: {
    gradient: 'from-cyan-50 to-cyan-100',
    text: 'text-cyan-700',
    valueText: 'text-cyan-900',
    icon: 'bg-cyan-500',
    progress: 'bg-cyan-200',
    progressText: 'text-cyan-600',
  },
};

export function StatCard({
  title,
  value,
  subtitle,
  icon: Icon,
  trend,
  progress,
  color,
  className,
  onClick,
}: StatCardProps) {
  const colorConfig = colorVariants[color];

  return (
    <Card 
      className={cn(
        'relative overflow-hidden border-0 shadow-lg hover:shadow-xl transition-all duration-300',
        `bg-gradient-to-br ${colorConfig.gradient}`,
        onClick && 'cursor-pointer hover:scale-105',
        className
      )}
      onClick={onClick}
    >
      <CardContent className="p-6">
        <div className="flex items-center justify-between">
          <div className="flex-1">
            <p className={cn('text-sm font-medium', colorConfig.text)}>
              {title}
            </p>
            <p className={cn('text-3xl font-bold mt-1', colorConfig.valueText)}>
              {value}
            </p>
            {subtitle && (
              <p className={cn('text-xs mt-1 opacity-80', colorConfig.text)}>
                {subtitle}
              </p>
            )}
            {trend && (
              <div className="flex items-center mt-2">
                {trend.direction === 'up' ? (
                  <TrendingUp className="h-3 w-3 text-green-500 mr-1" />
                ) : (
                  <TrendingDown className="h-3 w-3 text-red-500 mr-1" />
                )}
                <span className={cn(
                  'text-xs font-medium',
                  trend.direction === 'up' ? 'text-green-600' : 'text-red-600'
                )}>
                  {trend.value}
                </span>
              </div>
            )}
          </div>
          <div className={cn('p-3 rounded-full shadow-lg', colorConfig.icon)}>
            <Icon className="h-6 w-6 text-white" />
          </div>
        </div>
        
        {progress && (
          <div className="mt-4">
            <div className={cn('flex justify-between text-xs mb-1', colorConfig.progressText)}>
              <span>{progress.label || 'Progress'}</span>
              <span>{progress.value}{progress.max ? `/${progress.max}` : '%'}</span>
            </div>
            <Progress 
              value={progress.max ? (progress.value / progress.max) * 100 : progress.value} 
              className={cn('h-2', colorConfig.progress)}
            />
          </div>
        )}
      </CardContent>
    </Card>
  );
}
