/**
 * Test Student API with Authentication
 * 
 * This script gets a valid token and tests the student endpoints
 */

const API_BASE_URL = 'http://127.0.0.1:8000';

async function testStudentAPIWithAuth() {
  console.log('🧪 Testing Student API with Authentication...\n');

  let authToken = null;

  try {
    // Step 1: Get authentication token
    console.log('1. Getting authentication token...');
    
    const formData = new URLSearchParams();
    formData.append('username', 'admin');
    formData.append('password', 'admin123');
    
    const loginResponse = await fetch(`${API_BASE_URL}/api/v1/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: formData
    });

    if (loginResponse.ok) {
      const loginData = await loginResponse.json();
      authToken = loginData.access_token;
      console.log('✅ Authentication successful');
      console.log('   Token type:', loginData.token_type);
      console.log('   Token preview:', authToken ? authToken.substring(0, 20) + '...' : 'None');
    } else {
      console.log('❌ Authentication failed');
      console.log('   Status:', loginResponse.status);
      const errorText = await loginResponse.text();
      console.log('   Error:', errorText);
      return;
    }

  } catch (error) {
    console.log('❌ Authentication error:', error.message);
    return;
  }

  console.log('\n' + '='.repeat(50) + '\n');

  try {
    // Step 2: Test GET students with auth
    console.log('2. Testing GET /students with authentication...');
    
    const studentsResponse = await fetch(`${API_BASE_URL}/api/v1/students?page=1&size=5`, {
      headers: {
        'Authorization': `Bearer ${authToken}`,
        'Content-Type': 'application/json'
      }
    });

    console.log('   Status:', studentsResponse.status);
    
    if (studentsResponse.ok) {
      const studentsData = await studentsResponse.json();
      console.log('✅ GET /students successful');
      console.log('   Response structure:', Object.keys(studentsData));
      console.log('   Total students:', studentsData.total || 0);
      console.log('   Items count:', studentsData.items ? studentsData.items.length : 0);
      
      if (studentsData.items && studentsData.items.length > 0) {
        console.log('   Sample student fields:', Object.keys(studentsData.items[0]));
      }
    } else {
      const errorText = await studentsResponse.text();
      console.log('❌ GET /students failed');
      console.log('   Error:', errorText);
    }

  } catch (error) {
    console.log('❌ GET /students error:', error.message);
  }

  console.log('\n' + '='.repeat(50) + '\n');

  try {
    // Step 3: Test POST students with auth
    console.log('3. Testing POST /students with authentication...');
    
    const testStudent = {
      username: 'test001',
      admission_number: 'TEST001',
      name: 'Test',
      surname: 'Student',
      sex: 'male',
      date_of_birth: '2005-01-01',
      class_id: 1,
      grade_id: 1,
      guardian_name: 'Test Guardian',
      guardian_phone: '+**********',
      address: '123 Test Street',
      email: '<EMAIL>',
      password: 'TestPassword123',
      parent_id: 'test-parent-id',
      phone: null,
      img: null,
      blood_type: null,
      emergency_contact: null,
      medical_info: null,
      nationality: null,
      religion: null,
      roll_number: null,
      previous_school: null
    };
    
    console.log('   Sending payload:', JSON.stringify(testStudent, null, 2));
    
    const createResponse = await fetch(`${API_BASE_URL}/api/v1/students`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${authToken}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(testStudent)
    });

    console.log('   Status:', createResponse.status);
    
    if (createResponse.ok) {
      const createdStudent = await createResponse.json();
      console.log('✅ POST /students successful');
      console.log('   Created student:', JSON.stringify(createdStudent, null, 2));
    } else {
      const errorText = await createResponse.text();
      console.log('❌ POST /students failed');
      console.log('   Error:', errorText);
    }

  } catch (error) {
    console.log('❌ POST /students error:', error.message);
  }
}

// Run the test
testStudentAPIWithAuth().catch(console.error);
