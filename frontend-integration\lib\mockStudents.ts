/**
 * Mock Students Data
 *
 * Comprehensive mock data for students with:
 * - Realistic student profiles
 * - Multiple grades and classes
 * - Parent contact information
 * - Enrollment status tracking
 * - Academic performance data
 */

import { Student } from '@/schemas/zodSchemas';

// Sample student data
export const mockStudents: Student[] = [
  {
    id: '1',
    name: '<PERSON>',
    email: '<EMAIL>',
    phone: '(*************',
    grade: 'Grade 10',
    class: '10A',
    status: 'ACTIVE',
    enrollment_date: '2023-09-01',
    parent_contact: '(*************',
    created_at: '2023-09-01T08:00:00Z',
    updated_at: '2024-01-15T10:30:00Z',
  },
  {
    id: '2',
    name: '<PERSON>',
    email: '<EMAIL>',
    phone: '(*************',
    grade: 'Grade 11',
    class: '11B',
    status: 'ACTIVE',
    enrollment_date: '2022-09-01',
    parent_contact: '(*************',
    created_at: '2022-09-01T08:00:00Z',
    updated_at: '2024-01-10T14:20:00Z',
  },
  {
    id: '3',
    name: '<PERSON>',
    email: 'sophia.rod<PERSON><EMAIL>',
    phone: '(*************',
    grade: 'Grade 9',
    class: '9C',
    status: 'ACTIVE',
    enrollment_date: '2024-09-01',
    parent_contact: '(*************',
    created_at: '2024-09-01T08:00:00Z',
    updated_at: '2024-01-20T09:15:00Z',
  },
  {
    id: '4',
    name: 'James Wilson',
    email: '<EMAIL>',
    phone: '(*************',
    grade: 'Grade 12',
    class: '12A',
    status: 'ACTIVE',
    enrollment_date: '2021-09-01',
    parent_contact: '(*************',
    created_at: '2021-09-01T08:00:00Z',
    updated_at: '2024-01-18T11:45:00Z',
  },
  {
    id: '5',
    name: 'Olivia Brown',
    email: '<EMAIL>',
    phone: '(*************',
    grade: 'Grade 10',
    class: '10B',
    status: 'ACTIVE',
    enrollment_date: '2023-09-01',
    parent_contact: '(*************',
    created_at: '2023-09-01T08:00:00Z',
    updated_at: '2024-01-12T16:30:00Z',
  },
  {
    id: '6',
    name: 'Alexander Davis',
    email: '<EMAIL>',
    phone: '(*************',
    grade: 'Grade 11',
    class: '11A',
    status: 'ACTIVE',
    enrollment_date: '2022-09-01',
    parent_contact: '(*************',
    created_at: '2022-09-01T08:00:00Z',
    updated_at: '2024-01-14T13:20:00Z',
  },
  {
    id: '7',
    name: 'Isabella Martinez',
    email: '<EMAIL>',
    phone: '(*************',
    grade: 'Grade 9',
    class: '9A',
    status: 'ACTIVE',
    enrollment_date: '2024-09-01',
    parent_contact: '(*************',
    created_at: '2024-09-01T08:00:00Z',
    updated_at: '2024-01-16T10:10:00Z',
  },
  {
    id: '8',
    name: 'William Garcia',
    email: '<EMAIL>',
    phone: '(*************',
    grade: 'Grade 12',
    class: '12B',
    status: 'ACTIVE',
    enrollment_date: '2021-09-01',
    parent_contact: '(*************',
    created_at: '2021-09-01T08:00:00Z',
    updated_at: '2024-01-19T15:45:00Z',
  },
  {
    id: '9',
    name: 'Ava Thompson',
    email: '<EMAIL>',
    phone: '(*************',
    grade: 'Grade 10',
    class: '10C',
    status: 'ACTIVE',
    enrollment_date: '2023-09-01',
    parent_contact: '(*************',
    created_at: '2023-09-01T08:00:00Z',
    updated_at: '2024-01-11T12:25:00Z',
  },
  {
    id: '10',
    name: 'Ethan Anderson',
    email: '<EMAIL>',
    phone: '(*************',
    grade: 'Grade 11',
    class: '11C',
    status: 'ACTIVE',
    enrollment_date: '2022-09-01',
    parent_contact: '(*************',
    created_at: '2022-09-01T08:00:00Z',
    updated_at: '2024-01-13T14:55:00Z',
  },
  {
    id: '11',
    name: 'Mia Taylor',
    email: '<EMAIL>',
    phone: '(*************',
    grade: 'Grade 9',
    class: '9B',
    status: 'TRANSFERRED',
    enrollment_date: '2024-09-01',
    parent_contact: '(*************',
    created_at: '2024-09-01T08:00:00Z',
    updated_at: '2024-01-17T09:30:00Z',
  },
  {
    id: '12',
    name: 'Benjamin Lee',
    email: '<EMAIL>',
    phone: '(*************',
    grade: 'Grade 12',
    class: '12A',
    status: 'GRADUATED',
    enrollment_date: '2021-09-01',
    parent_contact: '(*************',
    created_at: '2021-09-01T08:00:00Z',
    updated_at: '2024-01-21T11:00:00Z',
  },
];

// Available grades and classes
export const mockGrades = ['All Grades', 'Grade 9', 'Grade 10', 'Grade 11', 'Grade 12'];

export const mockClassNames = [
  'All Classes',
  '9A',
  '9B',
  '9C',
  '10A',
  '10B',
  '10C',
  '11A',
  '11B',
  '11C',
  '12A',
  '12B',
];

export const mockStudentStatuses = ['All Status', 'ACTIVE', 'INACTIVE', 'TRANSFERRED', 'GRADUATED'];

// Helper function to get student by ID
export const getMockStudentById = (id: string): Student | undefined => {
  return mockStudents.find(student => student.id === id);
};

// Helper function to filter students
export const filterMockStudents = (filters: {
  search?: string;
  grade?: string;
  class?: string;
  status?: string;
}): Student[] => {
  let filtered = [...mockStudents];

  if (filters.search) {
    const searchLower = filters.search.toLowerCase();
    filtered = filtered.filter(
      student =>
        student.name.toLowerCase().includes(searchLower) ||
        student.email.toLowerCase().includes(searchLower) ||
        student.grade.toLowerCase().includes(searchLower) ||
        student.class.toLowerCase().includes(searchLower)
    );
  }

  if (filters.grade && filters.grade !== 'All Grades') {
    filtered = filtered.filter(student => student.grade === filters.grade);
  }

  if (filters.class && filters.class !== 'All Classes') {
    filtered = filtered.filter(student => student.class === filters.class);
  }

  if (filters.status && filters.status !== 'All Status') {
    filtered = filtered.filter(student => student.status === filters.status);
  }

  return filtered;
};

// Helper function to paginate students
export const paginateMockStudents = (
  students: Student[],
  page: number = 1,
  pageSize: number = 10
) => {
  const startIndex = (page - 1) * pageSize;
  const endIndex = startIndex + pageSize;

  return {
    data: students.slice(startIndex, endIndex),
    total: students.length,
    page,
    pageSize,
    totalPages: Math.ceil(students.length / pageSize),
  };
};

// Student statistics
export const mockStudentStats = {
  total: mockStudents.length,
  active: mockStudents.filter(s => s.status === 'ACTIVE').length,
  graduated: mockStudents.filter(s => s.status === 'GRADUATED').length,
  transferred: mockStudents.filter(s => s.status === 'TRANSFERRED').length,
  grades: new Set(mockStudents.map(s => s.grade)).size,
  classes: new Set(mockStudents.map(s => s.class)).size,
};

// CRUD operations for mock data
let mutableMockStudents = [...mockStudents];

export const createMockStudent = (
  studentData: Omit<Student, 'id' | 'created_at' | 'updated_at'>
): Student => {
  const newStudent: Student = {
    id: Math.random().toString(36).substr(2, 9),
    ...studentData,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
  };

  mutableMockStudents.push(newStudent);
  return newStudent;
};

export const updateMockStudent = (id: string, updates: Partial<Student>): Student | null => {
  const index = mutableMockStudents.findIndex(student => student.id === id);
  if (index === -1) return null;

  const updatedStudent = {
    ...mutableMockStudents[index],
    ...updates,
    updated_at: new Date().toISOString(),
  };

  mutableMockStudents[index] = updatedStudent;
  return updatedStudent;
};

export const deleteMockStudent = (id: string): boolean => {
  const index = mutableMockStudents.findIndex(student => student.id === id);
  if (index === -1) return false;

  mutableMockStudents.splice(index, 1);
  return true;
};

export const resetMockStudents = (): void => {
  mutableMockStudents = [...mockStudents];
};

export const getMutableMockStudents = (): Student[] => {
  return [...mutableMockStudents];
};
