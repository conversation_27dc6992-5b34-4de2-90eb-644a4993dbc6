/**
 * Activity Card Component
 * 
 * Reusable activity/notification card with icons and timestamps
 */

import { ReactNode } from 'react';
import { Clock, LucideIcon } from 'lucide-react';

import { cn } from '@/lib/utils';

export interface ActivityCardProps {
  id: string | number;
  title: string;
  description: string;
  time: string;
  icon: LucideIcon;
  type: 'student' | 'teacher' | 'exam' | 'attendance' | 'fee' | 'announcement' | 'event' | 'general';
  className?: string;
  onClick?: () => void;
}

const typeVariants = {
  student: {
    color: 'bg-blue-50 border-blue-200',
    iconColor: 'text-blue-500',
  },
  teacher: {
    color: 'bg-green-50 border-green-200',
    iconColor: 'text-green-500',
  },
  exam: {
    color: 'bg-purple-50 border-purple-200',
    iconColor: 'text-purple-500',
  },
  attendance: {
    color: 'bg-emerald-50 border-emerald-200',
    iconColor: 'text-emerald-500',
  },
  fee: {
    color: 'bg-orange-50 border-orange-200',
    iconColor: 'text-orange-500',
  },
  announcement: {
    color: 'bg-red-50 border-red-200',
    iconColor: 'text-red-500',
  },
  event: {
    color: 'bg-indigo-50 border-indigo-200',
    iconColor: 'text-indigo-500',
  },
  general: {
    color: 'bg-slate-50 border-slate-200',
    iconColor: 'text-slate-500',
  },
};

export function ActivityCard({
  id,
  title,
  description,
  time,
  icon: Icon,
  type,
  className,
  onClick,
}: ActivityCardProps) {
  const typeConfig = typeVariants[type];

  return (
    <div 
      className={cn(
        'flex items-start space-x-4 p-4 rounded-xl border transition-all duration-200',
        'hover:shadow-md hover:scale-[1.02] cursor-pointer',
        typeConfig.color,
        className
      )}
      onClick={onClick}
    >
      <div className="p-2 bg-white rounded-lg shadow-sm">
        <Icon className={cn('h-4 w-4', typeConfig.iconColor)} />
      </div>
      
      <div className="flex-1 min-w-0">
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <p className="font-medium text-slate-900 leading-tight">{title}</p>
            <p className="text-sm text-slate-600 mt-1 leading-relaxed">{description}</p>
          </div>
        </div>
        
        <div className="flex items-center mt-3">
          <Clock className="h-3 w-3 text-slate-400 mr-1" />
          <span className="text-xs text-slate-500 font-medium">{time}</span>
        </div>
      </div>
    </div>
  );
}

/**
 * Activity List Component
 * 
 * Container for multiple activity cards with consistent spacing
 */
export interface ActivityListProps {
  activities: ActivityCardProps[];
  className?: string;
  onActivityClick?: (activity: ActivityCardProps) => void;
}

export function ActivityList({ 
  activities, 
  className,
  onActivityClick 
}: ActivityListProps) {
  return (
    <div className={cn('space-y-3', className)}>
      {activities.map((activity) => (
        <ActivityCard
          key={activity.id}
          {...activity}
          onClick={() => onActivityClick?.(activity)}
        />
      ))}
    </div>
  );
}
