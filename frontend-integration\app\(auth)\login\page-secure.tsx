/**
 * Secure Login Page - React Hook Form + Zod Implementation
 *
 * Features:
 * - React Hook Form with Zod validation
 * - Real-time form validation
 * - Secure token storage
 * - Proper error handling
 * - Loading states
 * - Accessibility compliant
 * - Responsive design
 */

'use client';

import { zodResolver } from '@hookform/resolvers/zod';
import { Eye, EyeOff, Loader2, Lock, Mail } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { useState } from 'react';
import { useForm } from 'react-hook-form';

// Components
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';

// Hooks and utilities
import { useAuth } from '@/hooks/useAuth';

// Schemas and types
import { LoginSchema, type LoginFormData } from '@/schemas/zodSchemas';

// Demo credentials for development
const DEMO_CREDENTIALS = {
  email: '<EMAIL>',
  password: 'admin123',
  user: {
    id: '1',
    email: '<EMAIL>',
    first_name: 'Admin',
    last_name: 'User',
    role: 'ADMIN' as const,
    is_active: true,
  },
  access_token: 'demo-jwt-token-12345',
  token_type: 'Bearer',
  expires_in: 3600,
};

const USE_DEMO_AUTH = true; // TODO: Set to false when backend is ready

export default function SecureLoginPage() {
  const [showPassword, setShowPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [serverError, setServerError] = useState<string>('');

  const { login } = useAuth();
  const router = useRouter();

  // Initialize form with React Hook Form + Zod
  const form = useForm<LoginFormData>({
    resolver: zodResolver(LoginSchema),
    defaultValues: {
      email: USE_DEMO_AUTH ? DEMO_CREDENTIALS.email : '',
      password: USE_DEMO_AUTH ? DEMO_CREDENTIALS.password : '',
    },
    mode: 'onChange', // Real-time validation
  });

  // Handle form submission
  const onSubmit = async (data: LoginFormData) => {
    setIsLoading(true);
    setServerError('');

    try {
      if (USE_DEMO_AUTH) {
        // Demo authentication
        await new Promise(resolve => setTimeout(resolve, 1000)); // Simulate API delay

        if (data.email === DEMO_CREDENTIALS.email && data.password === DEMO_CREDENTIALS.password) {
          await login({ email: data.email, password: data.password });
          router.push('/dashboard');
        } else {
          throw new Error('Invalid email or password');
        }
      } else {
        // Real API authentication
        await login({ email: data.email, password: data.password });
        router.push('/dashboard');
      }
    } catch (error: any) {
      console.error('Login error:', error);

      // Handle different error types
      if (error.response?.status === 401) {
        setServerError('Invalid email or password');
      } else if (error.response?.status === 429) {
        setServerError('Too many login attempts. Please try again later.');
      } else if (error.response?.data?.message) {
        setServerError(error.response.data.message);
      } else {
        setServerError(error.message || 'Login failed. Please try again.');
      }
    } finally {
      setIsLoading(false);
    }
  };

  // Auto-fill demo credentials
  const fillDemoCredentials = () => {
    form.setValue('email', DEMO_CREDENTIALS.email);
    form.setValue('password', DEMO_CREDENTIALS.password);
  };

  return (
    <div className='container mx-auto flex items-center justify-center min-h-screen p-4'>
      <Card className='w-full max-w-md'>
        <CardHeader className='space-y-1 text-center'>
          <div className='mx-auto h-12 w-12 bg-primary rounded-full flex items-center justify-center mb-4'>
            <span className='text-primary-foreground font-bold text-xl'>🎓</span>
          </div>
          <CardTitle className='text-2xl font-bold'>Welcome Back</CardTitle>
          <CardDescription>Sign in to your school management account</CardDescription>
        </CardHeader>

        <CardContent className='space-y-4'>
          {/* Demo credentials banner */}
          {USE_DEMO_AUTH && (
            <Card className='bg-yellow-50 border-yellow-200'>
              <CardContent className='p-4'>
                <div className='flex items-center justify-between'>
                  <div>
                    <Badge variant='warning' className='mb-2'>
                      Demo Mode
                    </Badge>
                    <p className='text-sm text-yellow-800'>
                      Email: {DEMO_CREDENTIALS.email}
                      <br />
                      Password: {DEMO_CREDENTIALS.password}
                    </p>
                  </div>
                  <Button type='button' variant='outline' size='sm' onClick={fillDemoCredentials}>
                    Auto-fill
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Login Form */}
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className='space-y-4'>
              {/* Email Field */}
              <FormField
                control={form.control}
                name='email'
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Email Address</FormLabel>
                    <FormControl>
                      <div className='relative'>
                        <Mail className='absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4' />
                        <Input
                          {...field}
                          type='email'
                          placeholder='Enter your email'
                          className='pl-10'
                          disabled={isLoading}
                        />
                      </div>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Password Field */}
              <FormField
                control={form.control}
                name='password'
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Password</FormLabel>
                    <FormControl>
                      <div className='relative'>
                        <Lock className='absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4' />
                        <Input
                          {...field}
                          type={showPassword ? 'text' : 'password'}
                          placeholder='Enter your password'
                          className='pl-10 pr-10'
                          disabled={isLoading}
                        />
                        <Button
                          type='button'
                          variant='ghost'
                          size='sm'
                          className='absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent'
                          onClick={() => setShowPassword(!showPassword)}
                          disabled={isLoading}
                        >
                          {showPassword ? (
                            <EyeOff className='h-4 w-4 text-muted-foreground' />
                          ) : (
                            <Eye className='h-4 w-4 text-muted-foreground' />
                          )}
                        </Button>
                      </div>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Server Error */}
              {serverError && (
                <div className='p-3 text-sm text-destructive bg-destructive/10 border border-destructive/20 rounded-md'>
                  {serverError}
                </div>
              )}

              {/* Submit Button */}
              <Button
                type='submit'
                className='w-full'
                disabled={isLoading || !form.formState.isValid}
              >
                {isLoading ? (
                  <>
                    <Loader2 className='mr-2 h-4 w-4 animate-spin' />
                    Signing in...
                  </>
                ) : (
                  'Sign In'
                )}
              </Button>
            </form>
          </Form>

          {/* Additional Options */}
          <div className='space-y-4'>
            <div className='flex items-center justify-between text-sm'>
              <label className='flex items-center space-x-2 cursor-pointer'>
                <input type='checkbox' className='rounded border-gray-300' />
                <span className='text-muted-foreground'>Remember me</span>
              </label>
              <Button variant='link' className='p-0 h-auto text-sm'>
                Forgot password?
              </Button>
            </div>

            <div className='text-center text-sm text-muted-foreground'>
              Don't have an account?{' '}
              <Button variant='link' className='p-0 h-auto text-sm'>
                Contact administrator
              </Button>
            </div>
          </div>

          {/* Development Mode Indicator */}
          {USE_DEMO_AUTH && (
            <div className='text-center'>
              <Badge variant='outline' className='text-xs'>
                Development Mode - Demo Authentication
              </Badge>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
