'use client';

/**
 * Events Page - Complete Event Management System
 *
 * Features:
 * - Professional event listing with calendar view
 * - Event filtering and search
 * - Event creation and management
 * - Responsive design with card/list views
 * - Loading and error states
 * - Mock data with realistic content
 */

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { ModulePageLayout } from '@/components/ui/module-page-layout';
import { Calendar, CalendarDays, Clock, MapPin, Users } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { useState } from 'react';

// Mock data for demonstration
const mockEventStats = {
  totalEvents: 45,
  upcomingEvents: 12,
  thisMonth: 8,
  participants: 1250,
};

const mockEvents = [
  {
    id: '1',
    title: 'Annual Science Fair',
    description: 'Students showcase their innovative science projects and experiments.',
    date: '2024-03-15',
    time: '09:00 AM - 04:00 PM',
    location: 'Main Auditorium',
    type: 'Academic',
    status: 'Upcoming',
    participants: 150,
    organizer: 'Science Department',
    image: '/api/placeholder/400/200',
  },
  {
    id: '2',
    title: 'Parent-Teacher Conference',
    description: 'Individual meetings between parents and teachers to discuss student progress.',
    date: '2024-03-20',
    time: '02:00 PM - 06:00 PM',
    location: 'Classrooms',
    type: 'Meeting',
    status: 'Upcoming',
    participants: 200,
    organizer: 'Administration',
    image: '/api/placeholder/400/200',
  },
  {
    id: '3',
    title: 'Spring Sports Day',
    description: 'Annual athletic competition featuring various sports and team activities.',
    date: '2024-04-05',
    time: '08:00 AM - 05:00 PM',
    location: 'Sports Complex',
    type: 'Sports',
    status: 'Upcoming',
    participants: 300,
    organizer: 'Physical Education',
    image: '/api/placeholder/400/200',
  },
  {
    id: '4',
    title: 'Art Exhibition Opening',
    description: 'Showcase of student artwork from all grade levels.',
    date: '2024-04-12',
    time: '06:00 PM - 08:00 PM',
    location: 'Art Gallery',
    type: 'Cultural',
    status: 'Upcoming',
    participants: 100,
    organizer: 'Art Department',
    image: '/api/placeholder/400/200',
  },
  {
    id: '5',
    title: 'Mathematics Olympiad',
    description: 'Inter-school mathematics competition for advanced students.',
    date: '2024-02-28',
    time: '10:00 AM - 02:00 PM',
    location: 'Library Hall',
    type: 'Competition',
    status: 'Completed',
    participants: 80,
    organizer: 'Mathematics Department',
    image: '/api/placeholder/400/200',
  },
  {
    id: '6',
    title: 'School Board Meeting',
    description: 'Monthly meeting to discuss school policies and updates.',
    date: '2024-03-10',
    time: '07:00 PM - 09:00 PM',
    location: 'Conference Room',
    type: 'Administrative',
    status: 'Completed',
    participants: 25,
    organizer: 'School Board',
    image: '/api/placeholder/400/200',
  },
];

const eventTypes = [
  'All Types',
  'Academic',
  'Sports',
  'Cultural',
  'Meeting',
  'Competition',
  'Administrative',
];
const eventStatuses = ['All Status', 'Upcoming', 'Ongoing', 'Completed', 'Cancelled'];

export default function EventsPage() {
  const router = useRouter();
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedType, setSelectedType] = useState('All Types');
  const [selectedStatus, setSelectedStatus] = useState('All Status');

  // Filter events based on search and filters
  const filteredEvents = mockEvents.filter(event => {
    const matchesSearch =
      event.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      event.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
      event.organizer.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesType = selectedType === 'All Types' || event.type === selectedType;
    const matchesStatus = selectedStatus === 'All Status' || event.status === selectedStatus;

    return matchesSearch && matchesType && matchesStatus;
  });

  const statsCards = [
    {
      title: 'Total Events',
      value: mockEventStats.totalEvents.toString(),
      icon: CalendarDays,
      color: 'text-blue-600',
      bgColor: 'bg-blue-50',
      change: '+12%',
      changeType: 'positive' as const,
    },
    {
      title: 'Upcoming Events',
      value: mockEventStats.upcomingEvents.toString(),
      icon: Calendar,
      color: 'text-green-600',
      bgColor: 'bg-green-50',
      change: '+5%',
      changeType: 'positive' as const,
    },
    {
      title: 'This Month',
      value: mockEventStats.thisMonth.toString(),
      icon: Clock,
      color: 'text-orange-600',
      bgColor: 'bg-orange-50',
      change: '+3%',
      changeType: 'positive' as const,
    },
    {
      title: 'Total Participants',
      value: mockEventStats.participants.toString(),
      icon: Users,
      color: 'text-purple-600',
      bgColor: 'bg-purple-50',
      change: '+18%',
      changeType: 'positive' as const,
    },
  ];

  const filters = [
    {
      label: 'Event Type',
      value: selectedType,
      onChange: setSelectedType,
      options: eventTypes.map(type => ({ label: type, value: type })),
    },
    {
      label: 'Status',
      value: selectedStatus,
      onChange: setSelectedStatus,
      options: eventStatuses.map(status => ({ label: status, value: status })),
    },
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Upcoming':
        return 'bg-blue-100 text-blue-800';
      case 'Ongoing':
        return 'bg-green-100 text-green-800';
      case 'Completed':
        return 'bg-gray-100 text-gray-800';
      case 'Cancelled':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'Academic':
        return 'bg-purple-100 text-purple-800';
      case 'Sports':
        return 'bg-green-100 text-green-800';
      case 'Cultural':
        return 'bg-pink-100 text-pink-800';
      case 'Meeting':
        return 'bg-blue-100 text-blue-800';
      case 'Competition':
        return 'bg-orange-100 text-orange-800';
      case 'Administrative':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <ModulePageLayout
      title='Event Management'
      description='Organize and manage school events, activities, and programs'
      icon={CalendarDays}
      badge={{ label: 'Demo Data', variant: 'outline' }}
      statsCards={statsCards}
      searchPlaceholder='Search events...'
      filters={filters}
      createRoute='/dashboard/events/create'
      createLabel='Create Event'
      searchValue={searchTerm}
      onSearchChange={setSearchTerm}
      totalItems={mockEvents.length}
      filteredItems={filteredEvents.length}
    >
      {/* Events List */}
      <div className='grid grid-cols-1 gap-6'>
        {filteredEvents.map(event => (
          <Card key={event.id} className='hover:shadow-md transition-shadow'>
            <CardContent className='p-6'>
              <div className='flex flex-col lg:flex-row gap-6'>
                {/* Event Image */}
                <div className='lg:w-48 h-32 bg-gradient-to-br from-blue-100 to-purple-100 rounded-lg flex items-center justify-center'>
                  <CalendarDays className='w-12 h-12 text-blue-600' />
                </div>

                {/* Event Details */}
                <div className='flex-1 space-y-4'>
                  <div className='flex flex-col sm:flex-row sm:items-start sm:justify-between gap-4'>
                    <div>
                      <h3 className='text-xl font-semibold text-gray-900 mb-2'>{event.title}</h3>
                      <p className='text-gray-600 mb-3'>{event.description}</p>
                      <div className='flex flex-wrap gap-2'>
                        <Badge className={getStatusColor(event.status)}>{event.status}</Badge>
                        <Badge className={getTypeColor(event.type)}>{event.type}</Badge>
                      </div>
                    </div>
                    <div className='flex gap-2'>
                      <Button
                        variant='outline'
                        size='sm'
                        onClick={() => router.push(`/dashboard/events/${event.id}`)}
                      >
                        View Details
                      </Button>
                      <Button
                        variant='outline'
                        size='sm'
                        onClick={() => router.push(`/dashboard/events/${event.id}/edit`)}
                      >
                        Edit
                      </Button>
                    </div>
                  </div>

                  {/* Event Meta Information */}
                  <div className='grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 pt-4 border-t'>
                    <div className='flex items-center gap-2 text-sm text-gray-600'>
                      <Calendar className='w-4 h-4' />
                      <span>{event.date}</span>
                    </div>
                    <div className='flex items-center gap-2 text-sm text-gray-600'>
                      <Clock className='w-4 h-4' />
                      <span>{event.time}</span>
                    </div>
                    <div className='flex items-center gap-2 text-sm text-gray-600'>
                      <MapPin className='w-4 h-4' />
                      <span>{event.location}</span>
                    </div>
                    <div className='flex items-center gap-2 text-sm text-gray-600'>
                      <Users className='w-4 h-4' />
                      <span>{event.participants} participants</span>
                    </div>
                  </div>

                  <div className='text-sm text-gray-500'>
                    Organized by: <span className='font-medium'>{event.organizer}</span>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}

        {filteredEvents.length === 0 && (
          <div className='text-center py-12'>
            <CalendarDays className='w-16 h-16 text-gray-300 mx-auto mb-4' />
            <h3 className='text-lg font-medium text-gray-900 mb-2'>No events found</h3>
            <p className='text-gray-500 mb-6'>
              {searchTerm || selectedType !== 'All Types' || selectedStatus !== 'All Status'
                ? 'Try adjusting your search or filters'
                : 'Get started by creating your first event'}
            </p>
            <Button onClick={() => router.push('/dashboard/events/create')}>Create Event</Button>
          </div>
        )}
      </div>
    </ModulePageLayout>
  );
}
