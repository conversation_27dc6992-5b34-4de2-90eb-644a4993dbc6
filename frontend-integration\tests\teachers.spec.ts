/**
 * Teachers Module Integration Test
 *
 * Comprehensive test suite for the production-grade Teachers module
 * Tests all CRUD operations, role-based access, and UX features
 */

import { expect, test } from '@playwright/test';

const BASE_URL = 'http://localhost:3000';

// Test data
const testTeacher = {
  name: '<PERSON>',
  subject: 'Mathematics',
  email: '<EMAIL>',
  phone: '******-0123',
  department: 'Mathematics',
  status: 'ACTIVE',
  hire_date: '2024-01-15',
};

const updatedTeacher = {
  name: '<PERSON>',
  subject: 'Advanced Mathematics',
  email: '<EMAIL>',
  phone: '******-0124',
  department: 'Mathematics',
  status: 'ACTIVE',
  hire_date: '2024-01-15',
};

// Helper functions
async function loginAsAdmin(page) {
  await page.goto(`${BASE_URL}/login`);
  await page.fill('[name="email"]', '<EMAIL>');
  await page.fill('[name="password"]', 'admin123');
  await page.click('button[type="submit"]');
  await page.waitForURL('**/dashboard');
}

async function loginAsTeacher(page) {
  await page.goto(`${BASE_URL}/login`);
  await page.fill('[name="email"]', '<EMAIL>');
  await page.fill('[name="password"]', 'teacher123');
  await page.click('button[type="submit"]');
  await page.waitForURL('**/dashboard');
}

// Test Suite
test.describe('Teachers Module - Production Integration', () => {
  test.describe('Authentication & Authorization', () => {
    test('should redirect unauthenticated users to login', async ({ page }) => {
      await page.goto(`${BASE_URL}/dashboard/teachers`);
      await expect(page).toHaveURL(/.*\/login/);
    });

    test('should allow authenticated users to access teachers list', async ({ page }) => {
      await loginAsAdmin(page);
      await page.goto(`${BASE_URL}/dashboard/teachers`);
      await expect(page).toHaveURL(/.*\/dashboard\/teachers/);
      await expect(page.locator('h1')).toContainText('Teachers');
    });

    test('should show admin-only actions for admin users', async ({ page }) => {
      await loginAsAdmin(page);
      await page.goto(`${BASE_URL}/dashboard/teachers`);

      // Should see "Add Teacher" button
      await expect(page.locator('button:has-text("Add Teacher")')).toBeVisible();

      // Should see edit/delete actions in table
      const firstRow = page.locator('table tbody tr').first();
      if (await firstRow.isVisible()) {
        await firstRow.locator('button[aria-label="Actions"]').click();
        await expect(page.locator('text="Edit"')).toBeVisible();
        await expect(page.locator('text="Delete"')).toBeVisible();
      }
    });

    test('should hide admin-only actions for non-admin users', async ({ page }) => {
      await loginAsTeacher(page);
      await page.goto(`${BASE_URL}/dashboard/teachers`);

      // Should not see "Add Teacher" button or should be disabled
      const addButton = page.locator('button:has-text("Add Teacher")');
      if (await addButton.isVisible()) {
        await addButton.click();
        // Should show access denied message
        await expect(page.locator('text="Access Denied"')).toBeVisible();
      }
    });
  });

  test.describe('Teachers List Page', () => {
    test.beforeEach(async ({ page }) => {
      await loginAsAdmin(page);
      await page.goto(`${BASE_URL}/dashboard/teachers`);
    });

    test('should display teachers list with proper structure', async ({ page }) => {
      // Check page title and description
      await expect(page.locator('h1')).toContainText('Teachers');
      await expect(page.locator('text="Manage and view all teachers"')).toBeVisible();

      // Check stats cards
      await expect(page.locator('text="Total Teachers"')).toBeVisible();
      await expect(page.locator('text="Active Teachers"')).toBeVisible();
      await expect(page.locator('text="Departments"')).toBeVisible();

      // Check filters
      await expect(page.locator('input[placeholder*="Search teachers"]')).toBeVisible();
      await expect(page.locator('text="All Departments"')).toBeVisible();
      await expect(page.locator('text="All Status"')).toBeVisible();

      // Check table headers
      await expect(page.locator('th:has-text("Teacher")')).toBeVisible();
      await expect(page.locator('th:has-text("Department")')).toBeVisible();
      await expect(page.locator('th:has-text("Subject")')).toBeVisible();
      await expect(page.locator('th:has-text("Status")')).toBeVisible();
      await expect(page.locator('th:has-text("Actions")')).toBeVisible();
    });

    test('should filter teachers by search term', async ({ page }) => {
      const searchInput = page.locator('input[placeholder*="Search teachers"]');
      await searchInput.fill('John');

      // Wait for search results
      await page.waitForTimeout(500);

      // Check that results are filtered
      const tableRows = page.locator('table tbody tr');
      const rowCount = await tableRows.count();

      if (rowCount > 0) {
        // At least one row should contain "John"
        await expect(tableRows.first().locator('text="John"')).toBeVisible();
      }
    });

    test('should filter teachers by department', async ({ page }) => {
      const departmentSelect = page.locator('select:near(text="All Departments")');
      if (await departmentSelect.isVisible()) {
        await departmentSelect.selectOption('Mathematics');
        await page.waitForTimeout(500);

        // Check that results are filtered
        const tableRows = page.locator('table tbody tr');
        const rowCount = await tableRows.count();

        if (rowCount > 0) {
          await expect(tableRows.first().locator('text="Mathematics"')).toBeVisible();
        }
      }
    });

    test('should show empty state when no teachers found', async ({ page }) => {
      const searchInput = page.locator('input[placeholder*="Search teachers"]');
      await searchInput.fill('NonExistentTeacher12345');
      await page.waitForTimeout(500);

      await expect(page.locator('text="No Teachers Found"')).toBeVisible();
    });
  });

  test.describe('Create Teacher', () => {
    test.beforeEach(async ({ page }) => {
      await loginAsAdmin(page);
    });

    test('should navigate to create teacher page', async ({ page }) => {
      await page.goto(`${BASE_URL}/dashboard/teachers`);
      await page.click('button:has-text("Add Teacher")');
      await expect(page).toHaveURL(/.*\/dashboard\/teachers\/create/);
      await expect(page.locator('h1')).toContainText('Create Teacher');
    });

    test('should create a new teacher successfully', async ({ page }) => {
      await page.goto(`${BASE_URL}/dashboard/teachers/create`);

      // Fill form
      await page.fill('input[name="name"]', testTeacher.name);
      await page.fill('input[name="subject"]', testTeacher.subject);
      await page.fill('input[name="email"]', testTeacher.email);
      await page.fill('input[name="phone"]', testTeacher.phone);
      await page.fill('input[name="department"]', testTeacher.department);
      await page.selectOption('select[name="status"]', testTeacher.status);
      await page.fill('input[name="hire_date"]', testTeacher.hire_date);

      // Submit form
      await page.click('button:has-text("Create Teacher")');

      // Should redirect to teacher detail page or show success message
      await page.waitForTimeout(2000);
      const currentUrl = page.url();
      const hasRedirected =
        currentUrl.includes('/dashboard/teachers/') && !currentUrl.includes('/create');
      const hasSuccessMessage = await page
        .locator('text="Teacher created successfully"')
        .isVisible();

      expect(hasRedirected || hasSuccessMessage).toBeTruthy();
    });

    test('should validate required fields', async ({ page }) => {
      await page.goto(`${BASE_URL}/dashboard/teachers/create`);

      // Try to submit empty form
      await page.click('button:has-text("Create Teacher")');

      // Should show validation errors
      await expect(page.locator('text="required" >> visible=true')).toHaveCount({ min: 1 });
    });

    test('should validate email format', async ({ page }) => {
      await page.goto(`${BASE_URL}/dashboard/teachers/create`);

      await page.fill('input[name="email"]', 'invalid-email');
      await page.click('button:has-text("Create Teacher")');

      // Should show email validation error
      await expect(page.locator('text*="email" >> visible=true')).toBeVisible();
    });
  });

  test.describe('Teacher Detail Page', () => {
    test.beforeEach(async ({ page }) => {
      await loginAsAdmin(page);
    });

    test('should display teacher details', async ({ page }) => {
      // Navigate to teachers list and click on first teacher
      await page.goto(`${BASE_URL}/dashboard/teachers`);

      const firstTeacherRow = page.locator('table tbody tr').first();
      if (await firstTeacherRow.isVisible()) {
        await firstTeacherRow.locator('button:has-text("View Details")').click();

        // Should be on detail page
        await expect(page).toHaveURL(/.*\/dashboard\/teachers\/[^\/]+$/);
        await expect(page.locator('h1')).toContainText('Teacher Details');

        // Should show teacher information
        await expect(page.locator('text="Basic Information"')).toBeVisible();
        await expect(page.locator('text="Contact Information"')).toBeVisible();
        await expect(page.locator('text="Professional Details"')).toBeVisible();
      }
    });

    test('should show edit and delete buttons for admin', async ({ page }) => {
      await page.goto(`${BASE_URL}/dashboard/teachers`);

      const firstTeacherRow = page.locator('table tbody tr').first();
      if (await firstTeacherRow.isVisible()) {
        await firstTeacherRow.locator('button:has-text("View Details")').click();

        // Should see admin actions
        await expect(page.locator('button:has-text("Edit")')).toBeVisible();
        await expect(page.locator('button:has-text("Delete")')).toBeVisible();
      }
    });

    test('should handle non-existent teacher', async ({ page }) => {
      await page.goto(`${BASE_URL}/dashboard/teachers/non-existent-id`);

      // Should show not found message
      await expect(page.locator('text="Teacher Not Found"')).toBeVisible();
      await expect(page.locator('button:has-text("Back to Teachers")')).toBeVisible();
    });
  });

  test.describe('Edit Teacher', () => {
    test.beforeEach(async ({ page }) => {
      await loginAsAdmin(page);
    });

    test('should navigate to edit page and pre-fill form', async ({ page }) => {
      await page.goto(`${BASE_URL}/dashboard/teachers`);

      const firstTeacherRow = page.locator('table tbody tr').first();
      if (await firstTeacherRow.isVisible()) {
        // Go to detail page first
        await firstTeacherRow.locator('button:has-text("View Details")').click();

        // Click edit button
        await page.click('button:has-text("Edit")');

        // Should be on edit page
        await expect(page).toHaveURL(/.*\/dashboard\/teachers\/[^\/]+\/edit$/);
        await expect(page.locator('h1')).toContainText('Edit Teacher');

        // Form should be pre-filled
        const nameInput = page.locator('input[name="name"]');
        await expect(nameInput).not.toHaveValue('');
      }
    });

    test('should update teacher successfully', async ({ page }) => {
      // This test would require a known teacher ID
      // For now, we'll test the form validation
      await page.goto(`${BASE_URL}/dashboard/teachers/test-id/edit`);

      // If the page loads (even with error), form should be present
      const nameInput = page.locator('input[name="name"]');
      if (await nameInput.isVisible()) {
        await nameInput.fill(updatedTeacher.name);
        await page.click('button:has-text("Save Changes")');

        // Should show success or redirect
        await page.waitForTimeout(2000);
      }
    });
  });

  test.describe('Delete Teacher', () => {
    test.beforeEach(async ({ page }) => {
      await loginAsAdmin(page);
    });

    test('should show delete confirmation dialog', async ({ page }) => {
      await page.goto(`${BASE_URL}/dashboard/teachers`);

      const firstTeacherRow = page.locator('table tbody tr').first();
      if (await firstTeacherRow.isVisible()) {
        // Open actions menu
        await firstTeacherRow.locator('button[aria-label="Actions"]').click();

        // Click delete
        await page.click('text="Delete"');

        // Should show confirmation dialog
        await expect(page.locator('text="Delete Teacher"')).toBeVisible();
        await expect(page.locator('text="Are you sure"')).toBeVisible();
        await expect(page.locator('button:has-text("Cancel")')).toBeVisible();
        await expect(page.locator('button:has-text("Delete Teacher")')).toBeVisible();
      }
    });

    test('should cancel delete operation', async ({ page }) => {
      await page.goto(`${BASE_URL}/dashboard/teachers`);

      const firstTeacherRow = page.locator('table tbody tr').first();
      if (await firstTeacherRow.isVisible()) {
        await firstTeacherRow.locator('button[aria-label="Actions"]').click();
        await page.click('text="Delete"');

        // Cancel deletion
        await page.click('button:has-text("Cancel")');

        // Dialog should close
        await expect(page.locator('text="Delete Teacher"')).not.toBeVisible();
      }
    });
  });

  test.describe('Loading and Error States', () => {
    test('should show loading skeleton on page load', async ({ page }) => {
      await loginAsAdmin(page);

      // Navigate to teachers page and check for loading state
      const response = page.goto(`${BASE_URL}/dashboard/teachers`);

      // Should show loading skeleton briefly
      await expect(page.locator('[data-testid="skeleton"]').first()).toBeVisible({ timeout: 1000 });

      await response;
    });

    test('should handle API errors gracefully', async ({ page }) => {
      await loginAsAdmin(page);

      // This would require mocking API failures
      // For now, we test that error boundaries exist
      await page.goto(`${BASE_URL}/dashboard/teachers`);

      // Page should load even if API fails (fallback to dummy data)
      await expect(page.locator('h1')).toContainText('Teachers');
    });
  });

  test.describe('Responsive Design', () => {
    test('should work on mobile devices', async ({ page }) => {
      await page.setViewportSize({ width: 375, height: 667 });
      await loginAsAdmin(page);
      await page.goto(`${BASE_URL}/dashboard/teachers`);

      // Should show mobile-friendly layout
      await expect(page.locator('h1')).toBeVisible();

      // Table should be responsive or show cards
      const table = page.locator('table');
      const cards = page.locator('[data-testid="teacher-card"]');

      expect((await table.isVisible()) || (await cards.count()) > 0).toBeTruthy();
    });

    test('should work on tablet devices', async ({ page }) => {
      await page.setViewportSize({ width: 768, height: 1024 });
      await loginAsAdmin(page);
      await page.goto(`${BASE_URL}/dashboard/teachers`);

      await expect(page.locator('h1')).toBeVisible();
      await expect(page.locator('table')).toBeVisible();
    });
  });

  test.describe('Hard Refresh Support', () => {
    test('should work after hard refresh on list page', async ({ page }) => {
      await loginAsAdmin(page);
      await page.goto(`${BASE_URL}/dashboard/teachers`);

      // Hard refresh
      await page.reload();

      // Should still work
      await expect(page.locator('h1')).toContainText('Teachers');
    });

    test('should work after hard refresh on detail page', async ({ page }) => {
      await loginAsAdmin(page);
      await page.goto(`${BASE_URL}/dashboard/teachers/test-id`);

      // Hard refresh
      await page.reload();

      // Should handle the route properly (even if teacher doesn't exist)
      await expect(page.locator('h1')).toBeVisible();
    });

    test('should work after hard refresh on create page', async ({ page }) => {
      await loginAsAdmin(page);
      await page.goto(`${BASE_URL}/dashboard/teachers/create`);

      // Hard refresh
      await page.reload();

      // Should still show create form
      await expect(page.locator('h1')).toContainText('Create Teacher');
    });
  });
});
