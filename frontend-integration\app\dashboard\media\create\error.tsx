'use client';

import { ModuleError } from '@/components/ui/module-error';

interface CreateMediaErrorProps {
  error: Error & { digest?: string };
  reset: () => void;
}

export default function CreateMediaError({ error, reset }: CreateMediaErrorProps) {
  return (
    <ModuleError
      error={error}
      reset={reset}
      moduleName="Upload Media"
      moduleIcon="📁"
      backHref="/dashboard/media"
    />
  );
}
