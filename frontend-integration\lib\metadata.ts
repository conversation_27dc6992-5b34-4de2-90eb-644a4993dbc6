/**
 * Metadata Configuration for SEO and Social Sharing
 *
 * Centralized metadata management with:
 * - Consistent SEO optimization
 * - Social media sharing support
 * - Dynamic metadata generation
 * - Type-safe metadata patterns
 * - OpenGraph and Twitter Card support
 */

import { Metadata } from 'next';

// Base application metadata
export const BASE_METADATA = {
  title: 'School Management System',
  description: 'Comprehensive school management system for modern educational institutions',
  keywords: [
    'school management',
    'education software',
    'student management',
    'teacher portal',
    'academic management',
    'school administration',
    'educational technology',
    'learning management',
  ],
  authors: [{ name: 'School Management Team' }],
  creator: 'School Management System',
  publisher: 'Educational Solutions',
  applicationName: 'School Management System',
  category: 'Education',
} as const;

// OpenGraph configuration
export const OPENGRAPH_CONFIG = {
  type: 'website',
  locale: 'en_US',
  siteName: BASE_METADATA.title,
  images: [
    {
      url: '/og-image.png',
      width: 1200,
      height: 630,
      alt: 'School Management System',
    },
  ],
} as const;

// Twitter Card configuration
export const TWITTER_CONFIG = {
  card: 'summary_large_image' as const,
  site: '@schoolmanagement',
  creator: '@schoolmanagement',
  images: ['/twitter-image.png'],
} as const;

// Route-specific metadata templates
export const ROUTE_METADATA = {
  // Authentication routes
  auth: {
    login: {
      title: 'Login - School Management System',
      description: 'Sign in to access your school management dashboard with secure authentication.',
      keywords: ['login', 'sign in', 'authentication', 'school portal'],
    },
    register: {
      title: 'Register - School Management System',
      description:
        'Create your account to get started with our comprehensive school management platform.',
      keywords: ['register', 'sign up', 'create account', 'school registration'],
    },
    'forgot-password': {
      title: 'Reset Password - School Management System',
      description: 'Reset your password to regain access to your school management account.',
      keywords: ['password reset', 'forgot password', 'account recovery'],
    },
  },

  // Dashboard routes
  dashboard: {
    home: {
      title: 'Dashboard - School Management System',
      description:
        "Overview of your school's key metrics, recent activities, and quick access to important features.",
      keywords: ['dashboard', 'overview', 'school metrics', 'analytics'],
    },
    students: {
      title: 'Students - School Management System',
      description:
        'Manage student records, enrollment, academic progress, and personal information efficiently.',
      keywords: ['students', 'student management', 'enrollment', 'academic records'],
    },
    teachers: {
      title: 'Teachers - School Management System',
      description: 'Manage teacher profiles, assignments, schedules, and performance tracking.',
      keywords: ['teachers', 'faculty', 'staff management', 'teacher profiles'],
    },
    classes: {
      title: 'Classes - School Management System',
      description:
        'Organize and manage class schedules, room assignments, and student-teacher relationships.',
      keywords: ['classes', 'schedules', 'classroom management', 'timetables'],
    },
    subjects: {
      title: 'Subjects - School Management System',
      description:
        'Manage academic subjects, curriculum planning, and subject-teacher assignments.',
      keywords: ['subjects', 'curriculum', 'academic subjects', 'course management'],
    },
    attendance: {
      title: 'Attendance - School Management System',
      description: 'Track and manage student attendance with automated reporting and analytics.',
      keywords: ['attendance', 'attendance tracking', 'student presence', 'attendance reports'],
    },
    exams: {
      title: 'Exams - School Management System',
      description: 'Schedule exams, manage test papers, and track examination results efficiently.',
      keywords: ['exams', 'examinations', 'test management', 'exam scheduling'],
    },
    grades: {
      title: 'Grades - School Management System',
      description: 'Record, calculate, and manage student grades with comprehensive reporting.',
      keywords: ['grades', 'grading', 'academic performance', 'grade reports'],
    },
    fees: {
      title: 'Fees - School Management System',
      description: 'Manage school fees, payment tracking, and financial records for students.',
      keywords: ['fees', 'payments', 'financial management', 'fee collection'],
    },
    parents: {
      title: 'Parents - School Management System',
      description: 'Parent portal for communication, progress tracking, and school engagement.',
      keywords: ['parents', 'parent portal', 'family engagement', 'communication'],
    },
    announcements: {
      title: 'Announcements - School Management System',
      description: 'Create and manage school announcements, notifications, and important updates.',
      keywords: ['announcements', 'notifications', 'school news', 'updates'],
    },
    events: {
      title: 'Events - School Management System',
      description: 'Plan and manage school events, activities, and calendar scheduling.',
      keywords: ['events', 'school events', 'activities', 'event management'],
    },
    reports: {
      title: 'Reports - School Management System',
      description: 'Generate comprehensive reports and analytics for data-driven decision making.',
      keywords: ['reports', 'analytics', 'data analysis', 'school reports'],
    },
    settings: {
      title: 'Settings - School Management System',
      description: 'Configure system settings, user preferences, and administrative options.',
      keywords: ['settings', 'configuration', 'preferences', 'administration'],
    },
    profile: {
      title: 'Profile - School Management System',
      description: 'Manage your personal profile, account settings, and preferences.',
      keywords: ['profile', 'account', 'user settings', 'personal information'],
    },
  },

  // Special pages
  special: {
    'forms-demo': {
      title: 'Forms Demo - School Management System',
      description: 'Interactive demonstration of form components and validation patterns.',
      keywords: ['forms', 'demo', 'components', 'validation'],
    },
    'state-demo': {
      title: 'State Management Demo - School Management System',
      description: 'Comprehensive demonstration of state management patterns and best practices.',
      keywords: ['state management', 'demo', 'zustand', 'patterns'],
    },
  },
} as const;

/**
 * Generate metadata for a specific route
 */
export function generateMetadata(route: string, customMetadata?: Partial<Metadata>): Metadata {
  // Parse route to get metadata
  const routeParts = route.split('/').filter(Boolean);
  let routeMetadata: any = ROUTE_METADATA.dashboard.home; // Default

  // Determine route metadata
  if (routeParts.length === 0) {
    routeMetadata = ROUTE_METADATA.dashboard.home;
  } else if (routeParts[0] === 'login') {
    routeMetadata = ROUTE_METADATA.auth.login;
  } else if (routeParts[0] === 'dashboard' && routeParts.length === 1) {
    routeMetadata = ROUTE_METADATA.dashboard.home;
  } else if (routeParts[0] === 'dashboard' && routeParts[1]) {
    const subRoute = routeParts[1] as keyof typeof ROUTE_METADATA.dashboard;
    routeMetadata = ROUTE_METADATA.dashboard[subRoute] || ROUTE_METADATA.dashboard.home;
  } else if (routeParts[0] && routeParts[0] in ROUTE_METADATA.special) {
    const specialRoute = routeParts[0] as keyof typeof ROUTE_METADATA.special;
    routeMetadata = ROUTE_METADATA.special[specialRoute];
  }

  // Build complete metadata
  const metadata: Metadata = {
    title: routeMetadata.title,
    description: routeMetadata.description,
    keywords: [...BASE_METADATA.keywords, ...routeMetadata.keywords],
    authors: [...BASE_METADATA.authors],
    creator: BASE_METADATA.creator,
    publisher: BASE_METADATA.publisher,
    applicationName: BASE_METADATA.applicationName,
    category: BASE_METADATA.category,

    openGraph: {
      ...OPENGRAPH_CONFIG,
      images: [...OPENGRAPH_CONFIG.images],
      title: routeMetadata.title,
      description: routeMetadata.description,
      url: `/${route}`,
    },

    twitter: {
      ...TWITTER_CONFIG,
      images: [...TWITTER_CONFIG.images],
      title: routeMetadata.title,
      description: routeMetadata.description,
    },

    robots: {
      index: true,
      follow: true,
      googleBot: {
        index: true,
        follow: true,
        'max-video-preview': -1,
        'max-image-preview': 'large',
        'max-snippet': -1,
      },
    },

    verification: {
      ...(process.env.GOOGLE_VERIFICATION_ID && { google: process.env.GOOGLE_VERIFICATION_ID }),
      ...(process.env.YANDEX_VERIFICATION_ID && { yandex: process.env.YANDEX_VERIFICATION_ID }),
      ...(process.env.YAHOO_VERIFICATION_ID && { yahoo: process.env.YAHOO_VERIFICATION_ID }),
    },

    alternates: {
      canonical: `/${route}`,
    },

    ...customMetadata,
  };

  return metadata;
}

/**
 * Generate structured data for a page
 */
export function generateStructuredData(route: string, additionalData?: Record<string, any>) {
  const baseStructuredData = {
    '@context': 'https://schema.org',
    '@type': 'WebApplication',
    name: BASE_METADATA.title,
    description: BASE_METADATA.description,
    url: process.env.NEXT_PUBLIC_APP_URL || 'https://school-management.com',
    applicationCategory: 'EducationalApplication',
    operatingSystem: 'Web Browser',
    offers: {
      '@type': 'Offer',
      price: '0',
      priceCurrency: 'USD',
    },
    author: {
      '@type': 'Organization',
      name: BASE_METADATA.creator,
    },
    ...additionalData,
  };

  return JSON.stringify(baseStructuredData);
}

/**
 * Metadata utilities for dynamic content
 */
export const metadataUtils = {
  // Generate metadata for entity detail pages
  entityDetail: (entityType: string, entityName: string, description?: string) => ({
    title: `${entityName} - ${entityType} - School Management System`,
    description:
      description || `View and manage ${entityName} details in the school management system.`,
    keywords: [entityType.toLowerCase(), 'details', 'management', entityName.toLowerCase()],
  }),

  // Generate metadata for create/edit pages
  entityForm: (entityType: string, action: 'create' | 'edit', entityName?: string) => ({
    title: `${action === 'create' ? 'Create' : 'Edit'} ${entityType}${
      entityName ? ` - ${entityName}` : ''
    } - School Management System`,
    description: `${
      action === 'create' ? 'Create a new' : 'Edit'
    } ${entityType.toLowerCase()} in the school management system.`,
    keywords: [entityType.toLowerCase(), action, 'form', 'management'],
  }),

  // Generate metadata for list pages with filters
  entityList: (entityType: string, filters?: Record<string, string>) => {
    const filterText = filters
      ? ` filtered by ${Object.entries(filters)
          .map(([key, value]) => `${key}: ${value}`)
          .join(', ')}`
      : '';
    return {
      title: `${entityType} List${filterText} - School Management System`,
      description: `Browse and manage ${entityType.toLowerCase()} records${filterText} in the school management system.`,
      keywords: [
        entityType.toLowerCase(),
        'list',
        'browse',
        'management',
        ...(filters ? Object.keys(filters) : []),
      ],
    };
  },
};

// Export types for TypeScript support
export type RouteMetadata = typeof ROUTE_METADATA;
export type MetadataRoute =
  | keyof RouteMetadata['dashboard']
  | keyof RouteMetadata['auth']
  | keyof RouteMetadata['special'];
