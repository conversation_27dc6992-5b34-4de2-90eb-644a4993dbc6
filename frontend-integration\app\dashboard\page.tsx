// ================================
// app/(dashboard)/dashboard/page.tsx
// Premium Overview Page with Recharts + Motion
// ================================

'use client';

import { motion } from 'framer-motion';
import {
  Activity,
  Bell,
  BookOpen,
  CheckCircle2,
  DollarSign,
  GraduationCap,
  Plus,
  School,
  TrendingUp,
  Users,
} from 'lucide-react';
import { useRouter } from 'next/navigation';
import React, { useMemo } from 'react';
import {
  Bar,
  BarChart,
  CartesianGrid,
  Line,
  LineChart,
  ResponsiveContainer,
  Tooltip,
  XAxis,
  YAxis,
} from 'recharts';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

export default function DashboardPage() {
  const router = useRouter();

  // Mock data for charts & stats (replace with TanStack Query later)
  const stats = useMemo(
    () => ({
      students: { total: 1247, active: 1198, growth: 12.5 },
      attendance: { today: 94, present: 1128, total: 1198 },
      fees: { pending: 37, collectedPct: 82 },
      exams: { upcoming: 3 },
    }),
    []
  );

  const attendanceTrend = [
    { day: 'Mon', rate: 91 },
    { day: 'Tue', rate: 92 },
    { day: 'Wed', rate: 93 },
    { day: 'Thu', rate: 95 },
    { day: 'Fri', rate: 94 },
  ];

  const examPerformance = [
    { subject: 'Math', avg: 76 },
    { subject: 'Science', avg: 81 },
    { subject: 'English', avg: 74 },
    { subject: 'History', avg: 79 },
    { subject: 'CS', avg: 88 },
  ];

  const recent = [
    { id: 1, title: 'New student enrolled', desc: 'Emma Johnson joined Grade 10A', time: '2m' },
    { id: 2, title: 'Exam results published', desc: 'Mid-term results available', time: '1h' },
    { id: 3, title: 'Attendance marked', desc: 'All classes marked for today', time: '2h' },
    { id: 4, title: 'Fee payment received', desc: '15 students paid monthly fee', time: '3h' },
  ];

  const quickActions = [
    {
      label: 'Add Student',
      icon: <Users className='h-4 w-4' />,
      href: '/dashboard/students/create',
      gradient: 'from-sky-500 to-violet-600',
    },
    {
      label: 'Make Attendance',
      icon: <CheckCircle2 className='h-4 w-4' />,
      href: '/dashboard/attendance',
      gradient: 'from-emerald-500 to-emerald-600',
    },
    {
      label: 'Schedule Exam',
      icon: <BookOpen className='h-4 w-4' />,
      href: '/dashboard/exams/create',
      gradient: 'from-purple-500 to-purple-600',
    },
  ];

  return (
    <div className='space-y-6'>
      {/* Greeting */}
      <div className='flex flex-col md:flex-row md:items-center md:justify-between gap-4'>
        <div>
          <h1 className='text-3xl md:text-4xl font-bold tracking-tight bg-gradient-to-r from-sky-600 to-violet-600 bg-clip-text text-transparent'>
            Welcome back, Demo User
          </h1>
          <p className='text-slate-600 mt-1'>
            Here’s a quick overview of your school today.
            <Badge variant='outline' className='ml-2'>
              <Activity className='h-3 w-3 mr-1' />
              Live Preview
            </Badge>
          </p>
        </div>
        <div className='flex items-center gap-2'>
          <Button variant='outline' size='sm'>
            <Bell className='h-4 w-4 mr-2' />
            Notifications
          </Button>
          <Button
            size='sm'
            className='bg-gradient-to-r from-sky-500 to-violet-600 hover:from-sky-600 hover:to-violet-700 text-white'
          >
            <Plus className='h-4 w-4 mr-2' /> Quick Add
          </Button>
        </div>
      </div>

      {/* Top stats */}
      <div className='grid grid-cols-1 sm:grid-cols-2 xl:grid-cols-4 gap-6'>
        <StatCard
          title='Total Students'
          icon={<GraduationCap className='h-5 w-5' />}
          value={stats.students.total}
          footer={<StatFooter change={stats.students.growth} />}
          gradient='from-sky-50 to-sky-100'
        />
        <StatCard
          title='Attendance Today'
          icon={<CheckCircle2 className='h-5 w-5' />}
          value={`${stats.attendance.today}%`}
          footer={
            <span className='text-xs text-slate-600'>
              {stats.attendance.present}/{stats.attendance.total} present
            </span>
          }
          gradient='from-emerald-50 to-emerald-100'
        />
        <StatCard
          title='Pending Fees'
          icon={<DollarSign className='h-5 w-5' />}
          value={stats.fees.pending}
          footer={
            <span className='text-xs text-slate-600'>{stats.fees.collectedPct}% collected</span>
          }
          gradient='from-amber-50 to-amber-100'
        />
        <StatCard
          title='Upcoming Exams'
          icon={<School className='h-5 w-5' />}
          value={stats.exams.upcoming}
          footer={<span className='text-xs text-slate-600'>Next week</span>}
          gradient='from-violet-50 to-violet-100'
        />
      </div>

      {/* Charts + Activity */}
      <div className='grid grid-cols-1 xl:grid-cols-3 gap-6'>
        {/* Attendance Trend */}
        <Card className='shadow-md'>
          <CardHeader>
            <CardTitle>Attendance Trend</CardTitle>
          </CardHeader>
          <CardContent className='h-64'>
            <ResponsiveContainer width='100%' height='100%'>
              <LineChart data={attendanceTrend} margin={{ left: 8, right: 8, top: 8, bottom: 8 }}>
                <CartesianGrid strokeDasharray='3 3' />
                <XAxis dataKey='day' />
                <YAxis domain={[80, 100]} />
                <Tooltip />
                <Line type='monotone' dataKey='rate' dot />
              </LineChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        {/* Exam Performance */}
        <Card className='shadow-md'>
          <CardHeader>
            <CardTitle>Exam Performance (Avg %)</CardTitle>
          </CardHeader>
          <CardContent className='h-64'>
            <ResponsiveContainer width='100%' height='100%'>
              <BarChart data={examPerformance} margin={{ left: 8, right: 8, top: 8, bottom: 8 }}>
                <CartesianGrid strokeDasharray='3 3' />
                <XAxis dataKey='subject' />
                <YAxis domain={[0, 100]} />
                <Tooltip />
                <Bar dataKey='avg' radius={[6, 6, 0, 0]} />
              </BarChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        {/* Recent Activity + Quick Actions */}
        <Card className='shadow-md'>
          <CardHeader>
            <CardTitle>Recent Activity</CardTitle>
          </CardHeader>
          <CardContent className='space-y-4'>
            <ul className='space-y-3'>
              {recent.map(item => (
                <li key={item.id} className='flex items-start gap-3'>
                  <span
                    className='mt-1 inline-flex h-2.5 w-2.5 rounded-full bg-sky-500'
                    aria-hidden
                  />
                  <div className='text-sm'>
                    <p className='font-medium text-slate-900'>{item.title}</p>
                    <p className='text-slate-600'>{item.desc}</p>
                    <span className='text-xs text-slate-500'>{item.time} ago</span>
                  </div>
                </li>
              ))}
            </ul>

            <div className='grid grid-cols-1 sm:grid-cols-3 gap-3 pt-2'>
              {quickActions.map(a => (
                <Button
                  key={a.label}
                  onClick={() => router.push(a.href)}
                  className={cn(
                    'justify-start h-auto py-3 px-3 shadow-md hover:shadow-lg transition-all duration-200 text-white bg-gradient-to-br',
                    a.gradient
                  )}
                >
                  {a.icon}
                  <span className='ml-2'>{a.label}</span>
                </Button>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}

// ------- Small components -------
function StatCard({
  title,
  icon,
  value,
  footer,
  gradient,
}: {
  title: string;
  icon: React.ReactNode;
  value: React.ReactNode;
  footer?: React.ReactNode;
  gradient: string;
}) {
  return (
    <motion.div
      initial={{ opacity: 0, y: 8 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.25 }}
    >
      <Card
        className={cn(
          'border-0 bg-gradient-to-br',
          gradient,
          'shadow-md hover:shadow-lg transition-all duration-200'
        )}
      >
        <CardHeader className='pb-2'>
          <CardTitle className='flex items-center justify-between text-base text-slate-700'>
            <span>{title}</span>
            <span className='inline-flex p-2 bg-white rounded-lg shadow-sm'>{icon}</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className='text-3xl font-bold text-slate-900'>{value}</div>
          {footer && <div className='mt-2'>{footer}</div>}
        </CardContent>
      </Card>
    </motion.div>
  );
}

function StatFooter({ change }: { change: number }) {
  return (
    <div className='flex items-center gap-1 text-xs'>
      <TrendingUp className='h-3 w-3' />
      <span className='text-emerald-600 font-medium'>+{change}%</span>
      <span className='text-slate-500'>vs last month</span>
    </div>
  );
}

// Utility (inline to avoid imports if user lacks cn helper)
function cn(...classes: Array<string | undefined | false>) {
  return classes.filter(Boolean).join(' ');
}
