/**
 * SUPER_ADMIN specific UI components
 * Enhanced security features and admin-only functionality
 */

'use client';

import React, { useState } from 'react';
import { AlertTriangle, Shield, Trash2, Settings, Users, FileText, Database } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { useAuth } from '@/hooks/useAuth';
import { canDeleteClass, canBulkOperateClasses, canAccessSystemSettings, canViewAuditLogs } from '@/lib/permissions';
import { SecurityUtils, type AuditAction } from '@/lib/security';

// Delete button with enhanced security
interface SecureDeleteButtonProps {
  onDelete: () => void;
  resourceName: string;
  resourceType: string;
  disabled?: boolean;
}

export const SecureDeleteButton: React.FC<SecureDeleteButtonProps> = ({
  onDelete,
  resourceName,
  resourceType,
  disabled = false
}) => {
  const { user } = useAuth();
  const [isDeleting, setIsDeleting] = useState(false);

  const handleDelete = async () => {
    if (!canDeleteClass(user?.role)) {
      alert('Access denied: SUPER_ADMIN role required');
      return;
    }

    setIsDeleting(true);
    
    SecurityUtils.confirmDestructiveAction(
      `delete this ${resourceType}`,
      resourceName,
      async () => {
        try {
          await onDelete();
          
          // Log the action
          await SecurityUtils.handleSecurityEvent(
            'DELETE_CLASS' as AuditAction,
            user?.id || '',
            user?.email || '',
            user?.role || null,
            true,
            { resourceName, resourceType }
          );
        } catch (error) {
          await SecurityUtils.handleSecurityEvent(
            'DELETE_CLASS' as AuditAction,
            user?.id || '',
            user?.email || '',
            user?.role || null,
            false,
            { resourceName, resourceType },
            error instanceof Error ? error.message : 'Unknown error'
          );
        } finally {
          setIsDeleting(false);
        }
      }
    );
  };

  if (!canDeleteClass(user?.role)) {
    return null;
  }

  return (
    <Button
      variant="destructive"
      size="sm"
      onClick={handleDelete}
      disabled={disabled || isDeleting}
      className="gap-2"
    >
      <Trash2 className="h-4 w-4" />
      {isDeleting ? 'Deleting...' : 'Delete'}
    </Button>
  );
};

// Bulk operations toolbar
interface BulkOperationsToolbarProps {
  selectedItems: string[];
  onBulkDelete: (ids: string[]) => void;
  onClearSelection: () => void;
  itemType: string;
}

export const BulkOperationsToolbar: React.FC<BulkOperationsToolbarProps> = ({
  selectedItems,
  onBulkDelete,
  onClearSelection,
  itemType
}) => {
  const { user } = useAuth();
  const [isProcessing, setIsProcessing] = useState(false);

  const handleBulkDelete = async () => {
    if (!canBulkOperateClasses(user?.role)) {
      alert('Access denied: SUPER_ADMIN role required');
      return;
    }

    setIsProcessing(true);

    SecurityUtils.confirmBulkAction(
      `delete ${selectedItems.length} ${itemType}(s)`,
      selectedItems.length,
      async () => {
        try {
          await onBulkDelete(selectedItems);
          
          // Log the bulk action
          await SecurityUtils.handleSecurityEvent(
            'BULK_DELETE_CLASSES' as AuditAction,
            user?.id || '',
            user?.email || '',
            user?.role || null,
            true,
            { itemCount: selectedItems.length, itemType, itemIds: selectedItems }
          );
          
          onClearSelection();
        } catch (error) {
          await SecurityUtils.handleSecurityEvent(
            'BULK_DELETE_CLASSES' as AuditAction,
            user?.id || '',
            user?.email || '',
            user?.role || null,
            false,
            { itemCount: selectedItems.length, itemType, itemIds: selectedItems },
            error instanceof Error ? error.message : 'Unknown error'
          );
        } finally {
          setIsProcessing(false);
        }
      }
    );
  };

  if (!canBulkOperateClasses(user?.role) || selectedItems.length === 0) {
    return null;
  }

  return (
    <Card className="mb-4 border-orange-200 bg-orange-50">
      <CardContent className="flex items-center justify-between p-4">
        <div className="flex items-center gap-2">
          <Badge variant="secondary" className="bg-orange-100 text-orange-800">
            {selectedItems.length} selected
          </Badge>
          <span className="text-sm text-muted-foreground">
            Bulk operations available
          </span>
        </div>
        
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={onClearSelection}
          >
            Clear Selection
          </Button>
          
          <Button
            variant="destructive"
            size="sm"
            onClick={handleBulkDelete}
            disabled={isProcessing}
            className="gap-2"
          >
            <Trash2 className="h-4 w-4" />
            {isProcessing ? 'Deleting...' : `Delete ${selectedItems.length}`}
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};

// System settings access card
export const SystemSettingsCard: React.FC = () => {
  const { user } = useAuth();

  if (!canAccessSystemSettings(user?.role)) {
    return null;
  }

  return (
    <Card className="border-red-200 bg-red-50">
      <CardHeader>
        <CardTitle className="flex items-center gap-2 text-red-800">
          <Settings className="h-5 w-5" />
          System Settings
        </CardTitle>
      </CardHeader>
      <CardContent>
        <p className="text-sm text-red-700 mb-4">
          SUPER_ADMIN access to system configuration and management tools.
        </p>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
          <Button variant="outline" size="sm" className="justify-start gap-2">
            <Users className="h-4 w-4" />
            User Management
          </Button>
          
          <Button variant="outline" size="sm" className="justify-start gap-2">
            <Database className="h-4 w-4" />
            Database Backup
          </Button>
          
          <Button variant="outline" size="sm" className="justify-start gap-2">
            <FileText className="h-4 w-4" />
            Audit Logs
          </Button>
          
          <Button variant="outline" size="sm" className="justify-start gap-2">
            <Settings className="h-4 w-4" />
            System Config
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};

// Security status indicator
export const SecurityStatusIndicator: React.FC = () => {
  const { user } = useAuth();
  const [sessionStatus, setSessionStatus] = useState<'active' | 'warning' | 'expired'>('active');

  React.useEffect(() => {
    const checkSession = () => {
      const lastActivity = SecurityUtils.getLastActivity();
      const isExpired = SecurityUtils.isSessionExpired(lastActivity);
      
      if (isExpired) {
        setSessionStatus('expired');
      } else if (Date.now() - lastActivity > 20 * 60 * 1000) { // 20 minutes warning
        setSessionStatus('warning');
      } else {
        setSessionStatus('active');
      }
    };

    checkSession();
    const interval = setInterval(checkSession, 60000); // Check every minute

    return () => clearInterval(interval);
  }, []);

  if (user?.role !== 'SUPER_ADMIN') {
    return null;
  }

  const getStatusColor = () => {
    switch (sessionStatus) {
      case 'active': return 'bg-green-100 text-green-800 border-green-200';
      case 'warning': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'expired': return 'bg-red-100 text-red-800 border-red-200';
    }
  };

  const getStatusText = () => {
    switch (sessionStatus) {
      case 'active': return 'Session Active';
      case 'warning': return 'Session Expiring Soon';
      case 'expired': return 'Session Expired';
    }
  };

  return (
    <div className={`flex items-center gap-2 px-3 py-1 rounded-full text-xs font-medium border ${getStatusColor()}`}>
      <Shield className="h-3 w-3" />
      {getStatusText()}
    </div>
  );
};

// Access denied component
interface AccessDeniedProps {
  requiredRole: string;
  action: string;
}

export const AccessDenied: React.FC<AccessDeniedProps> = ({ requiredRole, action }) => {
  return (
    <Card className="border-red-200 bg-red-50">
      <CardContent className="flex flex-col items-center justify-center py-12">
        <AlertTriangle className="h-16 w-16 text-red-500 mb-4" />
        <h3 className="text-lg font-semibold text-red-800 mb-2">Access Denied</h3>
        <p className="text-red-700 text-center mb-4">
          {requiredRole} role required to {action}.
        </p>
        <Badge variant="destructive" className="mb-4">
          Insufficient Permissions
        </Badge>
        <p className="text-xs text-red-600 text-center">
          Contact your system administrator if you believe this is an error.
        </p>
      </CardContent>
    </Card>
  );
};

// Export all components
export {
  SecureDeleteButton,
  BulkOperationsToolbar,
  SystemSettingsCard,
  SecurityStatusIndicator,
  AccessDenied,
};
