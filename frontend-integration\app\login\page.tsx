'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { useToast } from '@/hooks/use-toast';
import { useAuthStore } from '@/stores/authStore';

export default function LoginPage() {
  const [credentials, setCredentials] = useState({
    username: 'admin',
    password: 'admin123'
  });
  const [isLoading, setIsLoading] = useState(false);
  const router = useRouter();
  const { toast } = useToast();
  const { setAuth } = useAuthStore();

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    try {
      console.log('🔐 Attempting login with:', credentials.username);

      // Call backend login API directly
      const response = await fetch('http://127.0.0.1:8000/api/v1/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: new URLSearchParams({
          username: credentials.username,
          password: credentials.password,
        }),
      });

      const data = await response.json();
      console.log('🔐 Login response:', { status: response.status, data });

      if (response.ok && data.access_token) {
        // Store token in localStorage for immediate use
        localStorage.setItem('access_token', data.access_token);
        localStorage.setItem('role', 'SUPER_ADMIN'); // Set role for testing

        // Update auth store
        setAuth({
          token: data.access_token,
          role: 'SUPER_ADMIN',
          userId: 'admin-user-id',
          user: {
            id: 'admin-user-id',
            username: credentials.username,
            email: '<EMAIL>',
            name: 'Administrator',
            role: 'SUPER_ADMIN',
            is_active: true,
          }
        });

        toast({
          title: 'Login Successful',
          description: 'Welcome back, Administrator!',
        });

        // Redirect to dashboard
        router.push('/dashboard/classes');
      } else {
        throw new Error(data.detail || 'Login failed');
      }
    } catch (error) {
      console.error('❌ Login error:', error);
      toast({
        title: 'Login Failed',
        description: error instanceof Error ? error.message : 'Please check your credentials',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleQuickLogin = () => {
    // Set token directly for testing
    const testToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJhZG1pbiIsInJvbGUiOiJTVVBFUl9BRE1JTiIsImV4cCI6MTc1ODYyMzA4N30.hmLaGnULEVeC4R6Y65Cj8LMBa3OBbWDuZBCnsC-RD_I';

    localStorage.setItem('access_token', testToken);
    localStorage.setItem('role', 'SUPER_ADMIN');

    setAuth({
      token: testToken,
      role: 'SUPER_ADMIN',
      userId: 'admin-user-id',
      user: {
        id: 'admin-user-id',
        username: 'admin',
        email: '<EMAIL>',
        name: 'Administrator',
        role: 'SUPER_ADMIN',
        is_active: true,
      }
    });

    toast({
      title: 'Quick Login Successful',
      description: 'Using test token for SUPER_ADMIN access',
    });

    router.push('/dashboard/classes');
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <Card className="w-full max-w-md">
        <CardHeader>
          <CardTitle className="text-2xl text-center">🏫 School Management</CardTitle>
          <p className="text-center text-gray-600">Sign in to your account</p>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleLogin} className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="username">Username</Label>
              <Input
                id="username"
                type="text"
                value={credentials.username}
                onChange={(e) => setCredentials(prev => ({ ...prev, username: e.target.value }))}
                placeholder="Enter username"
                required
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="password">Password</Label>
              <Input
                id="password"
                type="password"
                value={credentials.password}
                onChange={(e) => setCredentials(prev => ({ ...prev, password: e.target.value }))}
                placeholder="Enter password"
                required
              />
            </div>
            <Button type="submit" className="w-full" disabled={isLoading}>
              {isLoading ? 'Signing in...' : 'Sign In'}
            </Button>
          </form>

          <div className="mt-4 pt-4 border-t">
            <Button
              onClick={handleQuickLogin}
              variant="outline"
              className="w-full"
              type="button"
            >
              🚀 Quick Login (Test Token)
            </Button>
          </div>

          <div className="mt-4 text-sm text-gray-600">
            <p><strong>Test Credentials:</strong></p>
            <p>Username: admin</p>
            <p>Password: admin123</p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
