/**
 * Class React Query Hooks
 *
 * Comprehensive hooks for class data management with:
 * - Query hooks for data fetching
 * - Mutation hooks for data modification
 * - Optimistic updates
 * - Cache management
 * - Error handling
 * - Loading states
 */

import { classService, type ClassFilters } from '@/api/services/classService';
import { Class, ClassCreate, ClassUpdate } from '@/schemas/zodSchemas';
import { useQueryClient } from '@tanstack/react-query';
import { useMutationBase } from './useMutationBase';
import { useQueryBase } from './useQueryBase';

// Query Keys
export const classKeys = {
  all: ['classes'] as const,
  lists: () => [...classKeys.all, 'list'] as const,
  list: (filters: ClassFilters) => [...classKeys.lists(), filters] as const,
  details: () => [...classKeys.all, 'detail'] as const,
  detail: (id: string) => [...classKeys.details(), id] as const,
  stats: () => [...classKeys.all, 'stats'] as const,
  search: (query: string) => [...classKeys.all, 'search', query] as const,
  byTeacher: (teacherId: string) => [...classKeys.all, 'teacher', teacherId] as const,
};

// Query Hooks
export function useClasses(filters: ClassFilters = {}) {
  return useQueryBase(
    ['classes', 'list', JSON.stringify(filters)],
    () => classService.getClasses(filters),
    {
      staleTime: 2 * 60 * 1000, // 2 minutes
    }
  );
}

export function useClass(id: string, enabled = true) {
  return useQueryBase([...classKeys.detail(id)], () => classService.getClass(id), {
    enabled: enabled && !!id,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

export function useClassStats() {
  return useQueryBase([...classKeys.stats()], () => classService.getClassStats(), {
    staleTime: 10 * 60 * 1000, // 10 minutes
  });
}

export function useSearchClasses(query: string, limit = 10) {
  return useQueryBase(
    [...classKeys.search(query)],
    () => classService.searchClasses(query, limit),
    {
      enabled: query.length >= 2, // Only search with 2+ characters
      staleTime: 30 * 1000, // 30 seconds
    }
  );
}

export function useClassesByTeacher(teacherId: string) {
  return useQueryBase(
    [...classKeys.byTeacher(teacherId)],
    () => classService.getClassesByTeacher(teacherId),
    {
      enabled: !!teacherId,
      staleTime: 5 * 60 * 1000, // 5 minutes
    }
  );
}

// Mutation Hooks
export function useCreateClass() {
  const queryClient = useQueryClient();

  return useMutationBase(
    (classData: ClassCreate) => classService.createClass(classData),
    {
      successMessage: 'Class created successfully!',
      errorMessage: 'Failed to create class',
      invalidateQueries: [[...classKeys.lists()], [...classKeys.stats()]],
      onSuccess: newClass => {
        // Add to cache optimistically
        queryClient.setQueryData(classKeys.detail(newClass.id), newClass);
      },
    }
  );
}

export function useUpdateClass() {
  const queryClient = useQueryClient();

  return useMutationBase(
    ({ id, data }: { id: string; data: ClassUpdate }) => classService.updateClass(id, data),
    {
      successMessage: 'Class updated successfully!',
      errorMessage: 'Failed to update class',
      invalidateQueries: [[...classKeys.lists()], [...classKeys.stats()]],
      onSuccess: (updatedClass, { id }) => {
        // Update cache
        queryClient.setQueryData(classKeys.detail(id), updatedClass);
      },
    }
  );
}

export function useDeleteClass() {
  const queryClient = useQueryClient();

  return useMutationBase((id: string) => classService.deleteClass(id), {
    successMessage: 'Class deleted successfully!',
    errorMessage: 'Failed to delete class',
    invalidateQueries: [[...classKeys.lists()], [...classKeys.stats()]],
    onSuccess: (_, id) => {
      // Remove from cache
      queryClient.removeQueries({ queryKey: classKeys.detail(id) });
    },
  });
}

export function useBulkUpdateClasses() {
  const queryClient = useQueryClient();

  return useMutationBase(
    (updates: Array<{ id: string; data: ClassUpdate }>) =>
      classService.bulkUpdateClasses(updates),
    {
      successMessage: 'Classes updated successfully!',
      errorMessage: 'Failed to update classes',
      invalidateQueries: [[...classKeys.lists()], [...classKeys.stats()]],
      onSuccess: updatedClasses => {
        // Update individual class caches
        updatedClasses.forEach(classItem => {
          queryClient.setQueryData(classKeys.detail(classItem.id), classItem);
        });
      },
    }
  );
}

export function useBulkDeleteClasses() {
  const queryClient = useQueryClient();

  return useMutationBase((ids: string[]) => classService.bulkDeleteClasses(ids), {
    successMessage: 'Classes deleted successfully!',
    errorMessage: 'Failed to delete classes',
    invalidateQueries: [[...classKeys.lists()], [...classKeys.stats()]],
    onSuccess: (_, ids) => {
      // Remove from cache
      ids.forEach(id => {
        queryClient.removeQueries({ queryKey: classKeys.detail(id) });
      });
    },
  });
}

// Utility hooks
export function useClassCache() {
  const queryClient = useQueryClient();

  return {
    // Prefetch class data
    prefetchClass: (id: string) => {
      return queryClient.prefetchQuery({
        queryKey: classKeys.detail(id),
        queryFn: () => classService.getClass(id),
        staleTime: 5 * 60 * 1000,
      });
    },

    // Prefetch classes list
    prefetchClasses: (filters: ClassFilters = {}) => {
      return queryClient.prefetchQuery({
        queryKey: classKeys.list(filters),
        queryFn: () => classService.getClasses(filters),
        staleTime: 2 * 60 * 1000,
      });
    },

    // Invalidate all class queries
    invalidateClasses: () => {
      return queryClient.invalidateQueries({ queryKey: classKeys.all });
    },

    // Clear class cache
    clearClassCache: () => {
      queryClient.removeQueries({ queryKey: classKeys.all });
    },

    // Get cached class data
    getCachedClass: (id: string): Class | undefined => {
      return queryClient.getQueryData(classKeys.detail(id));
    },

    // Set class data in cache
    setCachedClass: (id: string, classItem: Class) => {
      queryClient.setQueryData(classKeys.detail(id), classItem);
    },

    // Optimistically update class
    optimisticUpdateClass: (id: string, updates: Partial<Class>) => {
      queryClient.setQueryData(classKeys.detail(id), (old: Class | undefined) => {
        if (!old) return old;
        return { ...old, ...updates };
      });
    },
  };
}

// Export all hooks for convenience
export const classHooks = {
  // Queries
  useClasses,
  useClass,
  useClassStats,
  useSearchClasses,
  useClassesByTeacher,

  // Mutations
  useCreateClass,
  useUpdateClass,
  useDeleteClass,
  useBulkUpdateClasses,
  useBulkDeleteClasses,

  // Utilities
  useClassCache,

  // Keys
  classKeys,
};