'use client';

import { AlertTriangle } from 'lucide-react';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';

interface ConfirmLeaveDialogProps {
  open: boolean;
  onConfirm: () => void;
  onCancel: () => void;
}

/**
 * ConfirmLeaveDialog Component
 * 
 * Modal dialog to confirm navigation away from settings
 * when there are unsaved changes.
 * 
 * Features:
 * - Accessible alert dialog
 * - Clear warning about data loss
 * - Consistent styling with brand colors
 * - Keyboard navigation support
 */
export function ConfirmLeaveDialog({ open, onConfirm, onCancel }: ConfirmLeaveDialogProps) {
  return (
    <AlertDialog open={open} onOpenChange={(isOpen) => !isOpen && onCancel()}>
      <AlertDialogContent className="sm:max-w-md">
        <AlertDialogHeader>
          <div className="flex items-center gap-3">
            <div className="p-2 bg-amber-50 rounded-lg">
              <AlertTriangle className="h-5 w-5 text-amber-600" />
            </div>
            <div>
              <AlertDialogTitle className="text-lg font-semibold">
                Unsaved Changes
              </AlertDialogTitle>
            </div>
          </div>
          <AlertDialogDescription className="text-slate-600 mt-2">
            You have unsaved changes that will be lost if you leave this page. 
            Are you sure you want to continue without saving?
          </AlertDialogDescription>
        </AlertDialogHeader>
        
        <AlertDialogFooter className="gap-2">
          <AlertDialogCancel 
            onClick={onCancel}
            className="hover:bg-slate-50"
          >
            Stay on Page
          </AlertDialogCancel>
          <AlertDialogAction
            onClick={onConfirm}
            className="bg-red-600 hover:bg-red-700 text-white"
          >
            Leave Without Saving
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}
