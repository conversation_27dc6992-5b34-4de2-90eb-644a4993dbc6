/**
 * Protected Route Component
 *
 * Handles authentication and authorization for protected routes
 * Features:
 * - Authentication checking
 * - Role-based access control
 * - Automatic redirects
 * - Loading states
 * - Error handling
 */

'use client';

import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import { useAuth } from '@/hooks/useAuth';
import { UserRole } from '@/schemas/zodSchemas';
import { usePathname, useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';

interface ProtectedRouteProps {
  children: React.ReactNode;
  requiredRoles?: UserRole[];
  fallbackPath?: string;
  showUnauthorized?: boolean;
}

export function ProtectedRoute({
  children,
  requiredRoles = [],
  fallbackPath = '/login',
  showUnauthorized = true,
}: ProtectedRouteProps) {
  const { user, isAuthenticated, isLoading } = useAuth();
  const router = useRouter();
  const pathname = usePathname();
  const [isChecking, setIsChecking] = useState(true);

  useEffect(() => {
    const checkAuth = async () => {
      // 🚧 DEVELOPMENT MODE: Simplified auth check
      // TODO: Restore full auth logic when connecting to real backend

      console.log('🚧 DEV MODE: ProtectedRoute check', {
        isLoading,
        isAuthenticated,
        user: user?.name,
        pathname,
      });

      // Wait for auth state to be determined
      if (isLoading) {
        console.log('🚧 DEV MODE: Still loading auth state...');
        return;
      }

      // In development mode, be more lenient with authentication
      if (!isAuthenticated && !user) {
        console.log('🚧 DEV MODE: No auth found, redirecting to login');
        const returnUrl = encodeURIComponent(pathname);
        router.replace(`${fallbackPath}?returnUrl=${returnUrl}`);
        return;
      }

      // If we have some auth data, allow access (development mode)
      console.log('🚧 DEV MODE: Auth check passed, allowing access');
      setIsChecking(false);
    };

    checkAuth();
  }, [
    isAuthenticated,
    isLoading,
    user,
    requiredRoles,
    router,
    pathname,
    fallbackPath,
    showUnauthorized,
  ]);

  // Show loading state while checking authentication
  if (isLoading || isChecking) {
    return <LoadingState />;
  }

  // In development mode, skip role checks for now
  // TODO: Re-enable role checks when connecting to real backend
  console.log('🚧 DEV MODE: Rendering protected content');

  // Render protected content
  return <>{children}</>;
}

// Loading state component
function LoadingState() {
  return (
    <div className='min-h-screen flex items-center justify-center bg-background'>
      <Card className='w-full max-w-md'>
        <CardContent className='p-6'>
          <div className='flex flex-col items-center space-y-4'>
            <div className='h-12 w-12 bg-primary rounded-full flex items-center justify-center'>
              <span className='text-primary-foreground font-bold text-xl'>🎓</span>
            </div>
            <div className='space-y-2 text-center'>
              <Skeleton className='h-4 w-48' />
              <Skeleton className='h-4 w-32' />
            </div>
            <div className='flex space-x-1'>
              <div className='w-2 h-2 bg-primary rounded-full animate-bounce'></div>
              <div
                className='w-2 h-2 bg-primary rounded-full animate-bounce'
                style={{ animationDelay: '0.1s' }}
              ></div>
              <div
                className='w-2 h-2 bg-primary rounded-full animate-bounce'
                style={{ animationDelay: '0.2s' }}
              ></div>
            </div>
            <p className='text-sm text-muted-foreground'>Checking authentication...</p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

// Unauthorized state component
function UnauthorizedState({
  requiredRoles,
  userRole,
}: {
  requiredRoles: UserRole[];
  userRole: UserRole;
}) {
  const router = useRouter();

  return (
    <div className='min-h-screen flex items-center justify-center bg-background'>
      <Card className='w-full max-w-md'>
        <CardContent className='p-6'>
          <div className='flex flex-col items-center space-y-4 text-center'>
            <div className='h-16 w-16 bg-destructive/10 rounded-full flex items-center justify-center'>
              <span className='text-destructive text-2xl'>🚫</span>
            </div>

            <div className='space-y-2'>
              <h2 className='text-xl font-semibold text-foreground'>Access Denied</h2>
              <p className='text-sm text-muted-foreground'>
                You don't have permission to access this page.
              </p>
            </div>

            <div className='space-y-2 text-xs text-muted-foreground'>
              <p>
                Your role: <span className='font-medium'>{userRole}</span>
              </p>
              <p>
                Required roles: <span className='font-medium'>{requiredRoles.join(', ')}</span>
              </p>
            </div>

            <div className='flex flex-col sm:flex-row gap-2 w-full'>
              <Button variant='outline' onClick={() => router.back()} className='flex-1'>
                Go Back
              </Button>
              <Button onClick={() => router.push('/dashboard')} className='flex-1'>
                Dashboard
              </Button>
            </div>

            <p className='text-xs text-muted-foreground'>
              Contact your administrator if you believe this is an error.
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

// Higher-order component for easy route protection
export function withAuth<P extends object>(
  Component: React.ComponentType<P>,
  requiredRoles?: UserRole[]
) {
  return function AuthenticatedComponent(props: P) {
    const protectedRouteProps: { children: React.ReactNode; requiredRoles?: UserRole[] } = {
      children: <Component {...props} />,
    };

    if (requiredRoles) {
      protectedRouteProps.requiredRoles = requiredRoles;
    }

    return <ProtectedRoute {...protectedRouteProps} />;
  };
}

// Hook for checking specific permissions
export function usePermissions() {
  const { user, isAuthenticated } = useAuth();

  // Get role from localStorage as fallback (for JWT-based auth)
  const getStoredRole = (): string | null => {
    if (typeof window === 'undefined') return null;
    return localStorage.getItem('user_role');
  };

  const currentRole = user?.role || getStoredRole();

  return {
    canAccess: (requiredRoles: UserRole[]) => {
      if (!isAuthenticated && !currentRole) return false;
      return requiredRoles.length === 0 || requiredRoles.includes(currentRole as UserRole);
    },

    isAdmin: () => {
      const role = currentRole;
      return role === 'ADMIN' || role === 'SUPER_ADMIN';
    },

    isTeacher: () => {
      return currentRole === 'TEACHER';
    },

    isStudent: () => {
      return currentRole === 'STUDENT';
    },

    isParent: () => {
      return currentRole === 'PARENT';
    },

    hasAnyRole: (roles: UserRole[]) => {
      if (!currentRole) return false;
      return roles.includes(currentRole as UserRole);
    },

    getCurrentRole: () => {
      return currentRole;
    },
  };
}
