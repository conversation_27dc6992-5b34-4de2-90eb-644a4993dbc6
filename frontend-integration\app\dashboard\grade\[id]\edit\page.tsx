'use client';

import { <PERSON><PERSON><PERSON><PERSON>, Award, Save } from 'lucide-react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';

import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';

// Mock data for demonstration
const mockGradeRecords = [
  {
    id: 1,
    studentName: '<PERSON>',
    studentId: 'STU001',
    class: 'Grade 10',
    subject: 'Mathematics',
    examType: 'Final Exam',
    examDate: '2024-02-15',
    totalMarks: 100,
    obtainedMarks: 85,
    percentage: 85,
    grade: 'A',
    remarks: 'Excellent performance in algebra and geometry',
    teacherName: '<PERSON><PERSON> <PERSON>',
    date: '2024-02-20',
  },
];

interface GradeFormData {
  studentId: string;
  studentName: string;
  class: string;
  subject: string;
  examType: string;
  examDate: string;
  totalMarks: string;
  obtainedMarks: string;
  grade: string;
  percentage: string;
  remarks: string;
  teacherId: string;
}

export default function EditGradePage({ params }: { params: { id: string } }) {
  const router = useRouter();
  const [formData, setFormData] = useState<GradeFormData | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    const record = mockGradeRecords.find(r => r.id === Number(params.id));
    if (record) {
      setFormData({
        studentId: record.studentId,
        studentName: record.studentName,
        class: record.class,
        subject: record.subject,
        examType: record.examType,
        examDate: record.examDate,
        totalMarks: record.totalMarks.toString(),
        obtainedMarks: record.obtainedMarks.toString(),
        grade: record.grade,
        percentage: record.percentage.toString(),
        remarks: record.remarks,
        teacherId: 'TEACHER001',
      });
    }
  }, [params.id]);

  if (!formData) {
    return <p className='p-6'>Grade record not found</p>;
  }

  const handleChange = (field: keyof GradeFormData, value: string) => {
    setFormData(prev => prev ? { ...prev, [field]: value } : null);
  };

  const handleMarksChange = (field: 'obtainedMarks' | 'totalMarks', value: string) => {
    if (!formData) return;
    
    const updatedData = { ...formData, [field]: value };
    
    // Calculate percentage and grade if both marks are available
    if (updatedData.obtainedMarks && updatedData.totalMarks) {
      const obtained = parseFloat(updatedData.obtainedMarks);
      const total = parseFloat(updatedData.totalMarks);
      
      if (!isNaN(obtained) && !isNaN(total) && total > 0) {
        const percentage = (obtained / total) * 100;
        updatedData.percentage = percentage.toFixed(1);
        
        // Calculate grade based on percentage
        if (percentage >= 90) updatedData.grade = 'A+';
        else if (percentage >= 80) updatedData.grade = 'A';
        else if (percentage >= 70) updatedData.grade = 'B+';
        else if (percentage >= 60) updatedData.grade = 'B';
        else if (percentage >= 50) updatedData.grade = 'C+';
        else if (percentage >= 40) updatedData.grade = 'C';
        else if (percentage >= 33) updatedData.grade = 'D';
        else updatedData.grade = 'F';
      }
    }
    
    setFormData(updatedData);
  };

  const handleSave = async () => {
    setIsLoading(true);
    try {
      await new Promise(resolve => setTimeout(resolve, 1000));
      console.log('Updated grade:', formData);
      router.push(`/dashboard/grade/${params.id}`);
    } catch (error) {
      console.error('Error updating grade:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const subjects = ['Mathematics', 'Physics', 'Chemistry', 'Biology', 'English', 'History'];
  const examTypes = ['Quiz', 'Mid-term', 'Final Exam', 'Assignment', 'Project'];

  return (
    <div className='container mx-auto p-4 sm:p-6 lg:p-8 max-w-4xl'>
      <div className='flex items-center gap-4 mb-8'>
        <Link href={`/dashboard/grade/${params.id}`}>
          <Button variant='outline' size='sm'>
            <ArrowLeft className='w-4 h-4 mr-2' />
            Back to Grade
          </Button>
        </Link>
        <div>
          <h1 className='text-3xl font-bold text-gray-900 flex items-center gap-2'>
            <Award className='w-8 h-8 text-purple-600' />
            Edit Grade
          </h1>
          <p className='text-gray-600 mt-1'>Update grade record details</p>
        </div>
      </div>

      <div className='space-y-6'>
        {/* Student Information */}
        <Card>
          <CardHeader>
            <CardTitle>Student Information</CardTitle>
            <CardDescription>Basic student details (read-only)</CardDescription>
          </CardHeader>
          <CardContent>
            <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
              <div>
                <Label>Student Name</Label>
                <Input value={formData.studentName} disabled />
              </div>
              <div>
                <Label>Student ID</Label>
                <Input value={formData.studentId} disabled />
              </div>
              <div>
                <Label>Class</Label>
                <Input value={formData.class} disabled />
              </div>
              <div>
                <Label>Subject</Label>
                <Select value={formData.subject} onValueChange={(value) => handleChange('subject', value)}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {subjects.map(subject => (
                      <SelectItem key={subject} value={subject}>{subject}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Exam Information */}
        <Card>
          <CardHeader>
            <CardTitle>Exam Information</CardTitle>
            <CardDescription>Update exam details</CardDescription>
          </CardHeader>
          <CardContent>
            <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
              <div>
                <Label htmlFor='examType'>Exam Type</Label>
                <Select value={formData.examType} onValueChange={(value) => handleChange('examType', value)}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {examTypes.map(type => (
                      <SelectItem key={type} value={type}>{type}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Label htmlFor='examDate'>Exam Date</Label>
                <Input
                  id='examDate'
                  type='date'
                  value={formData.examDate}
                  onChange={(e) => handleChange('examDate', e.target.value)}
                />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Grade Information */}
        <Card>
          <CardHeader>
            <CardTitle>Grade Information</CardTitle>
            <CardDescription>Enter marks and view calculated grade</CardDescription>
          </CardHeader>
          <CardContent>
            <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4'>
              <div>
                <Label htmlFor='totalMarks'>Total Marks</Label>
                <Input
                  id='totalMarks'
                  type='number'
                  value={formData.totalMarks}
                  onChange={(e) => handleMarksChange('totalMarks', e.target.value)}
                />
              </div>
              <div>
                <Label htmlFor='obtainedMarks'>Obtained Marks</Label>
                <Input
                  id='obtainedMarks'
                  type='number'
                  value={formData.obtainedMarks}
                  onChange={(e) => handleMarksChange('obtainedMarks', e.target.value)}
                />
              </div>
              <div>
                <Label>Percentage</Label>
                <Input value={formData.percentage ? `${formData.percentage}%` : ''} disabled />
              </div>
              <div>
                <Label>Grade</Label>
                <Input value={formData.grade} disabled />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Remarks */}
        <Card>
          <CardHeader>
            <CardTitle>Teacher's Remarks</CardTitle>
            <CardDescription>Add comments about student performance</CardDescription>
          </CardHeader>
          <CardContent>
            <Textarea
              placeholder='Enter remarks about student performance...'
              value={formData.remarks}
              onChange={(e) => handleChange('remarks', e.target.value)}
              rows={4}
            />
          </CardContent>
        </Card>

        {/* Actions */}
        <div className='flex justify-end gap-4'>
          <Link href={`/dashboard/grade/${params.id}`}>
            <Button variant='outline'>Cancel</Button>
          </Link>
          <Button onClick={handleSave} disabled={isLoading}>
            <Save className='w-4 h-4 mr-2' />
            {isLoading ? 'Saving...' : 'Save Changes'}
          </Button>
        </div>
      </div>
    </div>
  );
}
