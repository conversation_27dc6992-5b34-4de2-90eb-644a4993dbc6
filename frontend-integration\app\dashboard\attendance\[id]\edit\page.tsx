'use client';

import { <PERSON><PERSON> } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { ArrowLeft, Save, Users } from 'lucide-react';
import Link from 'next/link';
import { useParams, useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';

// Mock attendance data
const mockAttendance = [
  {
    id: 1,
    studentName: '<PERSON>',
    studentId: 'STU001',
    grade: 'Grade 10',
    date: '2024-03-15',
    status: 'Present',
    timeIn: '08:00',
    timeOut: '15:30',
    subject: 'Mathematics',
    teacher: 'Dr. <PERSON>',
    remarks: '',
  },
  {
    id: 2,
    studentName: '<PERSON>',
    studentId: 'STU002',
    grade: 'Grade 9',
    date: '2024-03-15',
    status: 'Absent',
    timeIn: '',
    timeOut: '',
    subject: 'Physics',
    teacher: 'Prof. <PERSON> <PERSON>',
    remarks: 'Sick leave',
  },
];

export default function EditAttendancePage() {
  const { id } = useParams();
  const router = useRouter();
  const [formData, setFormData] = useState<Record<string, any> | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    const record = mockAttendance.find(r => r.id === Number(id));
    if (record) {
      setFormData(record);
    }
  }, [id]);

  if (!formData) {
    return <p className='p-6'>Attendance record not found</p>;
  }

  const handleChange = (field: string, value: any) => {
    setFormData((prev: Record<string, any> | null) => {
      if (!prev) return prev;
      return { ...prev, [field]: value };
    });
  };

  const handleSave = async () => {
    setIsLoading(true);
    try {
      await new Promise(resolve => setTimeout(resolve, 1000));
      console.log('Updated attendance:', formData);
      router.push(`/dashboard/attendance/${id}`);
    } catch (error) {
      console.error('Error updating attendance:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const statuses = ['Present', 'Absent', 'Late', 'Excused'];
  const subjects = ['Mathematics', 'Physics', 'Chemistry', 'Biology', 'English', 'History'];
  const grades = ['Grade 9', 'Grade 10', 'Grade 11', 'Grade 12'];
  const teachers = ['Dr. Sarah Johnson', 'Prof. Michael Chen', 'Ms. Emily Davis', 'Mr. John Smith'];

  return (
    <div className='container mx-auto p-4 sm:p-6 lg:p-8 max-w-4xl'>
      <div className='flex items-center gap-4 mb-8'>
        <Link href={`/dashboard/attendance/${id}`}>
          <Button variant='outline' size='sm'>
            <ArrowLeft className='w-4 h-4 mr-2' />
            Back to Record
          </Button>
        </Link>
        <div>
          <h1 className='text-3xl font-bold text-gray-900 flex items-center gap-2'>
            <Users className='w-8 h-8 text-blue-600' />
            Edit Attendance
          </h1>
          <p className='text-gray-600 mt-1'>Update attendance record details</p>
        </div>
      </div>

      <div className='space-y-8'>
        <Card>
          <CardHeader>
            <CardTitle>Student Information</CardTitle>
          </CardHeader>
          <CardContent className='space-y-6'>
            <div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
              <div className='space-y-2'>
                <Label htmlFor='studentName'>Student Name *</Label>
                <Input
                  id='studentName'
                  value={formData.studentName}
                  onChange={e => handleChange('studentName', e.target.value)}
                  required
                />
              </div>
              <div className='space-y-2'>
                <Label htmlFor='studentId'>Student ID *</Label>
                <Input
                  id='studentId'
                  value={formData.studentId}
                  onChange={e => handleChange('studentId', e.target.value)}
                  required
                />
              </div>
            </div>

            <div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
              <div className='space-y-2'>
                <Label htmlFor='grade'>Grade *</Label>
                <Select value={formData.grade} onValueChange={value => handleChange('grade', value)}>
                  <SelectTrigger>
                    <SelectValue placeholder='Select grade' />
                  </SelectTrigger>
                  <SelectContent>
                    {grades.map(grade => (
                      <SelectItem key={grade} value={grade}>
                        {grade}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div className='space-y-2'>
                <Label htmlFor='date'>Date *</Label>
                <Input
                  id='date'
                  type='date'
                  value={formData.date}
                  onChange={e => handleChange('date', e.target.value)}
                  required
                />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Attendance Details</CardTitle>
          </CardHeader>
          <CardContent className='space-y-6'>
            <div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
              <div className='space-y-2'>
                <Label htmlFor='subject'>Subject *</Label>
                <Select value={formData.subject} onValueChange={value => handleChange('subject', value)}>
                  <SelectTrigger>
                    <SelectValue placeholder='Select subject' />
                  </SelectTrigger>
                  <SelectContent>
                    {subjects.map(subject => (
                      <SelectItem key={subject} value={subject}>
                        {subject}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div className='space-y-2'>
                <Label htmlFor='teacher'>Teacher *</Label>
                <Select value={formData.teacher} onValueChange={value => handleChange('teacher', value)}>
                  <SelectTrigger>
                    <SelectValue placeholder='Select teacher' />
                  </SelectTrigger>
                  <SelectContent>
                    {teachers.map(teacher => (
                      <SelectItem key={teacher} value={teacher}>
                        {teacher}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className='space-y-2'>
              <Label htmlFor='status'>Status *</Label>
              <Select value={formData.status} onValueChange={value => handleChange('status', value)}>
                <SelectTrigger>
                  <SelectValue placeholder='Select status' />
                </SelectTrigger>
                <SelectContent>
                  {statuses.map(status => (
                    <SelectItem key={status} value={status}>
                      {status}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {(formData.status === 'Present' || formData.status === 'Late') && (
              <div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
                <div className='space-y-2'>
                  <Label htmlFor='timeIn'>Time In</Label>
                  <Input
                    id='timeIn'
                    type='time'
                    value={formData.timeIn}
                    onChange={e => handleChange('timeIn', e.target.value)}
                  />
                </div>
                <div className='space-y-2'>
                  <Label htmlFor='timeOut'>Time Out</Label>
                  <Input
                    id='timeOut'
                    type='time'
                    value={formData.timeOut}
                    onChange={e => handleChange('timeOut', e.target.value)}
                  />
                </div>
              </div>
            )}

            <div className='space-y-2'>
              <Label htmlFor='remarks'>Remarks</Label>
              <Textarea
                id='remarks'
                placeholder='Enter any remarks or notes...'
                value={formData.remarks}
                onChange={e => handleChange('remarks', e.target.value)}
                rows={4}
              />
            </div>
          </CardContent>
        </Card>

        <div className='flex justify-end gap-4'>
          <Link href={`/dashboard/attendance/${id}`}>
            <Button type='button' variant='outline'>
              Cancel
            </Button>
          </Link>
          <Button onClick={handleSave} disabled={isLoading}>
            {isLoading ? (
              <>
                <div className='w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2' />
                Updating...
              </>
            ) : (
              <>
                <Save className='w-4 h-4 mr-2' />
                Update Record
              </>
            )}
          </Button>
        </div>
      </div>
    </div>
  );
}
