/**
 * Classes Loading Component
 *
 * Professional loading skeleton for the classes page
 */

import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';

export default function ClassesLoading() {
  return (
    <div className='container mx-auto p-4 sm:p-6 lg:p-8 space-y-8'>
      {/* Header Skeleton */}
      <div className='flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4'>
        <div className='space-y-2'>
          <Skeleton className='h-10 w-48' />
          <Skeleton className='h-4 w-96' />
        </div>
        <div className='flex items-center space-x-2'>
          <Skeleton className='h-10 w-32' />
          <Skeleton className='h-10 w-40' />
        </div>
      </div>

      {/* Stats Cards Skeleton */}
      <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6'>
        {Array.from({ length: 4 }).map((_, i) => (
          <Card key={i} className='border-0 shadow-lg bg-gradient-to-br from-white to-gray-50'>
            <CardContent className='p-6'>
              <div className='flex items-center justify-between'>
                <div className='space-y-2'>
                  <Skeleton className='h-4 w-20' />
                  <Skeleton className='h-8 w-16' />
                  <Skeleton className='h-3 w-24' />
                </div>
                <Skeleton className='h-12 w-12 rounded-xl' />
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Filters and Search Skeleton */}
      <Card className='border-0 shadow-lg'>
        <CardHeader>
          <div className='flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4'>
            <Skeleton className='h-6 w-32' />
            <div className='flex items-center space-x-2'>
              <Skeleton className='h-10 w-10' />
              <Skeleton className='h-10 w-10' />
            </div>
          </div>
        </CardHeader>
        <CardContent className='space-y-4'>
          {/* Search and Filters */}
          <div className='flex flex-col sm:flex-row gap-4'>
            <Skeleton className='h-10 flex-1' />
            <Skeleton className='h-10 w-40' />
            <Skeleton className='h-10 w-32' />
            <Skeleton className='h-10 w-28' />
          </div>
        </CardContent>
      </Card>

      {/* Students Grid/Table Skeleton */}
      <Card className='border-0 shadow-lg'>
        <CardContent className='p-6'>
          {/* View Toggle Skeleton */}
          <div className='flex items-center justify-between mb-6'>
            <Skeleton className='h-4 w-32' />
            <div className='flex items-center space-x-2'>
              <Skeleton className='h-8 w-16' />
              <Skeleton className='h-8 w-16' />
            </div>
          </div>

          {/* Grid View Skeleton */}
          <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6'>
            {Array.from({ length: 8 }).map((_, i) => (
              <Card key={i} className='border border-gray-200 hover:shadow-md transition-shadow'>
                <CardContent className='p-4'>
                  <div className='flex items-center space-x-3 mb-3'>
                    <Skeleton className='h-12 w-12 rounded-full' />
                    <div className='flex-1 space-y-1'>
                      <Skeleton className='h-4 w-24' />
                      <Skeleton className='h-3 w-32' />
                    </div>
                  </div>

                  <div className='space-y-2'>
                    <div className='flex items-center space-x-2'>
                      <Skeleton className='h-3 w-3' />
                      <Skeleton className='h-3 w-20' />
                    </div>
                    <div className='flex items-center space-x-2'>
                      <Skeleton className='h-3 w-3' />
                      <Skeleton className='h-3 w-16' />
                    </div>
                    <div className='flex items-center space-x-2'>
                      <Skeleton className='h-3 w-3' />
                      <Skeleton className='h-3 w-24' />
                    </div>
                  </div>

                  <div className='flex items-center justify-between mt-4'>
                    <Skeleton className='h-5 w-16 rounded-full' />
                    <div className='flex items-center space-x-1'>
                      <Skeleton className='h-6 w-6' />
                      <Skeleton className='h-6 w-6' />
                      <Skeleton className='h-6 w-6' />
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          {/* Pagination Skeleton */}
          <div className='flex items-center justify-between mt-8'>
            <Skeleton className='h-4 w-48' />
            <div className='flex items-center space-x-2'>
              <Skeleton className='h-8 w-8' />
              <Skeleton className='h-8 w-8' />
              <Skeleton className='h-8 w-8' />
              <Skeleton className='h-8 w-8' />
              <Skeleton className='h-8 w-8' />
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
