/**
 * Attendance Schema - Zod Validation
 * 
 * Features:
 * - Attendance marking validation
 * - Bulk attendance processing
 * - Date range validation
 * - Status validation
 * - Report generation validation
 */

import { z } from 'zod';
import { useForm, UseFormReturn } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';

// Attendance status enum
export const attendanceStatusSchema = z.enum(['PRESENT', 'ABSENT', 'LATE', 'EXCUSED'], {
  required_error: 'Attendance status is required',
});

// Single attendance record schema
export const attendanceRecordSchema = z.object({
  student_id: z.string().min(1, 'Student ID is required'),
  class_id: z.string().min(1, 'Class ID is required'),
  date: z.string().min(1, 'Date is required').refine((date) => {
    const attendanceDate = new Date(date);
    const today = new Date();
    today.setHours(23, 59, 59, 999);
    return attendanceDate <= today;
  }, 'Attendance date cannot be in the future'),
  status: attendanceStatusSchema,
  notes: z.string().max(500, 'Notes must be less than 500 characters').optional(),
  marked_by: z.string().min(1, 'Marked by is required'),
  marked_at: z.string().optional(),
});

// Bulk attendance schema
export const bulkAttendanceSchema = z.object({
  class_id: z.string().min(1, 'Class ID is required'),
  date: z.string().min(1, 'Date is required'),
  attendance_records: z.array(z.object({
    student_id: z.string().min(1, 'Student ID is required'),
    status: attendanceStatusSchema,
    notes: z.string().max(500).optional(),
  })).min(1, 'At least one attendance record is required'),
  marked_by: z.string().min(1, 'Marked by is required'),
});

// Attendance filter schema
export const attendanceFilterSchema = z.object({
  class_id: z.string().optional(),
  student_id: z.string().optional(),
  date_from: z.string().optional(),
  date_to: z.string().optional(),
  status: attendanceStatusSchema.optional(),
  page: z.number().min(1).default(1),
  limit: z.number().min(1).max(100).default(20),
});

// Attendance report schema
export const attendanceReportSchema = z.object({
  report_type: z.enum(['DAILY', 'WEEKLY', 'MONTHLY', 'CUSTOM'], {
    required_error: 'Report type is required',
  }),
  class_id: z.string().optional(),
  student_id: z.string().optional(),
  date_from: z.string().min(1, 'Start date is required'),
  date_to: z.string().min(1, 'End date is required').refine((date, ctx) => {
    const startDate = new Date(ctx.parent.date_from);
    const endDate = new Date(date);
    return endDate >= startDate;
  }, 'End date must be after start date'),
  include_summary: z.boolean().default(true),
  format: z.enum(['PDF', 'EXCEL', 'CSV']).default('PDF'),
});

// Type exports
export type AttendanceRecord = z.infer<typeof attendanceRecordSchema>;
export type BulkAttendanceInput = z.infer<typeof bulkAttendanceSchema>;
export type AttendanceFilterInput = z.infer<typeof attendanceFilterSchema>;
export type AttendanceReportInput = z.infer<typeof attendanceReportSchema>;
export type AttendanceStatus = z.infer<typeof attendanceStatusSchema>;

// Custom hooks
export function useAttendanceForm(
  defaultValues?: Partial<AttendanceRecord>
): UseFormReturn<AttendanceRecord> {
  return useForm({
    resolver: zodResolver(attendanceRecordSchema),
    defaultValues: {
      date: new Date().toISOString().split('T')[0],
      status: 'PRESENT',
      ...defaultValues,
    },
    mode: 'onChange',
  });
}

export function useBulkAttendanceForm(
  defaultValues?: Partial<BulkAttendanceInput>
): UseFormReturn<BulkAttendanceInput> {
  return useForm({
    resolver: zodResolver(bulkAttendanceSchema),
    defaultValues: {
      date: new Date().toISOString().split('T')[0],
      attendance_records: [],
      ...defaultValues,
    },
    mode: 'onChange',
  });
}

// Validation functions
export function validateAttendanceDate(date: string): boolean {
  const attendanceDate = new Date(date);
  const today = new Date();
  const thirtyDaysAgo = new Date();
  thirtyDaysAgo.setDate(today.getDate() - 30);
  
  return attendanceDate >= thirtyDaysAgo && attendanceDate <= today;
}

export function calculateAttendancePercentage(
  present: number,
  total: number
): number {
  if (total === 0) return 0;
  return Math.round((present / total) * 100);
}

export function getAttendanceStatusColor(status: AttendanceStatus): string {
  switch (status) {
    case 'PRESENT':
      return 'text-green-600 bg-green-50';
    case 'ABSENT':
      return 'text-red-600 bg-red-50';
    case 'LATE':
      return 'text-yellow-600 bg-yellow-50';
    case 'EXCUSED':
      return 'text-blue-600 bg-blue-50';
    default:
      return 'text-gray-600 bg-gray-50';
  }
}