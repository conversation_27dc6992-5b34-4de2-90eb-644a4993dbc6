'use client';

import { useState, useEffect, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import { Save, RotateCcw, Clock, AlertTriangle } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/hooks/use-toast';
import { SettingsTabs } from './SettingsTabs';
import { ConfirmLeaveDialog } from './ConfirmLeaveDialog';

/**
 * SettingsShell Component
 * 
 * Main container for settings with:
 * - Sticky header with save/reset actions
 * - Unsaved changes tracking
 * - Last saved timestamp
 * - Navigation guard for unsaved changes
 */
export function SettingsShell() {
  const router = useRouter();
  const { toast } = useToast();
  
  // State management
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [isResetting, setIsResetting] = useState(false);
  const [lastSaved, setLastSaved] = useState<Date | null>(null);
  const [showLeaveDialog, setShowLeaveDialog] = useState(false);
  const [pendingNavigation, setPendingNavigation] = useState<string | null>(null);

  // Format last saved time
  const formatLastSaved = useCallback((date: Date) => {
    const now = new Date();
    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));
    
    if (diffInMinutes < 1) return 'Just now';
    if (diffInMinutes < 60) return `${diffInMinutes} minute${diffInMinutes > 1 ? 's' : ''} ago`;
    
    const diffInHours = Math.floor(diffInMinutes / 60);
    if (diffInHours < 24) return `${diffInHours} hour${diffInHours > 1 ? 's' : ''} ago`;
    
    return date.toLocaleDateString();
  }, []);

  // Handle save action
  const handleSave = async () => {
    setIsSaving(true);
    try {
      // TODO: Implement actual save logic with API calls
      await new Promise(resolve => setTimeout(resolve, 1000)); // Simulate API call
      
      setHasUnsavedChanges(false);
      setLastSaved(new Date());
      
      toast({
        title: 'Settings saved',
        description: 'Your settings have been saved successfully.',
      });
    } catch (error) {
      toast({
        title: 'Save failed',
        description: 'Failed to save settings. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsSaving(false);
    }
  };

  // Handle reset action
  const handleReset = async () => {
    setIsResetting(true);
    try {
      // TODO: Implement actual reset logic
      await new Promise(resolve => setTimeout(resolve, 500)); // Simulate API call
      
      setHasUnsavedChanges(false);
      
      toast({
        title: 'Settings reset',
        description: 'Settings have been reset to their last saved state.',
      });
    } catch (error) {
      toast({
        title: 'Reset failed',
        description: 'Failed to reset settings. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsResetting(false);
    }
  };

  // Handle navigation with unsaved changes
  const handleNavigation = useCallback((url: string) => {
    if (hasUnsavedChanges) {
      setPendingNavigation(url);
      setShowLeaveDialog(true);
    } else {
      router.push(url);
    }
  }, [hasUnsavedChanges, router]);

  // Confirm leave without saving
  const handleConfirmLeave = () => {
    if (pendingNavigation) {
      router.push(pendingNavigation);
    }
    setShowLeaveDialog(false);
    setPendingNavigation(null);
    setHasUnsavedChanges(false);
  };

  // Cancel leave
  const handleCancelLeave = () => {
    setShowLeaveDialog(false);
    setPendingNavigation(null);
  };

  // Browser navigation guard
  useEffect(() => {
    const handleBeforeUnload = (e: BeforeUnloadEvent) => {
      if (hasUnsavedChanges) {
        e.preventDefault();
        e.returnValue = '';
      }
    };

    window.addEventListener('beforeunload', handleBeforeUnload);
    return () => window.removeEventListener('beforeunload', handleBeforeUnload);
  }, [hasUnsavedChanges]);

  return (
    <div className="space-y-6">
      {/* Sticky Header */}
      <div className="sticky top-0 z-10 bg-white/95 backdrop-blur-sm border-b border-slate-200 -mx-6 px-6 py-4">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          {/* Status Info */}
          <div className="flex items-center gap-4">
            {hasUnsavedChanges && (
              <Badge variant="outline" className="bg-amber-50 text-amber-700 border-amber-200">
                <AlertTriangle className="h-3 w-3 mr-1" />
                Unsaved changes
              </Badge>
            )}
            
            {lastSaved && (
              <div className="flex items-center gap-2 text-sm text-slate-600">
                <Clock className="h-4 w-4" />
                <span>Last saved {formatLastSaved(lastSaved)}</span>
              </div>
            )}
          </div>

          {/* Actions */}
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={handleReset}
              disabled={!hasUnsavedChanges || isResetting || isSaving}
            >
              <RotateCcw className="h-4 w-4 mr-2" />
              {isResetting ? 'Resetting...' : 'Reset'}
            </Button>
            
            <Button
              size="sm"
              onClick={handleSave}
              disabled={!hasUnsavedChanges || isSaving || isResetting}
              className="bg-gradient-to-r from-sky-600 to-violet-600 hover:from-sky-700 hover:to-violet-700"
            >
              <Save className="h-4 w-4 mr-2" />
              {isSaving ? 'Saving...' : 'Save Changes'}
            </Button>
          </div>
        </div>
      </div>

      {/* Settings Content */}
      <Card className="rounded-2xl shadow-md border-0 bg-gradient-to-br from-slate-50 to-slate-100">
        <CardContent className="p-0">
          <SettingsTabs 
            onDataChange={() => setHasUnsavedChanges(true)}
            onNavigation={handleNavigation}
          />
        </CardContent>
      </Card>

      {/* Confirm Leave Dialog */}
      <ConfirmLeaveDialog
        open={showLeaveDialog}
        onConfirm={handleConfirmLeave}
        onCancel={handleCancelLeave}
      />
    </div>
  );
}
