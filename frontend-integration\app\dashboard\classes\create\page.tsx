'use client';

import { <PERSON><PERSON><PERSON><PERSON>gle, <PERSON><PERSON>ef<PERSON> } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';

import { ClassForm } from '@/components/forms/ClassForm';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { useToast } from '@/hooks/use-toast';
import { useAuth } from '@/hooks/useAuth';
import { useCreateClass } from '@/hooks/useClasses';
import { useTeachers } from '@/hooks/useTeachers';
import { canCreateClass } from '@/lib/permissions';
import type { ClassCreate } from '@/schemas/zodSchemas';

export default function ClassCreatePage() {
  const router = useRouter();
  const { toast } = useToast();
  const { user } = useAuth();
  const [teachers, setTeachers] = useState<Array<{ id: string; name: string }>>([]);

  // Check permissions
  useEffect(() => {
    if (!canCreateClass(user?.role)) {
      toast({
        title: 'Access Denied',
        description: 'Only administrators can create classes',
        variant: 'destructive',
      });
      router.push('/dashboard/classes');
    }
  }, [user?.role, router, toast]);

  // Fetch teachers for dropdown
  const { data: teachersData } = useTeachers();
  useEffect(() => {
    if (teachersData?.data) {
      setTeachers(teachersData.data.map(t => ({ id: t.id, name: t.name || 'Unknown' })));
    }
  }, [teachersData]);

  const createClassMutation = useCreateClass();

  const handleSubmit = async (data: ClassCreate) => {
    try {
      // Transform data to match backend schema
      const payload = {
        name: data.name,
        class_name: data.name, // Backend expects both name and class_name
        section: data.section,
        grade: data.grade?.startsWith('Grade ') ? data.grade : `Grade ${data.grade}`, // Ensure "Grade X" format
        level: data.level ?? 'Standard',
        subject: data.subject ?? 'General',
        teacher_id: data.teacher_id, // Already converted by schema transform
        capacity: Number(data.capacity),
        room_number: data.room_number ?? '',
        building: data.building ?? 'Main Building',
        floor: data.floor ?? 1,
        schedule: data.schedule ?? '',
        duration_minutes: data.duration_minutes ?? 60,
        description: data.description ?? '',
        prerequisites: data.prerequisites ?? '',
        is_active: data.is_active !== false, // Default to true
        academic_year: data.academic_year?.includes('-')
          ? data.academic_year
          : `${data.academic_year}-${parseInt(data.academic_year) + 1}`, // Ensure YYYY-YYYY format
        semester: data.semester ?? 'Fall',
      };

      console.log('🚀 Submitting class creation payload:', JSON.stringify(payload, null, 2));

      const newClass = await createClassMutation.mutateAsync(payload);
      toast({
        title: 'Success',
        description: 'Class created successfully',
      });
      router.push(`/dashboard/classes/${newClass.id}`);
    } catch (error) {
      console.error('❌ Class creation error:', error);
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to create class',
        variant: 'destructive',
      });
    }
  };

  const handleBack = () => {
    router.push('/dashboard/classes');
  };

  if (!canCreateClass(user?.role)) {
    return (
      <div className='container mx-auto p-4 sm:p-6 lg:p-8'>
        <Card>
          <CardContent className='flex flex-col items-center justify-center py-12'>
            <AlertTriangle className='h-16 w-16 text-destructive mb-6' />
            <h2 className='text-2xl font-bold mb-2'>Access Denied</h2>
            <p className='text-muted-foreground text-center mb-6 max-w-md'>
              Only administrators can create classes.
            </p>
            <Button onClick={handleBack}>
              <ArrowLeft className='h-4 w-4 mr-2' />
              Back to Classes
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className='container mx-auto p-4 sm:p-6 lg:p-8 space-y-6'>
      <div className='flex items-center justify-between'>
        <div className='flex items-center gap-4'>
          <Button variant='ghost' size='sm' onClick={handleBack}>
            <ArrowLeft className='h-4 w-4 mr-2' />
            Back to Classes
          </Button>
          <div>
            <h1 className='text-2xl sm:text-3xl font-bold tracking-tight'>Create Class</h1>
            <p className='text-muted-foreground'>Add a new class to the system</p>
          </div>
        </div>
      </div>

      <ClassForm
        mode='create'
        onSubmit={handleSubmit}
        onCancel={handleBack}
        isLoading={createClassMutation.isPending}
        teachers={teachers}
      />
    </div>
  );
}
