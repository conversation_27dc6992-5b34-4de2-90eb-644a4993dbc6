// Simple test to check FastAPI backend
const API_URL = 'http://127.0.0.1:8000';

console.log('Testing FastAPI backend...');

try {
  // Test root endpoint
  const response = await fetch(`${API_URL}/`);
  console.log('Root endpoint status:', response.status);
  
  if (response.ok) {
    const data = await response.json();
    console.log('Root response:', data);
  }
  
  // Test docs endpoint
  const docsResponse = await fetch(`${API_URL}/docs`);
  console.log('Docs endpoint status:', docsResponse.status);
  
} catch (error) {
  console.error('Connection failed:', error.message);
  console.log('Make sure your FastAPI server is running on http://127.0.0.1:8000');
}
