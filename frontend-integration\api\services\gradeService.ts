/**
 * Grade Service
 * 
 * Handles all grade-related API calls
 */

import { apiUtils } from '../apiClient';
import type { 
  Grade, 
  GradeFilters, 
  GradeStats,
  PaginatedResponse 
} from '../../types';

export interface CreateGradeData {
  student_id: string;
  subject: string;
  exam_id?: string;
  marks_obtained: number;
  total_marks: number;
  remarks?: string;
  teacher_id: string;
  academic_year: string;
  term: 'FIRST' | 'SECOND' | 'THIRD' | 'ANNUAL';
}

export interface UpdateGradeData extends Partial<CreateGradeData> {}

export interface BulkGradeData {
  exam_id: string;
  subject: string;
  teacher_id: string;
  academic_year: string;
  term: 'FIRST' | 'SECOND' | 'THIRD' | 'ANNUAL';
  grades: Array<{
    student_id: string;
    marks_obtained: number;
    remarks?: string;
  }>;
}

export class GradeService {
  private static readonly BASE_URL = '/grades';

  static async getGrades(query: GradeFilters = {}): Promise<PaginatedResponse<Grade>> {
    const params = new URLSearchParams();
    
    Object.entries(query).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        params.append(key, String(value));
      }
    });

    const url = `${this.BASE_URL}${params.toString() ? `?${params.toString()}` : ''}`;
    return apiUtils.get<PaginatedResponse<Grade>>(url);
  }

  static async getGrade(id: string): Promise<Grade> {
    return apiUtils.get<Grade>(`${this.BASE_URL}/${id}`);
  }

  static async createGrade(data: CreateGradeData): Promise<Grade> {
    return apiUtils.post<Grade>(this.BASE_URL, data);
  }

  static async updateGrade(id: string, data: UpdateGradeData): Promise<Grade> {
    return apiUtils.patch<Grade>(`${this.BASE_URL}/${id}`, data);
  }

  static async deleteGrade(id: string): Promise<void> {
    return apiUtils.delete<void>(`${this.BASE_URL}/${id}`);
  }

  static async getGradeStats(): Promise<GradeStats> {
    return apiUtils.get<GradeStats>(`${this.BASE_URL}/stats`);
  }

  static async bulkCreateGrades(data: BulkGradeData): Promise<{
    created: number;
    failed: number;
    errors: Array<{ studentId: string; error: string }>;
  }> {
    return apiUtils.post<{
      created: number;
      failed: number;
      errors: Array<{ studentId: string; error: string }>;
    }>(`${this.BASE_URL}/bulk`, data);
  }

  static async getStudentGrades(studentId: string, academicYear?: string): Promise<Array<{
    id: string;
    subject: string;
    examTitle: string;
    marksObtained: number;
    totalMarks: number;
    percentage: number;
    grade: string;
    term: string;
    date: string;
  }>> {
    const url = academicYear 
      ? `${this.BASE_URL}/student/${studentId}?academicYear=${academicYear}`
      : `${this.BASE_URL}/student/${studentId}`;
    
    return apiUtils.get<Array<{
      id: string;
      subject: string;
      examTitle: string;
      marksObtained: number;
      totalMarks: number;
      percentage: number;
      grade: string;
      term: string;
      date: string;
    }>>(url);
  }

  static async getClassGrades(className: string, subject?: string, term?: string): Promise<Array<{
    studentId: string;
    studentName: string;
    grades: Array<{
      subject: string;
      marksObtained: number;
      totalMarks: number;
      percentage: number;
      grade: string;
    }>;
    overallPercentage: number;
    overallGrade: string;
  }>> {
    const params = new URLSearchParams();
    if (subject) params.append('subject', subject);
    if (term) params.append('term', term);

    const url = `${this.BASE_URL}/class/${className}${params.toString() ? `?${params.toString()}` : ''}`;
    return apiUtils.get<Array<{
      studentId: string;
      studentName: string;
      grades: Array<{
        subject: string;
        marksObtained: number;
        totalMarks: number;
        percentage: number;
        grade: string;
      }>;
      overallPercentage: number;
      overallGrade: string;
    }>>(url);
  }

  static async generateReportCard(studentId: string, academicYear: string, term: string): Promise<{
    reportCardUrl: string;
  }> {
    return apiUtils.post<{ reportCardUrl: string }>(`${this.BASE_URL}/report-card`, {
      studentId,
      academicYear,
      term,
    });
  }

  static async getSubjectAnalysis(subject: string, className?: string): Promise<{
    subject: string;
    class?: string;
    totalStudents: number;
    averageMarks: number;
    averagePercentage: number;
    passRate: number;
    gradeDistribution: Record<string, number>;
    topPerformers: Array<{
      studentId: string;
      studentName: string;
      marksObtained: number;
      percentage: number;
    }>;
  }> {
    const params = new URLSearchParams();
    if (className) params.append('class', className);

    const url = `${this.BASE_URL}/analysis/subject/${subject}${params.toString() ? `?${params.toString()}` : ''}`;
    return apiUtils.get<{
      subject: string;
      class?: string;
      totalStudents: number;
      averageMarks: number;
      averagePercentage: number;
      passRate: number;
      gradeDistribution: Record<string, number>;
      topPerformers: Array<{
        studentId: string;
        studentName: string;
        marksObtained: number;
        percentage: number;
      }>;
    }>(url);
  }
}

export const gradeService = GradeService;
