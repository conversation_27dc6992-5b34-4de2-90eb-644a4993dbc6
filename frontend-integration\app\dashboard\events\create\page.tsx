'use client';

/**
 * Create Event Page - Professional Event Creation Form
 *
 * Features:
 * - Comprehensive event creation form
 * - Date and time selection
 * - Location and capacity management
 * - Event type and status selection
 * - Responsive design
 * - Loading states and validation
 */

import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { ArrowLeft, CalendarDays, Save } from 'lucide-react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { useState } from 'react';

const eventTypes = [
  { id: 'academic', name: 'Academic' },
  { id: 'sports', name: 'Sports' },
  { id: 'cultural', name: 'Cultural' },
  { id: 'meeting', name: 'Meeting' },
  { id: 'competition', name: 'Competition' },
  { id: 'administrative', name: 'Administrative' },
];

const eventStatuses = [
  { id: 'upcoming', name: 'Upcoming' },
  { id: 'ongoing', name: 'Ongoing' },
  { id: 'completed', name: 'Completed' },
  { id: 'cancelled', name: 'Cancelled' },
];

const organizers = [
  { id: '1', name: 'Administration' },
  { id: '2', name: 'Science Department' },
  { id: '3', name: 'Mathematics Department' },
  { id: '4', name: 'Physical Education' },
  { id: '5', name: 'Art Department' },
  { id: '6', name: 'School Board' },
];

interface EventFormData {
  title: string;
  description: string;
  date: string;
  startTime: string;
  endTime: string;
  location: string;
  type: string;
  status: string;
  capacity: string;
  organizer: string;
  requirements: string;
  notes: string;
}

export default function CreateEventPage() {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  const [formData, setFormData] = useState<EventFormData>({
    title: '',
    description: '',
    date: '',
    startTime: '',
    endTime: '',
    location: '',
    type: '',
    status: 'upcoming',
    capacity: '',
    organizer: '',
    requirements: '',
    notes: '',
  });

  const handleInputChange = (field: keyof EventFormData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    try {
      // TODO: Replace with actual API call
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      console.log('Creating event:', formData);
      
      // Redirect to events list on success
      router.push('/dashboard/events');
    } catch (error) {
      console.error('Error creating event:', error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className='container mx-auto p-4 sm:p-6 lg:p-8 max-w-4xl'>
      {/* Header */}
      <div className='flex items-center gap-4 mb-8'>
        <Link href='/dashboard/events'>
          <Button variant='outline' size='sm'>
            <ArrowLeft className='w-4 h-4 mr-2' />
            Back to Events
          </Button>
        </Link>
        <div>
          <h1 className='text-3xl font-bold text-gray-900 flex items-center gap-2'>
            <CalendarDays className='w-8 h-8 text-blue-600' />
            Create New Event
          </h1>
          <p className='text-gray-600 mt-1'>Add a new event to the school calendar</p>
        </div>
      </div>

      {/* Form */}
      <form onSubmit={handleSubmit} className='space-y-8'>
        {/* Basic Information */}
        <Card>
          <CardHeader>
            <CardTitle>Event Information</CardTitle>
          </CardHeader>
          <CardContent className='space-y-6'>
            <div className='space-y-2'>
              <Label htmlFor='title'>Event Title *</Label>
              <Input
                id='title'
                placeholder='e.g., Annual Science Fair'
                value={formData.title}
                onChange={e => handleInputChange('title', e.target.value)}
                required
              />
            </div>

            <div className='space-y-2'>
              <Label htmlFor='description'>Description *</Label>
              <Textarea
                id='description'
                placeholder='Brief description of the event...'
                value={formData.description}
                onChange={e => handleInputChange('description', e.target.value)}
                rows={4}
                required
              />
            </div>

            <div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
              <div className='space-y-2'>
                <Label htmlFor='type'>Event Type *</Label>
                <Select value={formData.type} onValueChange={value => handleInputChange('type', value)}>
                  <SelectTrigger>
                    <SelectValue placeholder='Select event type' />
                  </SelectTrigger>
                  <SelectContent>
                    {eventTypes.map(type => (
                      <SelectItem key={type.id} value={type.id}>
                        {type.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div className='space-y-2'>
                <Label htmlFor='status'>Status</Label>
                <Select value={formData.status} onValueChange={value => handleInputChange('status', value)}>
                  <SelectTrigger>
                    <SelectValue placeholder='Select status' />
                  </SelectTrigger>
                  <SelectContent>
                    {eventStatuses.map(status => (
                      <SelectItem key={status.id} value={status.id}>
                        {status.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Date, Time & Location */}
        <Card>
          <CardHeader>
            <CardTitle>Schedule & Location</CardTitle>
          </CardHeader>
          <CardContent className='space-y-6'>
            <div className='grid grid-cols-1 md:grid-cols-3 gap-6'>
              <div className='space-y-2'>
                <Label htmlFor='date'>Event Date *</Label>
                <Input
                  id='date'
                  type='date'
                  value={formData.date}
                  onChange={e => handleInputChange('date', e.target.value)}
                  required
                />
              </div>
              <div className='space-y-2'>
                <Label htmlFor='startTime'>Start Time *</Label>
                <Input
                  id='startTime'
                  type='time'
                  value={formData.startTime}
                  onChange={e => handleInputChange('startTime', e.target.value)}
                  required
                />
              </div>
              <div className='space-y-2'>
                <Label htmlFor='endTime'>End Time *</Label>
                <Input
                  id='endTime'
                  type='time'
                  value={formData.endTime}
                  onChange={e => handleInputChange('endTime', e.target.value)}
                  required
                />
              </div>
            </div>

            <div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
              <div className='space-y-2'>
                <Label htmlFor='location'>Location *</Label>
                <Input
                  id='location'
                  placeholder='e.g., Main Auditorium'
                  value={formData.location}
                  onChange={e => handleInputChange('location', e.target.value)}
                  required
                />
              </div>
              <div className='space-y-2'>
                <Label htmlFor='capacity'>Expected Capacity</Label>
                <Input
                  id='capacity'
                  type='number'
                  placeholder='e.g., 150'
                  value={formData.capacity}
                  onChange={e => handleInputChange('capacity', e.target.value)}
                />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Organization & Additional Details */}
        <Card>
          <CardHeader>
            <CardTitle>Organization & Details</CardTitle>
          </CardHeader>
          <CardContent className='space-y-6'>
            <div className='space-y-2'>
              <Label htmlFor='organizer'>Organizer *</Label>
              <Select value={formData.organizer} onValueChange={value => handleInputChange('organizer', value)}>
                <SelectTrigger>
                  <SelectValue placeholder='Select organizer' />
                </SelectTrigger>
                <SelectContent>
                  {organizers.map(organizer => (
                    <SelectItem key={organizer.id} value={organizer.id}>
                      {organizer.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className='space-y-2'>
              <Label htmlFor='requirements'>Requirements</Label>
              <Textarea
                id='requirements'
                placeholder='Any special requirements or preparations needed...'
                value={formData.requirements}
                onChange={e => handleInputChange('requirements', e.target.value)}
                rows={3}
              />
            </div>

            <div className='space-y-2'>
              <Label htmlFor='notes'>Additional Notes</Label>
              <Textarea
                id='notes'
                placeholder='Any additional information or notes...'
                value={formData.notes}
                onChange={e => handleInputChange('notes', e.target.value)}
                rows={3}
              />
            </div>
          </CardContent>
        </Card>

        {/* Actions */}
        <div className='flex justify-end gap-4'>
          <Link href='/dashboard/events'>
            <Button type='button' variant='outline'>
              Cancel
            </Button>
          </Link>
          <Button type='submit' disabled={isLoading}>
            {isLoading ? (
              <>
                <div className='w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2' />
                Creating...
              </>
            ) : (
              <>
                <Save className='w-4 h-4 mr-2' />
                Create Event
              </>
            )}
          </Button>
        </div>
      </form>
    </div>
  );
}
