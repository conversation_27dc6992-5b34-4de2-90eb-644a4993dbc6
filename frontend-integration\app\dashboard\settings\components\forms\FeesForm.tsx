'use client';

import React, { useState } from 'react';
import { CreditCard, Tag, FileSpreadsheet, Calculator, DollarSign } from 'lucide-react';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { FeeTypesManagement } from '../fees/FeeTypesManagement';
import { ClassFeeSchedule } from '../fees/ClassFeeSchedule';
import { LateFeeDiscountRules } from '../fees/LateFeeDiscountRules';
import { OpeningBalanceManagement } from '../fees/OpeningBalanceManagement';

interface FeesFormProps {
  onDataChange?: () => void;
}

/**
 * FeesForm Component
 * 
 * Main Fees & Billing form with 4 tabs:
 * 1. Fee Types - Base fee heads management
 * 2. Class-wise Fee Schedule - Main editable grid (matches screenshot)
 * 3. Late Fee & Discount Rules - Global rules and per-class overrides
 * 4. Opening Balance - Class/student balance management
 */
export function FeesForm({ onDataChange }: FeesFormProps) {
  const [activeTab, setActiveTab] = useState('class-schedule');

  const tabs = [
    {
      id: 'fee-types',
      label: 'Fee Types',
      icon: Tag,
      description: 'Manage base fee heads and categories',
      component: FeeTypesManagement,
    },
    {
      id: 'class-schedule',
      label: 'Class-wise Fee Schedule',
      icon: FileSpreadsheet,
      description: 'Configure fee structure for all classes',
      component: ClassFeeSchedule,
      featured: true, // This is the main tab matching the screenshot
    },
    {
      id: 'rules',
      label: 'Late Fee & Discount Rules',
      icon: Calculator,
      description: 'Set up late fee and discount policies',
      component: LateFeeDiscountRules,
    },
    {
      id: 'opening-balance',
      label: 'Opening Balance',
      icon: DollarSign,
      description: 'Manage opening balances for classes and students',
      component: OpeningBalanceManagement,
    },
  ];

  return (
    <div className="p-6">
      {/* Header */}
      <div className="mb-6">
        <div className="flex items-center gap-3 mb-2">
          <div className="p-2 bg-gradient-to-r from-sky-50 to-violet-50 rounded-lg">
            <CreditCard className="h-6 w-6 text-sky-600" />
          </div>
          <div>
            <h2 className="text-2xl font-bold text-slate-900">Fees & Billing</h2>
            <p className="text-slate-600">Configure fee structure, rules, and billing settings</p>
          </div>
        </div>
      </div>

      {/* Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        {/* Tab Navigation */}
        <div className="border-b border-slate-200">
          <TabsList className="w-full h-auto p-1 bg-slate-50 justify-start overflow-x-auto">
            <div className="flex gap-1 min-w-max">
              {tabs.map((tab) => {
                const Icon = tab.icon;
                const isActive = activeTab === tab.id;
                
                return (
                  <TabsTrigger
                    key={tab.id}
                    value={tab.id}
                    className={`
                      flex items-center gap-2 px-4 py-3 rounded-lg text-sm font-medium
                      transition-all duration-200 whitespace-nowrap relative
                      ${isActive 
                        ? 'bg-white text-sky-800 shadow-sm border border-sky-100' 
                        : 'text-slate-600 hover:text-slate-900 hover:bg-white/50'
                      }
                    `}
                  >
                    <Icon className={`h-4 w-4 ${isActive ? 'text-sky-600' : 'text-slate-500'}`} />
                    <span className="hidden sm:inline">{tab.label}</span>
                    <span className="sm:hidden">{tab.label.split(' ')[0]}</span>
                    {tab.featured && (
                      <Badge variant="secondary" className="ml-1 text-xs bg-sky-100 text-sky-700">
                        Primary
                      </Badge>
                    )}
                  </TabsTrigger>
                );
              })}
            </div>
          </TabsList>
        </div>

        {/* Tab Content */}
        <div className="min-h-[600px]">
          {tabs.map((tab) => {
            const Component = tab.component;
            
            return (
              <TabsContent key={tab.id} value={tab.id} className="mt-0 focus-visible:outline-none">
                {/* Tab Header */}
                <div className="mb-6">
                  <div className="flex items-center gap-3">
                    <div className="p-2 bg-gradient-to-r from-sky-50 to-violet-50 rounded-lg">
                      <tab.icon className="h-5 w-5 text-sky-600" />
                    </div>
                    <div>
                      <h3 className="text-xl font-semibold text-slate-900 flex items-center gap-2">
                        {tab.label}
                        {tab.featured && (
                          <Badge variant="outline" className="bg-sky-50 text-sky-700 border-sky-200">
                            Featured
                          </Badge>
                        )}
                      </h3>
                      <p className="text-sm text-slate-600 mt-1">{tab.description}</p>
                    </div>
                  </div>
                </div>

                {/* Tab Component */}
                <Card className="border-0 shadow-sm">
                  <CardContent className="p-0">
                    <Component onDataChange={onDataChange} />
                  </CardContent>
                </Card>
              </TabsContent>
            );
          })}
        </div>
      </Tabs>

      {/* Footer Info */}
      <div className="mt-8 p-4 bg-slate-50 rounded-lg border border-slate-200">
        <div className="flex items-start gap-3">
          <div className="p-1 bg-blue-100 rounded">
            <CreditCard className="h-4 w-4 text-blue-600" />
          </div>
          <div className="text-sm">
            <p className="font-medium text-slate-900 mb-1">Fees & Billing Configuration</p>
            <div className="text-slate-600 space-y-1">
              <p>• <strong>Fee Types:</strong> Define base fee categories and manage their hierarchy</p>
              <p>• <strong>Class Schedule:</strong> Set specific fee amounts for each class with inline editing</p>
              <p>• <strong>Rules:</strong> Configure late fee calculations and discount policies</p>
              <p>• <strong>Opening Balance:</strong> Set initial balances for accurate billing</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
