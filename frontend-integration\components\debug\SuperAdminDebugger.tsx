/**
 * SUPER_ADMIN Debug Component
 * Helps identify why Create Class button is not appearing
 */

'use client';

import React from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { useAuth } from '@/hooks/useAuth';
import { canCreateClass } from '@/lib/permissions';

export const SuperAdminDebugger: React.FC = () => {
  const { user } = useAuth();

  const debugInfo = {
    userExists: !!user,
    userId: user?.id || 'undefined',
    userEmail: user?.email || 'undefined',
    userRole: user?.role || 'undefined',
    userRoleType: typeof user?.role,
    canCreateResult: canCreateClass(user?.role),
    exactRoleMatch: user?.role === 'SUPER_ADMIN',
    roleComparison: {
      userRole: user?.role,
      expectedRole: 'SUPER_ADMIN',
      strictEqual: user?.role === 'SUPER_ADMIN',
      looseEqual: user?.role == 'SUPER_ADMIN',
    }
  };

  return (
    <Card className="mb-4 border-yellow-200 bg-yellow-50">
      <CardHeader>
        <CardTitle className="text-yellow-800 flex items-center gap-2">
          🔍 SUPER_ADMIN Debug Information
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <h4 className="font-semibold text-sm mb-2">User Authentication</h4>
            <div className="space-y-1 text-xs">
              <div>User Exists: <Badge variant={debugInfo.userExists ? "default" : "destructive"}>{debugInfo.userExists.toString()}</Badge></div>
              <div>User ID: <code className="bg-gray-100 px-1 rounded">{debugInfo.userId}</code></div>
              <div>User Email: <code className="bg-gray-100 px-1 rounded">{debugInfo.userEmail}</code></div>
            </div>
          </div>
          
          <div>
            <h4 className="font-semibold text-sm mb-2">Role Information</h4>
            <div className="space-y-1 text-xs">
              <div>User Role: <code className="bg-gray-100 px-1 rounded">{debugInfo.userRole}</code></div>
              <div>Role Type: <code className="bg-gray-100 px-1 rounded">{debugInfo.userRoleType}</code></div>
              <div>Expected: <code className="bg-gray-100 px-1 rounded">SUPER_ADMIN</code></div>
            </div>
          </div>
        </div>

        <div className="border-t pt-4">
          <h4 className="font-semibold text-sm mb-2">Permission Check Results</h4>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-xs">
            <div>
              <div>canCreateClass(): <Badge variant={debugInfo.canCreateResult ? "default" : "destructive"}>{debugInfo.canCreateResult.toString()}</Badge></div>
            </div>
            <div>
              <div>Exact Match: <Badge variant={debugInfo.exactRoleMatch ? "default" : "destructive"}>{debugInfo.exactRoleMatch.toString()}</Badge></div>
            </div>
            <div>
              <div>Strict Equal: <Badge variant={debugInfo.roleComparison.strictEqual ? "default" : "destructive"}>{debugInfo.roleComparison.strictEqual.toString()}</Badge></div>
            </div>
          </div>
        </div>

        <div className="border-t pt-4">
          <h4 className="font-semibold text-sm mb-2">Raw Data</h4>
          <pre className="bg-gray-100 p-2 rounded text-xs overflow-auto">
            {JSON.stringify(debugInfo, null, 2)}
          </pre>
        </div>

        <div className="border-t pt-4">
          <h4 className="font-semibold text-sm mb-2">Full User Object</h4>
          <pre className="bg-gray-100 p-2 rounded text-xs overflow-auto">
            {JSON.stringify(user, null, 2)}
          </pre>
        </div>

        <div className="border-t pt-4 bg-blue-50 p-3 rounded">
          <h4 className="font-semibold text-sm mb-2 text-blue-800">Troubleshooting Steps</h4>
          <ol className="text-xs text-blue-700 space-y-1 list-decimal list-inside">
            <li>Check if user object is fully loaded (not null/undefined)</li>
            <li>Verify user.role is exactly "SUPER_ADMIN" (case-sensitive)</li>
            <li>Ensure canCreateClass() returns true</li>
            <li>Check if ModulePageLayout receives createRoute prop correctly</li>
            <li>Verify JWT token contains correct role claim</li>
          </ol>
        </div>

        {!debugInfo.canCreateResult && (
          <div className="border-t pt-4 bg-red-50 p-3 rounded">
            <h4 className="font-semibold text-sm mb-2 text-red-800">❌ Issue Detected</h4>
            <div className="text-xs text-red-700 space-y-1">
              {!debugInfo.userExists && <div>• User object is null/undefined - check authentication</div>}
              {debugInfo.userExists && debugInfo.userRole !== 'SUPER_ADMIN' && (
                <div>• User role is "{debugInfo.userRole}" but expected "SUPER_ADMIN"</div>
              )}
              {debugInfo.userExists && debugInfo.userRole === 'SUPER_ADMIN' && !debugInfo.canCreateResult && (
                <div>• Role matches but canCreateClass() still returns false - check permission function</div>
              )}
            </div>
          </div>
        )}

        {debugInfo.canCreateResult && (
          <div className="border-t pt-4 bg-green-50 p-3 rounded">
            <h4 className="font-semibold text-sm mb-2 text-green-800">✅ Permissions OK</h4>
            <div className="text-xs text-green-700">
              User has SUPER_ADMIN role and canCreateClass() returns true. 
              If Create button still not visible, check ModulePageLayout component.
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default SuperAdminDebugger;
