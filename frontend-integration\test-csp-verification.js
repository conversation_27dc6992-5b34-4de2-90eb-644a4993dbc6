/**
 * CSP Verification Test Script
 * 
 * This script tests the CSP fixes and API connectivity
 * to ensure the Students module works correctly.
 * 
 * Note: This script runs in Node.js environment and tests the backend directly.
 * For browser CSP testing, use test-browser-csp.js instead.
 */

import axios from 'axios';

async function testAPIConnections() {
  console.log('🔍 Testing API Connections...\n');

  // Test 1: Next.js API route (should work)
  try {
    console.log('1. Testing Next.js API route (/api/ping)...');
    const pingResponse = await axios.get('http://localhost:3000/api/ping');
    console.log('✅ Next.js API route working:', pingResponse.data);
  } catch (error) {
    console.log('❌ Next.js API route failed:', error.message);
  }

  console.log('\n' + '='.repeat(50) + '\n');

  // Test 2: Backend proxy through Next.js rewrites
  try {
    console.log('2. Testing backend proxy (/api/students/ -> http://127.0.0.1:8000/api/v1/students/)...');
    const studentsResponse = await axios.get('http://localhost:3000/api/students/');
    console.log('✅ Backend proxy working:', studentsResponse.data);
  } catch (error) {
    console.log('❌ Backend proxy failed:', error.message);
    if (error.response) {
      console.log('   Status:', error.response.status);
      console.log('   Data:', error.response.data);
    }
  }

  console.log('\n' + '='.repeat(50) + '\n');

  // Test 3: Direct backend connection (should fail due to CORS)
  try {
    console.log('3. Testing direct backend connection (should fail due to CORS)...');
    const directResponse = await axios.get('http://127.0.0.1:8000/api/v1/students/');
    console.log('⚠️  Direct backend connection worked (CORS might be disabled):', directResponse.data);
  } catch (error) {
    console.log('✅ Direct backend connection failed as expected (CORS working):', error.message);
  }

  console.log('\n' + '='.repeat(50) + '\n');

  // Test 4: Check if backend is running
  try {
    console.log('4. Checking if backend is accessible...');
    const healthResponse = await axios.get('http://127.0.0.1:8000/health', { timeout: 5000 });
    console.log('✅ Backend is running and accessible');
  } catch (error) {
    console.log('❌ Backend is not accessible:', error.message);
    console.log('   Make sure your FastAPI backend is running on http://127.0.0.1:8000');
  }

  console.log('\n' + '='.repeat(50) + '\n');
  console.log('🎯 Summary:');
  console.log('- If Test 1 fails: Next.js API routes are broken');
  console.log('- If Test 2 fails: Next.js rewrites are not working');
  console.log('- If Test 3 works: Backend CORS is disabled (security risk)');
  console.log('- If Test 4 fails: Backend is not running');
}

// Run the tests
testAPIConnections().catch(console.error);




