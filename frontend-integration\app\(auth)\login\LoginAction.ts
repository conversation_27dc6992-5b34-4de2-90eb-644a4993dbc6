"use server";
import { cookies } from "next/headers";

export async function saveAuthOnServer({ token, role }: { token: string; role: string }) {
  const cookieStore = cookies();
  
  // Set auth cookies for middleware
  cookieStore.set(process.env.NEXT_PUBLIC_AUTH_COOKIE || 'access_token', token, { 
    path: "/", 
    httpOnly: false, 
    sameSite: "lax",
    maxAge: 60 * 60 * 24 * 7 // 7 days
  });
  
  cookieStore.set(process.env.NEXT_PUBLIC_ROLE_KEY || 'role', role, { 
    path: "/", 
    httpOnly: false, 
    sameSite: "lax",
    maxAge: 60 * 60 * 24 * 7 // 7 days
  });
  
  console.log('🍪 Server cookies set:', { token: !!token, role });
}
