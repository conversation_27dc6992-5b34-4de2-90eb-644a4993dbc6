# API Configuration - Direct FastAPI Backend Communication
NEXT_PUBLIC_API_URL=http://127.0.0.1:8000
NEXT_PUBLIC_APP_URL=http://localhost:3000

# Backend URLs (for CORS compatibility)
# Use 127.0.0.1:8000 for direct backend communication
# This bypasses Next.js proxy and communicates directly with FastAPI
# Make sure your FastAPI CORS allows http://localhost:3000

# Development Settings
NODE_ENV=development
NEXT_PUBLIC_DEBUG=true

# Auth Configuration
NEXT_PUBLIC_AUTH_NAMESPACE=users
NEXT_PUBLIC_AUTH_COOKIE=access_token
NEXT_PUBLIC_ROLE_KEY=role
