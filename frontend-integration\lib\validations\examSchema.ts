/**
 * Exam Schema - Zod Validation
 * 
 * Features:
 * - Exam creation and management
 * - Grade validation
 * - Schedule validation
 * - Result processing
 */

import { useForm, type UseFormReturn } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';

// Exam type enum
export const examTypeSchema = z.enum(['MIDTERM', 'FINAL', 'QUIZ', 'ASSIGNMENT', 'PROJECT'], {
  required_error: 'Exam type is required',
});

// Exam status enum
export const examStatusSchema = z.enum(['SCHEDULED', 'IN_PROGRESS', 'COMPLETED', 'CANCELLED'], {
  required_error: 'Exam status is required',
});

// Grade schema
export const gradeSchema = z.object({
  student_id: z.string().min(1, 'Student ID is required'),
  exam_id: z.string().min(1, 'Exam ID is required'),
  score: z.number().min(0, 'Score cannot be negative').max(100, 'Score cannot exceed 100'),
  grade_letter: z.enum(['A+', 'A', 'A-', 'B+', 'B', 'B-', 'C+', 'C', 'C-', 'D+', 'D', 'F']).optional(),
  comments: z.string().max(500, 'Comments must be less than 500 characters').optional(),
  graded_by: z.string().min(1, 'Graded by is required'),
  graded_at: z.string().optional(),
});

// Exam creation schema
export const createExamSchema = z.object({
  title: z.string().min(1, 'Exam title is required').max(100, 'Title must be less than 100 characters'),
  description: z.string().max(500, 'Description must be less than 500 characters').optional(),
  subject: z.string().min(1, 'Subject is required'),
  class_id: z.string().min(1, 'Class is required'),
  teacher_id: z.string().min(1, 'Teacher is required'),
  exam_type: examTypeSchema,
  total_marks: z.number().min(1, 'Total marks must be at least 1').max(1000, 'Total marks cannot exceed 1000'),
  passing_marks: z.number().min(0, 'Passing marks cannot be negative'),
  duration_minutes: z.number().min(1, 'Duration must be at least 1 minute').max(480, 'Duration cannot exceed 8 hours'),
  exam_date: z.string().min(1, 'Exam date is required'),
  start_time: z.string().min(1, 'Start time is required'),
  end_time: z.string().min(1, 'End time is required'),
  room: z.string().max(50, 'Room must be less than 50 characters').optional(),
  instructions: z.string().max(1000, 'Instructions must be less than 1000 characters').optional(),
  status: examStatusSchema.default('SCHEDULED'),
}).refine((data) => {
  return data.passing_marks <= data.total_marks;
}, {
  message: 'Passing marks cannot exceed total marks',
  path: ['passing_marks'],
}).refine((data) => {
  const examDate = new Date(`${data.exam_date}T${data.start_time}`);
  const endDate = new Date(`${data.exam_date}T${data.end_time}`);
  return endDate > examDate;
}, {
  message: 'End time must be after start time',
  path: ['end_time'],
});

// Exam update schema
export const updateExamSchema = createExamSchema.extend({
  id: z.string().min(1, 'Exam ID is required'),
}).partial().omit({ id: true }).extend({
  id: z.string().min(1, 'Exam ID is required'),
});

// Bulk grade entry schema
export const bulkGradeSchema = z.object({
  exam_id: z.string().min(1, 'Exam ID is required'),
  grades: z.array(gradeSchema).min(1, 'At least one grade is required'),
});

// Type exports
export type CreateExamInput = z.infer<typeof createExamSchema>;
export type UpdateExamInput = z.infer<typeof updateExamSchema>;
export type GradeInput = z.infer<typeof gradeSchema>;
export type BulkGradeInput = z.infer<typeof bulkGradeSchema>;
export type ExamType = z.infer<typeof examTypeSchema>;
export type ExamStatus = z.infer<typeof examStatusSchema>;

// Custom hooks
export function useExamForm(
  defaultValues?: Partial<CreateExamInput>,
  mode: 'create' | 'update' = 'create'
): UseFormReturn<CreateExamInput> | UseFormReturn<UpdateExamInput> {
  const schema = mode === 'create' ? createExamSchema : updateExamSchema;
  
  return useForm({
    resolver: zodResolver(schema),
    defaultValues: {
      status: 'SCHEDULED' as const,
      exam_type: 'QUIZ' as const,
      total_marks: 100,
      passing_marks: 40,
      duration_minutes: 60,
      ...defaultValues,
    },
    mode: 'onChange',
  });
}

// Utility functions
export function calculateGradeLetter(score: number, totalMarks: number): string {
  const percentage = (score / totalMarks) * 100;
  
  if (percentage >= 97) return 'A+';
  if (percentage >= 93) return 'A';
  if (percentage >= 90) return 'A-';
  if (percentage >= 87) return 'B+';
  if (percentage >= 83) return 'B';
  if (percentage >= 80) return 'B-';
  if (percentage >= 77) return 'C+';
  if (percentage >= 73) return 'C';
  if (percentage >= 70) return 'C-';
  if (percentage >= 67) return 'D+';
  if (percentage >= 60) return 'D';
  return 'F';
}

export function getExamStatusColor(status: ExamStatus): string {
  switch (status) {
    case 'SCHEDULED':
      return 'text-blue-600 bg-blue-50';
    case 'IN_PROGRESS':
      return 'text-yellow-600 bg-yellow-50';
    case 'COMPLETED':
      return 'text-green-600 bg-green-50';
    case 'CANCELLED':
      return 'text-red-600 bg-red-50';
    default:
      return 'text-gray-600 bg-gray-50';
  }
}