'use client';

import React, { useState } from 'react';
import { GraduationCap, Calendar, Users, BookOpen, School, Award } from 'lucide-react';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { AcademicPanel } from '../academic/AcademicPanel';
import type { Session, Section, Class, Subject, Designation } from '../../schemas/academic.schemas';

// Mock data
const mockSessions: Session[] = [
  { id: '1', name: '2024-2025', startDate: '2024-04-01', endDate: '2025-03-31', isActive: true, sortOrder: 0 },
  { id: '2', name: '2023-2024', startDate: '2023-04-01', endDate: '2024-03-31', isActive: false, sortOrder: 1 },
];

const mockSections: Section[] = [
  { id: '1', name: 'Section A', code: 'A', capacity: 30, isActive: true, sortOrder: 0 },
  { id: '2', name: 'Section B', code: 'B', capacity: 30, isActive: true, sortOrder: 1 },
];

const mockClasses: Class[] = [
  { id: '1', name: 'Nursery', level: 1, ageRange: { min: 3, max: 4 }, isActive: true, sortOrder: 0 },
  { id: '2', name: 'LKG', level: 2, ageRange: { min: 4, max: 5 }, isActive: true, sortOrder: 1 },
];

const mockSubjects: Subject[] = [
  { id: '1', name: 'Mathematics', code: 'MATH', category: 'CORE', credits: 4, isActive: true, sortOrder: 0 },
  { id: '2', name: 'English', code: 'ENG', category: 'CORE', credits: 4, isActive: true, sortOrder: 1 },
];

const mockDesignations: Designation[] = [
  { id: '1', title: 'Principal', department: 'Administration', level: 'PRINCIPAL', permissions: ['ALL'], isActive: true, sortOrder: 0 },
  { id: '2', title: 'Head Teacher', department: 'Academic', level: 'HEAD', permissions: ['MANAGE_TEACHERS'], isActive: true, sortOrder: 1 },
];

interface AcademicStructureFormProps {
  onDataChange?: () => void;
}

/**
 * AcademicStructureForm Component
 * 
 * 5 panels: Sessions, Sections, Classes, Subjects, Designations
 * Each with CRUD operations, reordering, and guarded delete
 */
export function AcademicStructureForm({ onDataChange }: AcademicStructureFormProps) {
  const [activeTab, setActiveTab] = useState('sessions');

  const panels = [
    {
      id: 'sessions',
      label: 'Sessions',
      icon: Calendar,
      description: 'Academic sessions (one active)',
      data: mockSessions,
      columns: [
        { key: 'name', label: 'Session Name', render: (item: Session) => (
          <div className="flex items-center gap-2">
            <span className="font-medium">{item.name}</span>
            {item.isActive && <Badge className="bg-green-100 text-green-800">Active</Badge>}
          </div>
        )},
        { key: 'period', label: 'Period', render: (item: Session) => (
          <span className="text-sm text-slate-600">
            {new Date(item.startDate).toLocaleDateString()} - {new Date(item.endDate).toLocaleDateString()}
          </span>
        )},
      ],
      formFields: [
        { name: 'name', label: 'Session Name', type: 'text', required: true },
        { name: 'startDate', label: 'Start Date', type: 'date', required: true },
        { name: 'endDate', label: 'End Date', type: 'date', required: true },
        { name: 'isActive', label: 'Active Session', type: 'checkbox' },
        { name: 'description', label: 'Description', type: 'textarea' },
      ],
    },
    {
      id: 'sections',
      label: 'Sections',
      icon: Users,
      description: 'Class sections and capacity',
      data: mockSections,
      columns: [
        { key: 'name', label: 'Section', render: (item: Section) => (
          <div className="flex items-center gap-2">
            <span className="font-medium">{item.name}</span>
            <Badge variant="outline">{item.code}</Badge>
          </div>
        )},
        { key: 'capacity', label: 'Capacity', render: (item: Section) => `${item.capacity} students` },
      ],
      formFields: [
        { name: 'name', label: 'Section Name', type: 'text', required: true },
        { name: 'code', label: 'Section Code', type: 'text', required: true },
        { name: 'capacity', label: 'Capacity', type: 'number', required: true },
        { name: 'isActive', label: 'Active', type: 'checkbox' },
      ],
    },
    {
      id: 'classes',
      label: 'Classes',
      icon: School,
      description: 'Grade levels and age ranges',
      data: mockClasses,
      columns: [
        { key: 'name', label: 'Class', render: (item: Class) => (
          <div className="flex items-center gap-2">
            <span className="font-medium">{item.name}</span>
            <Badge variant="secondary">Level {item.level}</Badge>
          </div>
        )},
        { key: 'ageRange', label: 'Age Range', render: (item: Class) => `${item.ageRange.min}-${item.ageRange.max} years` },
      ],
      formFields: [
        { name: 'name', label: 'Class Name', type: 'text', required: true },
        { name: 'level', label: 'Level', type: 'number', required: true },
        { name: 'ageRange.min', label: 'Min Age', type: 'number', required: true },
        { name: 'ageRange.max', label: 'Max Age', type: 'number', required: true },
        { name: 'isActive', label: 'Active', type: 'checkbox' },
      ],
    },
    {
      id: 'subjects',
      label: 'Subjects',
      icon: BookOpen,
      description: 'Academic subjects and categories',
      data: mockSubjects,
      columns: [
        { key: 'name', label: 'Subject', render: (item: Subject) => (
          <div className="flex items-center gap-2">
            <span className="font-medium">{item.name}</span>
            <Badge variant="outline">{item.code}</Badge>
          </div>
        )},
        { key: 'category', label: 'Category', render: (item: Subject) => (
          <Badge className={`${
            item.category === 'CORE' ? 'bg-blue-100 text-blue-800' :
            item.category === 'ELECTIVE' ? 'bg-green-100 text-green-800' :
            item.category === 'LANGUAGE' ? 'bg-purple-100 text-purple-800' :
            'bg-orange-100 text-orange-800'
          }`}>
            {item.category.replace('_', ' ')}
          </Badge>
        )},
        { key: 'credits', label: 'Credits', render: (item: Subject) => `${item.credits} credits` },
      ],
      formFields: [
        { name: 'name', label: 'Subject Name', type: 'text', required: true },
        { name: 'code', label: 'Subject Code', type: 'text', required: true },
        { name: 'category', label: 'Category', type: 'select', required: true, options: [
          { value: 'CORE', label: 'Core' },
          { value: 'ELECTIVE', label: 'Elective' },
          { value: 'EXTRA_CURRICULAR', label: 'Extra Curricular' },
          { value: 'LANGUAGE', label: 'Language' },
        ]},
        { name: 'credits', label: 'Credits', type: 'number', required: true },
        { name: 'isActive', label: 'Active', type: 'checkbox' },
      ],
    },
    {
      id: 'designations',
      label: 'Designations',
      icon: Award,
      description: 'Staff roles and permissions',
      data: mockDesignations,
      columns: [
        { key: 'title', label: 'Designation', render: (item: Designation) => (
          <div>
            <span className="font-medium">{item.title}</span>
            <p className="text-sm text-slate-600">{item.department}</p>
          </div>
        )},
        { key: 'level', label: 'Level', render: (item: Designation) => (
          <Badge className={`${
            item.level === 'PRINCIPAL' ? 'bg-red-100 text-red-800' :
            item.level === 'HEAD' ? 'bg-orange-100 text-orange-800' :
            item.level === 'SENIOR' ? 'bg-blue-100 text-blue-800' :
            'bg-gray-100 text-gray-800'
          }`}>
            {item.level}
          </Badge>
        )},
      ],
      formFields: [
        { name: 'title', label: 'Designation Title', type: 'text', required: true },
        { name: 'department', label: 'Department', type: 'text', required: true },
        { name: 'level', label: 'Level', type: 'select', required: true, options: [
          { value: 'JUNIOR', label: 'Junior' },
          { value: 'SENIOR', label: 'Senior' },
          { value: 'HEAD', label: 'Head' },
          { value: 'PRINCIPAL', label: 'Principal' },
        ]},
        { name: 'isActive', label: 'Active', type: 'checkbox' },
      ],
    },
  ];

  return (
    <div className="p-6">
      {/* Header */}
      <div className="mb-6">
        <div className="flex items-center gap-3 mb-2">
          <div className="p-2 bg-gradient-to-r from-sky-50 to-violet-50 rounded-lg">
            <GraduationCap className="h-6 w-6 text-sky-600" />
          </div>
          <div>
            <h2 className="text-2xl font-bold text-slate-900">Academic Structure</h2>
            <p className="text-slate-600">Manage sessions, classes, subjects, and organizational structure</p>
          </div>
        </div>
      </div>

      {/* Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        {/* Tab Navigation */}
        <div className="border-b border-slate-200">
          <TabsList className="w-full h-auto p-1 bg-slate-50 justify-start overflow-x-auto">
            <div className="flex gap-1 min-w-max">
              {panels.map((panel) => {
                const Icon = panel.icon;
                const isActive = activeTab === panel.id;
                
                return (
                  <TabsTrigger
                    key={panel.id}
                    value={panel.id}
                    className={`
                      flex items-center gap-2 px-4 py-3 rounded-lg text-sm font-medium
                      transition-all duration-200 whitespace-nowrap
                      ${isActive 
                        ? 'bg-white text-sky-800 shadow-sm border border-sky-100' 
                        : 'text-slate-600 hover:text-slate-900 hover:bg-white/50'
                      }
                    `}
                  >
                    <Icon className={`h-4 w-4 ${isActive ? 'text-sky-600' : 'text-slate-500'}`} />
                    <span className="hidden sm:inline">{panel.label}</span>
                    <span className="sm:hidden">{panel.label}</span>
                  </TabsTrigger>
                );
              })}
            </div>
          </TabsList>
        </div>

        {/* Tab Content */}
        <div className="min-h-[500px]">
          {panels.map((panel) => (
            <TabsContent key={panel.id} value={panel.id} className="mt-0 focus-visible:outline-none">
              {/* Panel Header */}
              <div className="mb-6">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-gradient-to-r from-sky-50 to-violet-50 rounded-lg">
                    <panel.icon className="h-5 w-5 text-sky-600" />
                  </div>
                  <div>
                    <h3 className="text-xl font-semibold text-slate-900">{panel.label}</h3>
                    <p className="text-sm text-slate-600 mt-1">{panel.description}</p>
                  </div>
                </div>
              </div>

              {/* Panel Content */}
              <Card className="border-0 shadow-sm">
                <CardContent className="p-0">
                  <AcademicPanel
                    title={panel.label}
                    data={panel.data}
                    columns={panel.columns}
                    formFields={panel.formFields}
                    onDataChange={onDataChange}
                  />
                </CardContent>
              </Card>
            </TabsContent>
          ))}
        </div>
      </Tabs>
    </div>
  );
}
