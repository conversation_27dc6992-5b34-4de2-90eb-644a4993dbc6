// scripts/smoke-teachers.mjs - Smoke test for teachers API
// Usage: set API_TOKEN=eyJ... && node scripts/smoke-teachers.mjs

const base = (process.env.NEXT_PUBLIC_API_URL || "http://127.0.0.1:8000/api/v1").replace(/\/$/, "");
const token = process.env.API_TOKEN; // paste a valid JWT or export it before running

console.log("🔍 Teachers API Smoke Test");
console.log("Base URL:", base);
console.log("Token:", token ? `${token.substring(0, 20)}...` : "❌ NOT SET");

(async () => {
  if (!token) {
    console.error("❌ No API_TOKEN set. Example:");
    console.error("Windows PowerShell: $env:API_TOKEN=\"eyJ...\"; node scripts/smoke-teachers.mjs");
    console.error("Windows CMD: set API_TOKEN=eyJ... && node scripts/smoke-teachers.mjs");
    console.error("Linux/Mac: API_TOKEN=eyJ... node scripts/smoke-teachers.mjs");
    process.exit(1);
  }

  const url = `${base}/teachers/`;
  console.log("\n🚀 Testing:", url);

  try {
    const res = await fetch(url, { 
      headers: { 
        Authorization: `Bearer ${token}`,
        'Content-Type': 'application/json'
      } 
    });

    console.log("📊 Response:", res.status, res.statusText);
    
    const text = await res.text();
    let data;
    
    try {
      data = JSON.parse(text);
    } catch (e) {
      console.log("📄 Raw Response:", text);
      return;
    }

    if (res.status === 200) {
      if (Array.isArray(data)) {
        console.log("✅ Success: Got teachers array");
        console.log("📈 Count:", data.length);
        if (data.length > 0) {
          console.log("👤 Sample teacher:", {
            id: data[0].id,
            name: data[0].name || data[0].full_name,
            email: data[0].email
          });
        } else {
          console.log("📝 Result: Empty list (this is OK - frontend will show 'No teachers found')");
        }
      } else {
        console.log("⚠️ Unexpected format (not array):", data);
      }
    } else if (res.status === 404) {
      console.log("📝 Result: 404 (backend returns 404 for empty list - frontend handles this)");
    } else if (res.status === 401) {
      console.log("🔐 Result: 401 Unauthorized - check your token");
    } else if (res.status === 403) {
      console.log("🚫 Result: 403 Forbidden - insufficient permissions");
    } else {
      console.log("❌ Unexpected status:", res.status);
      console.log("📄 Response:", data);
    }

  } catch (error) {
    console.error("💥 Request failed:", error.message);
    
    if (error.code === 'ECONNREFUSED') {
      console.error("🔌 Connection refused - is the backend server running?");
      console.error("💡 Try: Start your FastAPI server on http://127.0.0.1:8000");
    }
  }

  console.log("\n🎯 Expected Results:");
  console.log("✅ 200 + [] → Frontend shows 'No teachers found'");
  console.log("✅ 200 + [teachers] → Frontend shows teachers list");
  console.log("✅ 404 → Frontend treats as empty list");
  console.log("❌ 401 → Check token/login");
  console.log("❌ 403 → Check user permissions");
})();
