<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Set SUPER_ADMIN Token</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .token-info {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
            border-left: 4px solid #007bff;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border-left-color: #28a745;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border-left-color: #dc3545;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
        }
        button:hover {
            background: #0056b3;
        }
        button:disabled {
            background: #6c757d;
            cursor: not-allowed;
        }
        .code-block {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            overflow-x: auto;
            border: 1px solid #e9ecef;
        }
        .status {
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔐 SUPER_ADMIN Token Setter</h1>
            <p>Set your JWT token to access the School Management System</p>
        </div>

        <div id="status"></div>

        <div class="token-info">
            <h3>📋 Current Token Information</h3>
            <div id="tokenInfo">Loading...</div>
        </div>

        <div style="text-align: center; margin: 30px 0;">
            <button onclick="setToken()">🚀 Set SUPER_ADMIN Token</button>
            <button onclick="clearToken()">🗑️ Clear Token</button>
            <button onclick="checkStatus()">🔍 Check Status</button>
            <button onclick="goToDashboard()">📊 Go to Dashboard</button>
        </div>

        <div class="token-info">
            <h3>🎯 What this does:</h3>
            <ul>
                <li>Sets a valid SUPER_ADMIN JWT token in localStorage</li>
                <li>Configures the role as "SUPER_ADMIN"</li>
                <li>Enables the Create Class button</li>
                <li>Allows full access to admin features</li>
            </ul>
        </div>

        <div class="token-info">
            <h3>🔧 Manual Setup (if button doesn't work):</h3>
            <div class="code-block">
localStorage.setItem('access_token', 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJhZG1pbiIsInJvbGUiOiJTVVBFUl9BRE1JTiIsImV4cCI6MTc1ODU0MjAwMn0.XsVZ8UudtkElrzzetHZo2X1HvXh56r3DHpx3fJMyLfU');
localStorage.setItem('role', 'SUPER_ADMIN');
window.location.reload();
            </div>
        </div>
    </div>

    <script>
        const TOKEN = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJhZG1pbiIsInJvbGUiOiJTVVBFUl9BRE1JTiIsImV4cCI6MTc1ODU0MjAwMn0.XsVZ8UudtkElrzzetHZo2X1HvXh56r3DHpx3fJMyLfU';

        function decodeToken(token) {
            try {
                const payload = JSON.parse(atob(token.split('.')[1]));
                return payload;
            } catch (error) {
                return null;
            }
        }

        function showStatus(message, type = 'info') {
            const statusDiv = document.getElementById('status');
            statusDiv.innerHTML = `<div class="status ${type}">${message}</div>`;
        }

        function updateTokenInfo() {
            const tokenInfoDiv = document.getElementById('tokenInfo');
            const currentToken = localStorage.getItem('access_token');
            const currentRole = localStorage.getItem('role');

            if (currentToken) {
                const payload = decodeToken(currentToken);
                if (payload) {
                    const isExpired = Date.now() / 1000 > payload.exp;
                    const expiryDate = new Date(payload.exp * 1000).toLocaleString();
                    
                    tokenInfoDiv.innerHTML = `
                        <strong>✅ Token Status:</strong> ${isExpired ? '❌ EXPIRED' : '✅ VALID'}<br>
                        <strong>👤 User ID:</strong> ${payload.sub}<br>
                        <strong>🎭 Role:</strong> ${payload.role}<br>
                        <strong>⏰ Expires:</strong> ${expiryDate}<br>
                        <strong>💾 Stored Role:</strong> ${currentRole || 'Not set'}
                    `;
                } else {
                    tokenInfoDiv.innerHTML = '❌ Invalid token format';
                }
            } else {
                tokenInfoDiv.innerHTML = '❌ No token found in localStorage';
            }
        }

        function setToken() {
            try {
                localStorage.setItem('access_token', TOKEN);
                localStorage.setItem('role', 'SUPER_ADMIN');
                
                showStatus('✅ Token set successfully! The page will reload in 2 seconds...', 'success');
                updateTokenInfo();
                
                setTimeout(() => {
                    window.location.reload();
                }, 2000);
            } catch (error) {
                showStatus('❌ Failed to set token: ' + error.message, 'error');
            }
        }

        function clearToken() {
            try {
                localStorage.removeItem('access_token');
                localStorage.removeItem('role');
                
                showStatus('✅ Token cleared successfully!', 'success');
                updateTokenInfo();
            } catch (error) {
                showStatus('❌ Failed to clear token: ' + error.message, 'error');
            }
        }

        function checkStatus() {
            updateTokenInfo();
            
            const currentToken = localStorage.getItem('access_token');
            const currentRole = localStorage.getItem('role');
            
            if (currentToken && currentRole === 'SUPER_ADMIN') {
                const payload = decodeToken(currentToken);
                if (payload && Date.now() / 1000 < payload.exp) {
                    showStatus('✅ Everything looks good! You should be able to see the Create Class button.', 'success');
                } else {
                    showStatus('⚠️ Token is expired. Click "Set SUPER_ADMIN Token" to get a fresh one.', 'error');
                }
            } else {
                showStatus('❌ Token or role not properly set. Click "Set SUPER_ADMIN Token" to fix this.', 'error');
            }
        }

        function goToDashboard() {
            window.location.href = '/dashboard/classes';
        }

        // Initialize on page load
        document.addEventListener('DOMContentLoaded', function() {
            updateTokenInfo();
            checkStatus();
        });
    </script>
</body>
</html>
