{"version": "2.0.0", "tasks": [{"label": "dev", "type": "npm", "script": "dev", "group": {"kind": "build", "isDefault": true}, "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": ["$tsc", "$eslint-stylish"], "runOptions": {"runOn": "folderOpen"}}, {"label": "build", "type": "npm", "script": "build", "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": true}, "problemMatcher": ["$tsc", "$eslint-stylish"]}, {"label": "start", "type": "npm", "script": "start", "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": []}, {"label": "lint", "type": "npm", "script": "lint", "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": true}, "problemMatcher": ["$eslint-stylish"]}, {"label": "lint:fix", "type": "npm", "script": "lint:fix", "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": true}, "problemMatcher": ["$eslint-stylish"]}, {"label": "format", "type": "npm", "script": "format", "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": true}, "problemMatcher": []}, {"label": "format:check", "type": "npm", "script": "format:check", "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": true}, "problemMatcher": []}, {"label": "type-check", "type": "npm", "script": "type-check", "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": true}, "problemMatcher": ["$tsc"]}, {"label": "test", "type": "npm", "script": "test", "group": {"kind": "test", "isDefault": true}, "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": true}, "problemMatcher": []}, {"label": "test:watch", "type": "npm", "script": "test:watch", "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": true}, "problemMatcher": [], "isBackground": true}, {"label": "clean", "type": "shell", "command": "rm", "args": ["-rf", ".next", "out", "node_modules/.cache"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "windows": {"command": "powershell", "args": ["-Command", "Remove-Item -Recurse -Force -ErrorAction SilentlyContinue .next, out, 'node_modules/.cache'"]}}, {"label": "install", "type": "npm", "script": "install", "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": []}, {"label": "analyze", "type": "npm", "script": "analyze", "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": true}, "problemMatcher": []}, {"label": "storybook", "type": "npm", "script": "storybook", "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": [], "isBackground": true}]}