import { redirect } from "next/navigation";

/**
 * Home Page - Redirects to appropriate page based on authentication
 * 
 * This is the root page that handles initial routing:
 * - Authenticated users go to dashboard
 * - Unauthenticated users go to login
 */
export default function HomePage() {
  // For now, redirect to login
  // TODO: Add authentication check and redirect accordingly
  redirect("/login");
}
