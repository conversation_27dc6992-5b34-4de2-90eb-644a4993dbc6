'use client';

import { ModuleError } from '@/components/ui/module-error';

interface CreateStudentFeeErrorProps {
  error: Error & { digest?: string };
  reset: () => void;
}

export default function CreateStudentFeeError({ error, reset }: CreateStudentFeeErrorProps) {
  return (
    <ModuleError
      error={error}
      reset={reset}
      moduleName="Create Fee Record"
      moduleIcon="💰"
      backHref="/dashboard/student-fee"
    />
  );
}
