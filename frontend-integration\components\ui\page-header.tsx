import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { LucideIcon } from 'lucide-react';
import Link from 'next/link';
import { ReactNode } from 'react';

interface PageHeaderProps {
  title: string;
  description?: string;
  icon?: LucideIcon;
  badge?: {
    label: string;
    variant?: 'default' | 'secondary' | 'destructive' | 'outline';
  };
  actions?: {
    label: string;
    href?: string;
    onClick?: () => void;
    icon?: LucideIcon;
    variant?: 'default' | 'destructive' | 'outline' | 'secondary' | 'ghost' | 'link';
  }[];
  children?: ReactNode;
}

export function PageHeader({
  title,
  description,
  icon: Icon,
  badge,
  actions = [],
  children,
}: PageHeaderProps) {
  return (
    <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 mb-6">
      <div>
        <h1 className="text-2xl sm:text-3xl font-bold tracking-tight flex items-center">
          {Icon && <Icon className="w-8 h-8 mr-3 text-blue-600" />}
          {title}
        </h1>
        {(description || badge) && (
          <div className="flex items-center gap-2 mt-1">
            {description && (
              <p className="text-muted-foreground">{description}</p>
            )}
            {badge && (
              <Badge variant={badge.variant || 'outline'}>
                {badge.label}
              </Badge>
            )}
          </div>
        )}
        {children}
      </div>
      
      {actions.length > 0 && (
        <div className="flex flex-col sm:flex-row gap-2">
          {actions.map((action, index) => {
            const ActionIcon = action.icon;
            const buttonContent = (
              <>
                {ActionIcon && <ActionIcon className="w-4 h-4 mr-2" />}
                {action.label}
              </>
            );

            if (action.href) {
              return (
                <Link key={index} href={action.href}>
                  <Button 
                    variant={action.variant || 'default'}
                    className="w-full sm:w-auto"
                  >
                    {buttonContent}
                  </Button>
                </Link>
              );
            }

            return (
              <Button
                key={index}
                onClick={action.onClick}
                variant={action.variant || 'default'}
                className="w-full sm:w-auto"
              >
                {buttonContent}
              </Button>
            );
          })}
        </div>
      )}
    </div>
  );
}
