// scripts/get-jwt-token.mjs - Get JWT token for testing
// This script helps you obtain a valid JWT token for API testing

const API_BASE = process.env.NEXT_PUBLIC_API_BASE || 'http://127.0.0.1:8000';

console.log('🔐 JWT Token Retrieval Script');
console.log('API Base:', API_BASE);
console.log('');

// Test credentials (adjust these based on your backend)
const TEST_CREDENTIALS = [
  { username: 'admin', password: 'admin123', description: 'Default admin credentials' },
  { username: '<EMAIL>', password: 'admin123', description: 'Admin email format' },
  { username: 'test', password: 'test123', description: 'Test user credentials' },
];

// Possible auth endpoints to try
const AUTH_ENDPOINTS = [
  '/api/v1/auth/login',
  '/api/v1/users/auth/login',
  '/auth/login',
];

async function tryLogin(endpoint, credentials) {
  console.log(`🚀 Trying: ${endpoint}`);
  console.log(`   Credentials: ${credentials.username} / ${credentials.password}`);
  
  try {
    // Try form-encoded format (common for FastAPI OAuth2)
    const formData = new URLSearchParams();
    formData.append('username', credentials.username);
    formData.append('password', credentials.password);
    
    const response = await fetch(`${API_BASE}${endpoint}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: formData
    });

    console.log(`   Status: ${response.status} ${response.statusText}`);
    
    if (response.ok) {
      const data = await response.json();
      console.log('   ✅ SUCCESS!');
      
      // Extract token from various possible response formats
      const token = data.access_token || data.token || data.jwt;
      
      if (token) {
        console.log('   🎯 Token found:', token.substring(0, 50) + '...');
        console.log('');
        console.log('🎉 SUCCESS! Copy this token:');
        console.log('');
        console.log(`TOKEN: ${token}`);
        console.log('');
        console.log('💡 Now run the smoke test:');
        console.log('Windows PowerShell:');
        console.log(`$env:API_TOKEN="${token}"; node scripts/smoke-classes.mjs`);
        console.log('');
        console.log('Windows CMD:');
        console.log(`set API_TOKEN=${token} && node scripts/smoke-classes.mjs`);
        console.log('');
        console.log('Linux/Mac:');
        console.log(`API_TOKEN=${token} node scripts/smoke-classes.mjs`);
        
        return token;
      } else {
        console.log('   ⚠️  Response successful but no token found');
        console.log('   📄 Response:', JSON.stringify(data, null, 2));
      }
    } else {
      const errorText = await response.text();
      console.log(`   ❌ Failed: ${errorText}`);
    }
  } catch (error) {
    if (error.code === 'ECONNREFUSED') {
      console.log('   🔌 Connection refused - backend server not running');
    } else {
      console.log(`   ❌ Error: ${error.message}`);
    }
  }
  
  console.log('');
  return null;
}

async function tryJsonLogin(endpoint, credentials) {
  console.log(`🚀 Trying JSON format: ${endpoint}`);
  console.log(`   Credentials: ${credentials.username} / ${credentials.password}`);
  
  try {
    const response = await fetch(`${API_BASE}${endpoint}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        username: credentials.username,
        password: credentials.password,
        email: credentials.username, // Some APIs expect email field
      })
    });

    console.log(`   Status: ${response.status} ${response.statusText}`);
    
    if (response.ok) {
      const data = await response.json();
      console.log('   ✅ SUCCESS!');
      
      const token = data.access_token || data.token || data.jwt;
      
      if (token) {
        console.log('   🎯 Token found:', token.substring(0, 50) + '...');
        return token;
      } else {
        console.log('   ⚠️  Response successful but no token found');
        console.log('   📄 Response:', JSON.stringify(data, null, 2));
      }
    } else {
      const errorText = await response.text();
      console.log(`   ❌ Failed: ${errorText}`);
    }
  } catch (error) {
    console.log(`   ❌ Error: ${error.message}`);
  }
  
  console.log('');
  return null;
}

(async () => {
  console.log('🔍 Attempting to retrieve JWT token...');
  console.log('');
  
  let tokenFound = false;
  
  // Try each endpoint with each credential set
  for (const endpoint of AUTH_ENDPOINTS) {
    if (tokenFound) break;
    
    for (const credentials of TEST_CREDENTIALS) {
      if (tokenFound) break;
      
      // Try form-encoded format first
      const token = await tryLogin(endpoint, credentials);
      if (token) {
        tokenFound = true;
        break;
      }
      
      // Try JSON format
      const jsonToken = await tryJsonLogin(endpoint, credentials);
      if (jsonToken) {
        tokenFound = true;
        break;
      }
    }
  }
  
  if (!tokenFound) {
    console.log('❌ No valid token found with any endpoint/credential combination');
    console.log('');
    console.log('💡 Troubleshooting:');
    console.log('1. Make sure your backend server is running');
    console.log('2. Check if the credentials are correct');
    console.log('3. Verify the auth endpoint exists');
    console.log('4. Check backend logs for authentication errors');
    console.log('');
    console.log('🔧 Manual testing:');
    console.log('Try these endpoints in your browser or Postman:');
    AUTH_ENDPOINTS.forEach(endpoint => {
      console.log(`   ${API_BASE}${endpoint}`);
    });
  }
})();
