// <PERSON><PERSON>t to set JW<PERSON> token in frontend localStorage
// This simulates what happens when a user logs in through the UI

const token =
  'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJhZG1pbiIsInJvbGUiOiJTVVBFUl9BRE1JTiIsImV4cCI6MTc1ODYyNTEyNH0.QT0c8rhi64u2aGLOZNN_lt93NmGtoMkJeY1XqcBRX1Q';

console.log('🔧 Frontend Token Setter');
console.log('='.repeat(50));

// Decode token to show info
try {
  const payload = JSON.parse(atob(token.split('.')[1]));
  console.log('📋 Token Information:');
  console.log(`   User ID: ${payload.sub}`);
  console.log(`   Role: ${payload.role}`);
  console.log(`   Expires: ${new Date(payload.exp * 1000).toISOString()}`);

  const isExpired = Date.now() / 1000 > payload.exp;
  console.log(`   Status: ${isExpired ? '❌ EXPIRED' : '✅ VALID'}`);

  if (isExpired) {
    console.log('\n❌ Token is expired! Get a new one with:');
    console.log('   node scripts/get-jwt-token.mjs');
    process.exit(1);
  }
} catch (error) {
  console.log('❌ Failed to decode token:', error.message);
  process.exit(1);
}

console.log('\n🎯 Instructions to set token in frontend:');
console.log('\n1. Open your browser and go to: http://localhost:3000/dashboard/classes');
console.log('\n2. Open browser Developer Tools (F12)');
console.log('\n3. Go to Console tab');
console.log('\n4. Paste and run this code:');

console.log('\n' + '='.repeat(80));
console.log('// PASTE THIS IN BROWSER CONSOLE:');
console.log('');
console.log(`localStorage.setItem('access_token', '${token}');`);
console.log(`localStorage.setItem('role', 'SUPER_ADMIN');`);
console.log('');
console.log('// Verify it was set:');
console.log('console.log("Token set:", !!localStorage.getItem("access_token"));');
console.log('console.log("Role set:", localStorage.getItem("role"));');
console.log('');
console.log('// Refresh the page');
console.log('window.location.reload();');
console.log('='.repeat(80));

console.log('\n5. After running the code above, refresh the page');
console.log('\n6. The debug component should now show:');
console.log('   ✅ User Exists: true');
console.log('   ✅ User Role: "SUPER_ADMIN"');
console.log('   ✅ canCreateClass(): true');

console.log('\n💡 Alternative: Use the auth store directly in console:');
console.log('');
console.log('// In browser console after page loads:');
console.log('const { setAuth } = window.__ZUSTAND_STORE__?.getState?.() || {};');
console.log('if (setAuth) {');
console.log(`  setAuth({ token: '${token}', role: 'SUPER_ADMIN', userId: 'admin' });`);
console.log('  console.log("Auth set via Zustand store");');
console.log('  window.location.reload();');
console.log('} else {');
console.log('  console.log("Zustand store not found, use localStorage method above");');
console.log('}');

console.log('\n🔍 Debugging:');
console.log('If the user object is still null after setting the token:');
console.log('1. Check browser Network tab for /auth/me API call');
console.log('2. Verify backend server is running on http://127.0.0.1:8000');
console.log('3. Check browser console for any error messages');
console.log('4. Ensure AuthProvider is properly initialized');

console.log('\n✅ Expected Result:');
console.log('After setting the token and refreshing:');
console.log('- Debug component shows user data');
console.log('- Create Class button appears');
console.log('- User can navigate to create page');

console.log('\n🎉 Ready! Follow the instructions above to set the token.');
