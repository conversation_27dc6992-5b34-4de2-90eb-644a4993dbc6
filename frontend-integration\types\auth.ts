/**
 * Authentication and User Types
 * 
 * Comprehensive type definitions matching FastAPI backend
 * No ad-hoc shapes - these match backend exactly
 */

// ============================================================================
// AUTH TYPES
// ============================================================================

export interface Token {
  access_token: string;
  token_type: 'bearer';
}

export interface LoginPayload {
  username?: string;
  email?: string;
  password: string;
}

export interface RegisterPayload {
  username: string;
  email: string;
  password: string;
  first_name?: string;
  last_name?: string;
}

export interface ChangePasswordPayload {
  current_password: string;
  new_password: string;
}

// ============================================================================
// USER TYPES
// ============================================================================

export interface User {
  id: string;
  email: string;
  username: string;
  first_name?: string;
  last_name?: string;
  is_active: boolean;
  roles?: string[];
  created_at?: string;
  updated_at?: string;
}

export interface UpdateMePayload {
  email?: string;
  username?: string;
  first_name?: string;
  last_name?: string;
}

export interface AdminUpdateUserPayload {
  email?: string;
  username?: string;
  first_name?: string;
  last_name?: string;
  is_active?: boolean;
}

// ============================================================================
// ADMIN USER MANAGEMENT TYPES
// ============================================================================

export interface UserListParams {
  search?: string;
  page?: number;
  size?: number;
  is_active?: boolean;
  role?: string;
}

export interface UserListResponse {
  items: User[];
  total: number;
  page: number;
  size: number;
  pages: number;
}

export interface UserActivationResponse {
  id: string;
  is_active: boolean;
}

export interface UserStatsResponse {
  total: number;
  active: number;
  inactive: number;
  by_role?: Record<string, number>;
  recent_registrations?: number;
}

// ============================================================================
// API RESPONSE TYPES
// ============================================================================

export interface ApiError {
  detail: string;
  status_code?: number;
  request_id?: string;
}

export interface ValidationError {
  detail: Array<{
    loc: (string | number)[];
    msg: string;
    type: string;
  }>;
}

// ============================================================================
// FRONTEND STATE TYPES
// ============================================================================

export interface AuthState {
  isAuthenticated: boolean;
  user: User | null;
  isLoading: boolean;
  error: string | null;
}

export interface LoginFormData {
  username: string;
  password: string;
  rememberMe?: boolean;
}

export interface ProfileFormData {
  email: string;
  username: string;
  first_name: string;
  last_name: string;
}

export interface ChangePasswordFormData {
  current_password: string;
  new_password: string;
  confirm_password: string;
}

export interface AdminUserFormData {
  email: string;
  username: string;
  first_name: string;
  last_name: string;
  is_active: boolean;
}

// ============================================================================
// UTILITY TYPES
// ============================================================================

export type AuthNamespace = 'auth' | 'users';

export interface NamespaceDetectionResult {
  isValid: boolean;
  detectedNamespace: AuthNamespace | null;
  currentNamespace: string;
  suggestion?: string;
}

// ============================================================================
// HOOK TYPES
// ============================================================================

export interface UseLoginOptions {
  onSuccess?: (data: Token) => void;
  onError?: (error: Error) => void;
}

export interface UseLogoutOptions {
  onSuccess?: () => void;
  onError?: (error: Error) => void;
}

export interface UseMeOptions {
  enabled?: boolean;
  staleTime?: number;
  retry?: number | boolean;
}

export interface UseUsersOptions {
  params?: UserListParams;
  enabled?: boolean;
}

export interface UseUserOptions {
  id: string;
  enabled?: boolean;
}

// ============================================================================
// TABLE TYPES (for admin users page)
// ============================================================================

export interface UserTableColumn {
  id: keyof User | 'actions';
  label: string;
  sortable?: boolean;
  width?: string;
}

export interface UserTableAction {
  id: 'edit' | 'activate' | 'deactivate' | 'view';
  label: string;
  icon?: string;
  variant?: 'default' | 'destructive' | 'outline';
  disabled?: (user: User) => boolean;
}

export interface UserTableFilters {
  search: string;
  is_active: 'all' | 'active' | 'inactive';
  role: string;
}

// ============================================================================
// SESSION TYPES
// ============================================================================

export interface SessionData {
  user: User;
  expires_at: string;
}

export interface CookieOptions {
  httpOnly: boolean;
  secure: boolean;
  sameSite: 'lax' | 'strict' | 'none';
  path: string;
  maxAge: number;
}
