'use client';

import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { ArrowLeft, Bell, Save } from 'lucide-react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';

// Mock notification data
const mockNotifications = [
  {
    id: 1,
    title: 'Parent-Teacher Conference Reminder',
    message: 'Reminder: Parent-teacher conferences are scheduled for March 20th. Please confirm your attendance.',
    type: 'Reminder',
    priority: 'High',
    status: 'Unread',
    sender: 'Administration',
    recipient: 'All Parents',
  },
  {
    id: 2,
    title: 'School Closure Due to Weather',
    message: 'Due to severe weather conditions, the school will be closed tomorrow, March 2nd. All classes are cancelled.',
    type: 'Alert',
    priority: 'Critical',
    status: 'Read',
    sender: 'Principal Office',
    recipient: 'All Students & Parents',
  },
];

interface EditNotificationPageProps {
  params: {
    id: string;
  };
}

export default function EditNotificationPage({ params }: EditNotificationPageProps) {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  const [formData, setFormData] = useState({
    title: '',
    message: '',
    type: '',
    priority: '',
    sender: '',
    recipient: '',
  });

  useEffect(() => {
    const notification = mockNotifications.find(n => n.id === parseInt(params.id));
    if (notification) {
      setFormData({
        title: notification.title,
        message: notification.message,
        type: notification.type,
        priority: notification.priority,
        sender: notification.sender,
        recipient: notification.recipient,
      });
    }
  }, [params.id]);

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    try {
      // TODO: Replace with actual API call
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      console.log('Updating notification:', formData);
      
      // Redirect to notification detail on success
      router.push(`/dashboard/notifications/${params.id}`);
    } catch (error) {
      console.error('Error updating notification:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const notificationTypes = ['Alert', 'Reminder', 'Announcement', 'Information'];
  const priorities = ['Critical', 'High', 'Medium', 'Low'];
  const senders = ['Administration', 'Principal Office', 'Science Department', 'Library', 'Cafeteria'];
  const recipients = ['All Students', 'All Parents', 'All Teachers', 'All Staff', 'All Students & Parents'];

  return (
    <div className='container mx-auto p-4 sm:p-6 lg:p-8 max-w-4xl'>
      {/* Header */}
      <div className='flex items-center gap-4 mb-8'>
        <Link href={`/dashboard/notifications/${params.id}`}>
          <Button variant='outline' size='sm'>
            <ArrowLeft className='w-4 h-4 mr-2' />
            Back to Notification
          </Button>
        </Link>
        <div>
          <h1 className='text-3xl font-bold text-gray-900 flex items-center gap-2'>
            <Bell className='w-8 h-8 text-blue-600' />
            Edit Notification
          </h1>
          <p className='text-gray-600 mt-1'>Update notification details and settings</p>
        </div>
      </div>

      {/* Form */}
      <form onSubmit={handleSubmit} className='space-y-8'>
        {/* Basic Information */}
        <Card>
          <CardHeader>
            <CardTitle>Notification Content</CardTitle>
          </CardHeader>
          <CardContent className='space-y-6'>
            <div className='space-y-2'>
              <Label htmlFor='title'>Title *</Label>
              <Input
                id='title'
                placeholder='e.g., Parent-Teacher Conference Reminder'
                value={formData.title}
                onChange={e => handleInputChange('title', e.target.value)}
                required
              />
            </div>

            <div className='space-y-2'>
              <Label htmlFor='message'>Message *</Label>
              <Textarea
                id='message'
                placeholder='Enter the notification message...'
                value={formData.message}
                onChange={e => handleInputChange('message', e.target.value)}
                rows={6}
                required
              />
            </div>

            <div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
              <div className='space-y-2'>
                <Label htmlFor='type'>Type *</Label>
                <Select value={formData.type} onValueChange={value => handleInputChange('type', value)}>
                  <SelectTrigger>
                    <SelectValue placeholder='Select type' />
                  </SelectTrigger>
                  <SelectContent>
                    {notificationTypes.map(type => (
                      <SelectItem key={type} value={type}>
                        {type}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div className='space-y-2'>
                <Label htmlFor='priority'>Priority *</Label>
                <Select value={formData.priority} onValueChange={value => handleInputChange('priority', value)}>
                  <SelectTrigger>
                    <SelectValue placeholder='Select priority' />
                  </SelectTrigger>
                  <SelectContent>
                    {priorities.map(priority => (
                      <SelectItem key={priority} value={priority}>
                        {priority}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Recipients & Sender */}
        <Card>
          <CardHeader>
            <CardTitle>Recipients & Sender</CardTitle>
          </CardHeader>
          <CardContent className='space-y-6'>
            <div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
              <div className='space-y-2'>
                <Label htmlFor='sender'>Sender *</Label>
                <Select value={formData.sender} onValueChange={value => handleInputChange('sender', value)}>
                  <SelectTrigger>
                    <SelectValue placeholder='Select sender' />
                  </SelectTrigger>
                  <SelectContent>
                    {senders.map(sender => (
                      <SelectItem key={sender} value={sender}>
                        {sender}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div className='space-y-2'>
                <Label htmlFor='recipient'>Recipients *</Label>
                <Select value={formData.recipient} onValueChange={value => handleInputChange('recipient', value)}>
                  <SelectTrigger>
                    <SelectValue placeholder='Select recipients' />
                  </SelectTrigger>
                  <SelectContent>
                    {recipients.map(recipient => (
                      <SelectItem key={recipient} value={recipient}>
                        {recipient}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Actions */}
        <div className='flex justify-end gap-4'>
          <Link href={`/dashboard/notifications/${params.id}`}>
            <Button type='button' variant='outline'>
              Cancel
            </Button>
          </Link>
          <Button type='submit' disabled={isLoading}>
            {isLoading ? (
              <>
                <div className='w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2' />
                Updating...
              </>
            ) : (
              <>
                <Save className='w-4 h-4 mr-2' />
                Update Notification
              </>
            )}
          </Button>
        </div>
      </form>
    </div>
  );
}
