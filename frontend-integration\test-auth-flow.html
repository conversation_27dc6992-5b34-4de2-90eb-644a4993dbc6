<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Teachers Auth Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .success { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background-color: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background-color: #0056b3; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 4px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>🔐 Teachers Module Authentication Test</h1>
    
    <div class="test-section">
        <h2>1. Authentication Status</h2>
        <div id="auth-status"></div>
        <button onclick="checkAuthStatus()">Check Auth Status</button>
    </div>

    <div class="test-section">
        <h2>2. JWT Token Information</h2>
        <div id="token-info"></div>
        <button onclick="checkTokenInfo()">Check Token Info</button>
    </div>

    <div class="test-section">
        <h2>3. Role-Based Access Test</h2>
        <div id="role-test"></div>
        <button onclick="testRoleAccess()">Test Role Access</button>
    </div>

    <div class="test-section">
        <h2>4. API Authentication Test</h2>
        <div id="api-test"></div>
        <button onclick="testApiAuth()">Test API Auth</button>
    </div>

    <div class="test-section">
        <h2>5. Teacher Creation Test</h2>
        <div id="create-test"></div>
        <button onclick="testTeacherCreation()">Test Teacher Creation</button>
    </div>

    <script>
        function checkAuthStatus() {
            const statusDiv = document.getElementById('auth-status');
            
            // Check localStorage
            const token = localStorage.getItem('access_token');
            const role = localStorage.getItem('user_role');
            const userId = localStorage.getItem('user_id');
            
            let html = '<h3>LocalStorage Data:</h3>';
            html += `<div class="${token ? 'success' : 'error'}">Token: ${token ? '✅ Present' : '❌ Missing'}</div>`;
            html += `<div class="${role ? 'success' : 'error'}">Role: ${role || '❌ Missing'}</div>`;
            html += `<div class="${userId ? 'success' : 'info'}">User ID: ${userId || 'Not set'}</div>`;
            
            // Check cookies
            const cookies = document.cookie.split(';').map(c => c.trim());
            const authCookie = cookies.find(c => c.startsWith('access_token='));
            html += `<div class="${authCookie ? 'success' : 'info'}">Auth Cookie: ${authCookie ? '✅ Present' : 'Not set'}</div>`;
            
            statusDiv.innerHTML = html;
        }

        function checkTokenInfo() {
            const infoDiv = document.getElementById('token-info');
            const token = localStorage.getItem('access_token');
            
            if (!token) {
                infoDiv.innerHTML = '<div class="error">No JWT token found</div>';
                return;
            }
            
            try {
                const payload = JSON.parse(atob(token.split('.')[1]));
                let html = '<h3>JWT Payload:</h3>';
                html += '<pre>' + JSON.stringify(payload, null, 2) + '</pre>';
                
                // Check expiration
                const now = Date.now() / 1000;
                const isExpired = payload.exp && payload.exp < now;
                html += `<div class="${isExpired ? 'error' : 'success'}">Token Status: ${isExpired ? '❌ Expired' : '✅ Valid'}</div>`;
                
                if (payload.exp) {
                    const expDate = new Date(payload.exp * 1000);
                    html += `<div class="info">Expires: ${expDate.toLocaleString()}</div>`;
                }
                
                infoDiv.innerHTML = html;
            } catch (error) {
                infoDiv.innerHTML = `<div class="error">Failed to decode token: ${error.message}</div>`;
            }
        }

        function testRoleAccess() {
            const testDiv = document.getElementById('role-test');
            const role = localStorage.getItem('user_role');
            
            let html = '<h3>Role Access Test:</h3>';
            html += `<div class="info">Current Role: ${role || 'None'}</div>`;
            
            const isAdmin = role === 'ADMIN' || role === 'SUPER_ADMIN';
            html += `<div class="${isAdmin ? 'success' : 'error'}">Admin Access: ${isAdmin ? '✅ Granted' : '❌ Denied'}</div>`;
            
            const canCreateTeachers = isAdmin;
            html += `<div class="${canCreateTeachers ? 'success' : 'error'}">Can Create Teachers: ${canCreateTeachers ? '✅ Yes' : '❌ No'}</div>`;
            
            testDiv.innerHTML = html;
        }

        async function testApiAuth() {
            const testDiv = document.getElementById('api-test');
            const token = localStorage.getItem('access_token');
            
            if (!token) {
                testDiv.innerHTML = '<div class="error">No token available for API test</div>';
                return;
            }
            
            testDiv.innerHTML = '<div class="info">Testing API authentication...</div>';
            
            try {
                const response = await fetch('/api/v1/teachers', {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    }
                });
                
                let html = '<h3>API Test Results:</h3>';
                html += `<div class="${response.ok ? 'success' : 'error'}">Status: ${response.status} ${response.statusText}</div>`;
                
                if (response.ok) {
                    const data = await response.json();
                    html += '<div class="success">✅ API authentication successful</div>';
                    html += `<div class="info">Response: ${JSON.stringify(data).substring(0, 200)}...</div>`;
                } else {
                    const errorText = await response.text();
                    html += `<div class="error">❌ API authentication failed: ${errorText}</div>`;
                }
                
                testDiv.innerHTML = html;
            } catch (error) {
                testDiv.innerHTML = `<div class="error">❌ API test failed: ${error.message}</div>`;
            }
        }

        async function testTeacherCreation() {
            const testDiv = document.getElementById('create-test');
            const token = localStorage.getItem('access_token');
            const role = localStorage.getItem('user_role');
            
            if (!token) {
                testDiv.innerHTML = '<div class="error">No token available for teacher creation test</div>';
                return;
            }
            
            if (role !== 'ADMIN' && role !== 'SUPER_ADMIN') {
                testDiv.innerHTML = '<div class="error">Insufficient permissions for teacher creation test</div>';
                return;
            }
            
            testDiv.innerHTML = '<div class="info">Testing teacher creation...</div>';
            
            const testTeacher = {
                name: 'Test Teacher ' + Date.now(),
                subject: 'Test Subject',
                email: '<EMAIL>',
                department: 'Test Department',
                status: 'ACTIVE'
            };
            
            try {
                const response = await fetch('/api/v1/teachers', {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(testTeacher)
                });
                
                let html = '<h3>Teacher Creation Test:</h3>';
                html += `<div class="${response.ok ? 'success' : 'error'}">Status: ${response.status} ${response.statusText}</div>`;
                
                if (response.ok) {
                    const createdTeacher = await response.json();
                    html += '<div class="success">✅ Teacher creation successful</div>';
                    html += '<pre>' + JSON.stringify(createdTeacher, null, 2) + '</pre>';
                } else {
                    const errorText = await response.text();
                    html += `<div class="error">❌ Teacher creation failed: ${errorText}</div>`;
                }
                
                testDiv.innerHTML = html;
            } catch (error) {
                testDiv.innerHTML = `<div class="error">❌ Teacher creation test failed: ${error.message}</div>`;
            }
        }

        // Auto-run initial checks
        window.onload = function() {
            checkAuthStatus();
            checkTokenInfo();
            testRoleAccess();
        };
    </script>
</body>
</html>
