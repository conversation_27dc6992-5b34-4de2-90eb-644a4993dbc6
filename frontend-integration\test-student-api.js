/**
 * Test Student API Endpoints
 *
 * This script tests the FastAPI backend student endpoints
 * to understand the exact schema and fix integration issues.
 */

const axios = require('axios');

const API_BASE_URL = 'http://127.0.0.1:8000/api/v1';

async function testStudentAPI() {
  console.log('🧪 Testing Student API Endpoints...\n');

  try {
    // Test 1: Get students list (should work)
    console.log('1. Testing GET /students...');
    const listResponse = await axios.get(`${API_BASE_URL}/students?page=1&size=5`);
    console.log('✅ GET /students successful');
    console.log('   Status:', listResponse.status);
    console.log('   Response structure:', Object.keys(listResponse.data));
    console.log('   Sample data:', JSON.stringify(listResponse.data, null, 2));
  } catch (error) {
    console.log('❌ GET /students failed:', error.message);
    if (error.response) {
      console.log('   Status:', error.response.status);
      console.log('   Data:', error.response.data);
    }
  }

  console.log('\n' + '='.repeat(50) + '\n');

  try {
    // Test 2: Get API schema/docs
    console.log('2. Testing GET /openapi.json for schema...');
    const schemaResponse = await axios.get(`${API_BASE_URL.replace('/api/v1', '')}/openapi.json`);
    console.log('✅ OpenAPI schema retrieved');

    // Find student creation schema
    const studentPaths = schemaResponse.data.paths['/api/v1/students'];
    if (studentPaths && studentPaths.post) {
      console.log('   Student POST schema found:');
      const postSchema = studentPaths.post.requestBody?.content?.['application/json']?.schema;
      if (postSchema) {
        console.log('   Required fields:', postSchema.required || 'Not specified');
        console.log('   Properties:', Object.keys(postSchema.properties || {}));
        console.log('   Full schema:', JSON.stringify(postSchema, null, 2));
      }
    }
  } catch (error) {
    console.log('❌ OpenAPI schema failed:', error.message);
  }

  console.log('\n' + '='.repeat(50) + '\n');

  try {
    // Test 3: Try creating a student with minimal data
    console.log('3. Testing POST /students with minimal data...');
    const minimalStudent = {
      reg_no: 'TEST001',
      first_name: 'Test',
      last_name: 'Student',
      gender: 'male',
      class_id: '1',
      section_id: '1',
      password: 'TestPassword123',
      parent_id: 'test-parent-id',
    };

    console.log('   Sending payload:', JSON.stringify(minimalStudent, null, 2));
    const createResponse = await axios.post(`${API_BASE_URL}/students`, minimalStudent);
    console.log('✅ POST /students successful');
    console.log('   Status:', createResponse.status);
    console.log('   Created student:', JSON.stringify(createResponse.data, null, 2));
  } catch (error) {
    console.log('❌ POST /students failed:', error.message);
    if (error.response) {
      console.log('   Status:', error.response.status);
      console.log('   Error details:', JSON.stringify(error.response.data, null, 2));
    }
  }

  console.log('\n' + '='.repeat(50) + '\n');

  try {
    // Test 4: Test without trailing slash (correct endpoint)
    console.log('4. Testing POST /students (without trailing slash)...');
    const testStudent = {
      reg_no: 'TEST002',
      first_name: 'Test2',
      last_name: 'Student2',
      gender: 'female',
      class_id: '1',
      section_id: '1',
      password: 'TestPassword123',
      parent_id: 'test-parent-id',
    };

    const createResponse = await axios.post(`${API_BASE_URL}/students`, testStudent);
    console.log('✅ POST /students (without slash) successful');
    console.log('   Status:', createResponse.status);
  } catch (error) {
    console.log('❌ POST /students (without slash) failed:', error.message);
    if (error.response) {
      console.log('   Status:', error.response.status);
      console.log('   This confirms trailing slash behavior');
    }
  }
}

// Run the test
testStudentAPI().catch(console.error);
