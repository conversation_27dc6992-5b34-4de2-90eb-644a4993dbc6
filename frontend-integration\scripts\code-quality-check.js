#!/usr/bin/env node

/**
 * Code Quality Check Script
 * 
 * Comprehensive code quality validation for TypeScript files:
 * - Strict typing validation
 * - ESLint compliance
 * - Prettier formatting
 * - Dead code detection
 * - Generic variable name detection
 * - Console statement validation
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Configuration
const CONFIG = {
  srcDir: '.',
  extensions: ['.ts', '.tsx'],
  excludeDirs: ['node_modules', '.next', 'dist', 'build'],
  genericVariableNames: ['data', 'item', 'result', 'response', 'props'],
  allowedConsoleInDev: true,
};

// Colors for console output
const colors = {
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
  reset: '\x1b[0m',
  bold: '\x1b[1m',
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function logHeader(title) {
  log('\n' + '='.repeat(80), 'cyan');
  log(`🔍 ${title}`, 'bold');
  log('='.repeat(80), 'cyan');
}

function logStep(step, status = 'info') {
  const emoji = status === 'success' ? '✅' : status === 'error' ? '❌' : status === 'warning' ? '⚠️' : '🔍';
  const color = status === 'success' ? 'green' : status === 'error' ? 'red' : status === 'warning' ? 'yellow' : 'blue';
  log(`${emoji} ${step}`, color);
}

// Get all TypeScript files
function getTypeScriptFiles(dir, files = []) {
  const items = fs.readdirSync(dir);
  
  for (const item of items) {
    const fullPath = path.join(dir, item);
    const stat = fs.statSync(fullPath);
    
    if (stat.isDirectory() && !CONFIG.excludeDirs.includes(item)) {
      getTypeScriptFiles(fullPath, files);
    } else if (stat.isFile() && CONFIG.extensions.some(ext => item.endsWith(ext))) {
      files.push(fullPath);
    }
  }
  
  return files;
}

// Check TypeScript strict mode
function checkTypeScriptConfig() {
  logStep('Checking TypeScript configuration...', 'info');
  
  try {
    const tsconfigPath = path.join(CONFIG.srcDir, 'tsconfig.json');
    if (!fs.existsSync(tsconfigPath)) {
      logStep('tsconfig.json not found', 'error');
      return false;
    }
    
    const tsconfig = JSON.parse(fs.readFileSync(tsconfigPath, 'utf8'));
    const compilerOptions = tsconfig.compilerOptions || {};
    
    const requiredOptions = {
      strict: true,
      noImplicitAny: true,
      strictNullChecks: true,
      strictFunctionTypes: true,
      noImplicitReturns: true,
      noFallthroughCasesInSwitch: true,
    };
    
    let allGood = true;
    for (const [option, expected] of Object.entries(requiredOptions)) {
      if (compilerOptions[option] !== expected) {
        logStep(`Missing or incorrect TypeScript option: ${option} should be ${expected}`, 'error');
        allGood = false;
      }
    }
    
    if (allGood) {
      logStep('TypeScript configuration is strict ✓', 'success');
    }
    
    return allGood;
  } catch (error) {
    logStep(`Error checking TypeScript config: ${error.message}`, 'error');
    return false;
  }
}

// Check ESLint configuration
function checkESLintConfig() {
  logStep('Checking ESLint configuration...', 'info');
  
  try {
    const eslintConfigPath = path.join(CONFIG.srcDir, '.eslintrc.json');
    if (!fs.existsSync(eslintConfigPath)) {
      logStep('.eslintrc.json not found', 'error');
      return false;
    }
    
    const eslintConfig = JSON.parse(fs.readFileSync(eslintConfigPath, 'utf8'));
    const rules = eslintConfig.rules || {};
    
    const requiredRules = {
      '@typescript-eslint/no-unused-vars': 'error',
      '@typescript-eslint/no-explicit-any': 'error',
      'unused-imports/no-unused-imports': 'error',
    };
    
    let allGood = true;
    for (const [rule, expected] of Object.entries(requiredRules)) {
      if (rules[rule] !== expected) {
        logStep(`Missing or incorrect ESLint rule: ${rule} should be ${expected}`, 'warning');
        allGood = false;
      }
    }
    
    if (allGood) {
      logStep('ESLint configuration is comprehensive ✓', 'success');
    }
    
    return allGood;
  } catch (error) {
    logStep(`Error checking ESLint config: ${error.message}`, 'error');
    return false;
  }
}

// Check for generic variable names
function checkGenericVariableNames(files) {
  logStep('Checking for generic variable names...', 'info');
  
  let issues = [];
  
  for (const file of files) {
    try {
      const content = fs.readFileSync(file, 'utf8');
      const lines = content.split('\n');
      
      lines.forEach((line, index) => {
        // Skip comments and strings
        if (line.trim().startsWith('//') || line.trim().startsWith('*')) return;
        
        // Check for generic variable declarations
        CONFIG.genericVariableNames.forEach(genericName => {
          const patterns = [
            new RegExp(`\\b${genericName}\\s*[:=]`, 'g'),
            new RegExp(`\\b${genericName}\\s*\\)\\s*=>`, 'g'),
            new RegExp(`function\\s+\\w+\\s*\\([^)]*\\b${genericName}\\b`, 'g'),
            new RegExp(`\\(\\s*${genericName}\\s*[,)]`, 'g'),
          ];
          
          patterns.forEach(pattern => {
            if (pattern.test(line)) {
              // Skip if it's a generic type parameter like <TData>
              if (!line.includes(`<${genericName}>`) && !line.includes(`T${genericName.charAt(0).toUpperCase()}`)) {
                issues.push({
                  file: file.replace(CONFIG.srcDir + '/', ''),
                  line: index + 1,
                  issue: `Generic variable name '${genericName}' should be more specific`,
                  code: line.trim(),
                });
              }
            }
          });
        });
      });
    } catch (error) {
      logStep(`Error reading file ${file}: ${error.message}`, 'error');
    }
  }
  
  if (issues.length === 0) {
    logStep('No generic variable names found ✓', 'success');
    return true;
  } else {
    logStep(`Found ${issues.length} generic variable name issues:`, 'warning');
    issues.forEach(issue => {
      log(`  ${issue.file}:${issue.line} - ${issue.issue}`, 'yellow');
      log(`    ${issue.code}`, 'reset');
    });
    return false;
  }
}

// Check for console statements
function checkConsoleStatements(files) {
  logStep('Checking for console statements...', 'info');
  
  let issues = [];
  
  for (const file of files) {
    try {
      const content = fs.readFileSync(file, 'utf8');
      const lines = content.split('\n');
      
      lines.forEach((line, index) => {
        if (line.includes('console.')) {
          // Check if it's properly wrapped in development check
          const hasDevCheck = content.includes("process.env.NODE_ENV === 'development'") ||
                             content.includes("NODE_ENV !== 'production'");
          
          if (!hasDevCheck && CONFIG.allowedConsoleInDev) {
            issues.push({
              file: file.replace(CONFIG.srcDir + '/', ''),
              line: index + 1,
              issue: 'Console statement not wrapped in development check',
              code: line.trim(),
            });
          }
        }
      });
    } catch (error) {
      logStep(`Error reading file ${file}: ${error.message}`, 'error');
    }
  }
  
  if (issues.length === 0) {
    logStep('Console statements are properly handled ✓', 'success');
    return true;
  } else {
    logStep(`Found ${issues.length} console statement issues:`, 'warning');
    issues.forEach(issue => {
      log(`  ${issue.file}:${issue.line} - ${issue.issue}`, 'yellow');
      log(`    ${issue.code}`, 'reset');
    });
    return false;
  }
}

// Run TypeScript compiler check
function runTypeScriptCheck() {
  logStep('Running TypeScript compiler check...', 'info');
  
  try {
    execSync('npx tsc --noEmit --skipLibCheck', { stdio: 'pipe' });
    logStep('TypeScript compilation successful ✓', 'success');
    return true;
  } catch (error) {
    logStep('TypeScript compilation failed:', 'error');
    log(error.stdout?.toString() || error.message, 'red');
    return false;
  }
}

// Run ESLint check
function runESLintCheck() {
  logStep('Running ESLint check...', 'info');
  
  try {
    execSync('npx eslint . --ext .ts,.tsx --max-warnings 0', { stdio: 'pipe' });
    logStep('ESLint check passed ✓', 'success');
    return true;
  } catch (error) {
    logStep('ESLint check failed:', 'error');
    log(error.stdout?.toString() || error.message, 'red');
    return false;
  }
}

// Main function
function main() {
  logHeader('CODE QUALITY CHECK');
  
  const files = getTypeScriptFiles(CONFIG.srcDir);
  log(`Found ${files.length} TypeScript files to check`, 'blue');
  
  const checks = [
    { name: 'TypeScript Configuration', fn: checkTypeScriptConfig },
    { name: 'ESLint Configuration', fn: checkESLintConfig },
    { name: 'TypeScript Compilation', fn: runTypeScriptCheck },
    { name: 'ESLint Rules', fn: runESLintCheck },
    { name: 'Generic Variable Names', fn: () => checkGenericVariableNames(files) },
    { name: 'Console Statements', fn: () => checkConsoleStatements(files) },
  ];
  
  let passedChecks = 0;
  const results = [];
  
  for (const check of checks) {
    logHeader(check.name);
    const passed = check.fn();
    results.push({ name: check.name, passed });
    if (passed) passedChecks++;
  }
  
  // Final summary
  logHeader('SUMMARY');
  log(`\n📊 Results: ${passedChecks}/${checks.length} checks passed\n`, 'bold');
  
  results.forEach(result => {
    const status = result.passed ? 'success' : 'error';
    logStep(`${result.name}: ${result.passed ? 'PASSED' : 'FAILED'}`, status);
  });
  
  if (passedChecks === checks.length) {
    log('\n🎉 ALL CODE QUALITY CHECKS PASSED!', 'green');
    log('✅ Code is production-ready with excellent quality standards', 'green');
    process.exit(0);
  } else {
    log('\n⚠️ SOME CODE QUALITY ISSUES FOUND', 'yellow');
    log('🔧 Please address the issues above before deployment', 'yellow');
    process.exit(1);
  }
}

// Run the script
if (require.main === module) {
  main();
}

module.exports = { main, checkTypeScriptConfig, checkESLintConfig, checkGenericVariableNames, checkConsoleStatements };
