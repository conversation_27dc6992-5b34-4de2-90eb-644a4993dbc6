/**
 * Dashboard Loading Component
 *
 * Shows loading skeleton while dashboard data is being fetched
 */
export default function DashboardLoading() {
  return (
    <div className='p-6'>
      <div className='animate-pulse'>
        {/* Header skeleton */}
        <div className='mb-8'>
          <div className='h-8 bg-gray-200 rounded w-1/4 mb-2' />
          <div className='h-4 bg-gray-200 rounded w-1/2' />
        </div>

        {/* Stats grid skeleton */}
        <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8'>
          {[...Array(4)].map((_, i) => (
            <div key={i} className='bg-white p-6 rounded-lg shadow-sm border border-gray-200'>
              <div className='flex items-center'>
                <div className='w-12 h-12 bg-gray-200 rounded-lg' />
                <div className='ml-4 flex-1'>
                  <div className='h-4 bg-gray-200 rounded w-3/4 mb-2' />
                  <div className='h-6 bg-gray-200 rounded w-1/2' />
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Performance metrics skeleton */}
        <div className='grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8'>
          {[...Array(2)].map((_, i) => (
            <div key={i} className='bg-white p-6 rounded-lg shadow-sm border border-gray-200'>
              <div className='h-6 bg-gray-200 rounded w-1/3 mb-4' />
              <div className='flex items-center'>
                <div className='flex-1'>
                  <div className='flex justify-between mb-1'>
                    <div className='h-4 bg-gray-200 rounded w-1/4' />
                    <div className='h-4 bg-gray-200 rounded w-1/6' />
                  </div>
                  <div className='w-full bg-gray-200 rounded-full h-2' />
                </div>
                <div className='ml-4 w-8 h-8 bg-gray-200 rounded' />
              </div>
            </div>
          ))}
        </div>

        {/* Quick actions and activities skeleton */}
        <div className='grid grid-cols-1 lg:grid-cols-2 gap-6'>
          {/* Quick actions skeleton */}
          <div className='bg-white p-6 rounded-lg shadow-sm border border-gray-200'>
            <div className='h-6 bg-gray-200 rounded w-1/3 mb-4' />
            <div className='grid grid-cols-2 gap-3'>
              {[...Array(4)].map((_, i) => (
                <div key={i} className='p-3 border border-gray-200 rounded-lg'>
                  <div className='w-6 h-6 bg-gray-200 rounded mb-1' />
                  <div className='h-4 bg-gray-200 rounded w-3/4' />
                </div>
              ))}
            </div>
          </div>

          {/* Recent activities skeleton */}
          <div className='bg-white p-6 rounded-lg shadow-sm border border-gray-200'>
            <div className='h-6 bg-gray-200 rounded w-1/3 mb-4' />
            <div className='space-y-3'>
              {[...Array(4)].map((_, i) => (
                <div key={i} className='flex items-start space-x-3'>
                  <div className='w-6 h-6 bg-gray-200 rounded' />
                  <div className='flex-1'>
                    <div className='h-4 bg-gray-200 rounded w-full mb-1' />
                    <div className='h-3 bg-gray-200 rounded w-1/4' />
                  </div>
                </div>
              ))}
            </div>
            <div className='mt-4 h-4 bg-gray-200 rounded w-1/3' />
          </div>
        </div>
      </div>
    </div>
  );
}
