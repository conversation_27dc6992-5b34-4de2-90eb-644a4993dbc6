/**
 * Hardened API Client with Deep Diagnostic Logging
 *
 * Features:
 * - Comprehensive request/response logging
 * - CSP/CORS/Network error detection
 * - Request/Response interceptors
 * - Error categorization and debugging
 * - Development-friendly configuration
 */

import axios, { AxiosError, AxiosResponse, InternalAxiosRequestConfig } from 'axios';
import { useAuthStore } from '../stores/authStore';

// API Configuration for Direct Backend Connection
const API_CONFIG = {
  baseURL: 'http://127.0.0.1:8000/api/v1', // ✅ Direct backend URL for development
  timeout: 30000, // 30 seconds
  retryAttempts: 3,
  retryDelay: 1000, // 1 second
};

// Create axios instance
const client = axios.create({
  baseURL: API_CONFIG.baseURL,
  timeout: API_CONFIG.timeout,
  headers: { 'Content-Type': 'application/json' },
  withCredentials: true, // Enable cookies for auth
  maxRedirects: 0, // Prevent automatic redirects to avoid losing Authorization headers
});

// Token management utilities
const getAuthToken = (): string | null => {
  if (typeof window === 'undefined') return null;

  // Get token from localStorage (primary method)
  const token = localStorage.getItem(process.env.NEXT_PUBLIC_AUTH_COOKIE || 'access_token');
  if (token) {
    console.log('🔑 Using token from localStorage');
    return token;
  }

  // Fallback: try to get from cookies
  const cookies = document.cookie.split(';');
  const tokenCookie = cookies.find(cookie =>
    cookie.trim().startsWith(`${process.env.NEXT_PUBLIC_AUTH_COOKIE || 'access_token'}=`)
  );
  if (tokenCookie) {
    console.log('🔑 Using token from cookie fallback');
    return tokenCookie.split('=')[1];
  }

  return null;
};

// Request interceptor with comprehensive logging and JWT auth
client.interceptors.request.use(
  (config: InternalAxiosRequestConfig) => {
    // Add JWT Bearer token for authentication
    const token = getAuthToken();
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
      console.log('🔐 Added Authorization header with JWT token');
    } else {
      console.log('⚠️ No JWT token found - request will be unauthenticated');
    }

    // Add request timestamp
    config.metadata = { startTime: Date.now() };

    // Comprehensive request logging
    console.log('[API ▶]', {
      method: config.method,
      url: config.url,
      fullUrl: config.baseURL + config.url,
      data: config.data,
      hasAuth: !!token,
    });

    return config;
  },
  (error: AxiosError) => {
    console.error('❌ Request Interceptor Error:', error);
    return Promise.reject(ApiError.fromAxiosError(error));
  }
);

// Response interceptor with enhanced error handling
client.interceptors.response.use(
  (response: AxiosResponse) => {
    // Calculate request duration
    const duration = Date.now() - (response.config.metadata?.startTime || 0);

    // Success response logging
    console.log('[API ✓]', {
      status: response.status,
      method: response.config.method?.toUpperCase(),
      url: response.config.url,
      fullUrl: `${response.config.baseURL}${response.config.url}`,
      duration: `${duration}ms`,
      dataSize: JSON.stringify(response.data).length,
      timestamp: new Date().toISOString(),
    });

    return response;
  },
  async (error: AxiosError) => {
    const originalRequest = error.config as InternalAxiosRequestConfig & { _retry?: boolean };
    const duration = Date.now() - (originalRequest?.metadata?.startTime || 0);

    // Enhanced error logging with CSP/CORS detection
    const errorDetails = {
      method: originalRequest?.method?.toUpperCase(),
      url: originalRequest?.url,
      fullUrl: `${originalRequest?.baseURL || ''}${originalRequest?.url || ''}`,
      status: error.response?.status,
      statusText: error.response?.statusText,
      responseData: error.response?.data,
      message: error.message,
      duration: `${duration}ms`,
      isCspOrNetwork: !error.response, // true when CSP/CORS/network blocked
      timestamp: new Date().toISOString(),
    };

    // Categorize and log errors
    if (!error.response) {
      // Network/CSP/CORS error
      console.error('[API ✗] NETWORK/CSP/CORS BLOCK', errorDetails);
      console.error('🔍 Troubleshooting: Check CSP headers, CORS config, and network connectivity');
    } else if (error.response.status === 401) {
      // Authentication error - proxy already cleared cookie
      console.error('[API ✗] AUTHENTICATION ERROR', errorDetails);

      // Clear client-side auth state and redirect
      if (typeof window !== 'undefined') {
        useAuthStore.getState().logout();
        if (!window.location.pathname.includes('/login')) {
          window.location.href = '/login';
        }
      }
    } else {
      // Other HTTP errors
      console.error('[API ✗] HTTP ERROR', errorDetails);
    }

    // Handle network errors with retry logic
    if (!error.response && originalRequest && !originalRequest._retry) {
      return retryRequest(client, originalRequest, error);
    }

    return Promise.reject(ApiError.fromAxiosError(error));
  }
);

// Utility functions
// getAuthToken is imported from authStore, no need to redefine

async function retryRequest(client: any, config: any, error: any, retryCount = 0): Promise<any> {
  if (retryCount >= API_CONFIG.retryAttempts) {
    return Promise.reject(error);
  }

  await new Promise(resolve => setTimeout(resolve, API_CONFIG.retryDelay * (retryCount + 1)));

  try {
    return await client(config);
  } catch (retryError) {
    return retryRequest(client, config, retryError, retryCount + 1);
  }
}

// API Error class
class ApiError extends Error {
  public status?: number;
  public statusText?: string;
  public responseData?: any;
  public isCspOrNetwork: boolean;

  constructor(
    message: string,
    status?: number,
    statusText?: string,
    responseData?: any,
    isCspOrNetwork = false
  ) {
    super(message);
    this.name = 'ApiError';
    this.status = status;
    this.statusText = statusText;
    this.responseData = responseData;
    this.isCspOrNetwork = isCspOrNetwork;
  }

  static fromAxiosError(error: AxiosError): ApiError {
    return new ApiError(
      error.message,
      error.response?.status,
      error.response?.statusText,
      error.response?.data,
      !error.response
    );
  }
}

// Export the client as 'api' for direct usage
export const api = client;

// Export default for backward compatibility
export default client;
