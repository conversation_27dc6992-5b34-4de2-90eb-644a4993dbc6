/**
 * Settings API Adapter
 * 
 * API layer for settings management with:
 * - CRUD operations for all settings tabs
 * - File upload handling
 * - Mock data for development
 * - Type-safe API calls
 * - Error handling
 */

import apiService from '@/api/apiService';
import type {
  SchoolProfile,
  UsersRoles,
  Fees,
  AcademicStructure,
  ExamTerms,
  Notifications,
  IDCardNote,
  Sessions,
  SettingsData,
} from '../schemas/settings.schemas';

// API endpoints
const ENDPOINTS = {
  SCHOOL_PROFILE: '/api/settings/school-profile',
  USERS_ROLES: '/api/settings/users-roles',
  FEES: '/api/settings/fees',
  ACADEMIC_STRUCTURE: '/api/settings/academic-structure',
  EXAM_TERMS: '/api/settings/exam-terms',
  NOTIFICATIONS: '/api/settings/notifications',
  ID_CARD_NOTE: '/api/settings/id-card-note',
  SESSIONS: '/api/settings/sessions',
  UPLOAD: '/api/settings/upload',
} as const;

// Mock data for development
const MOCK_SETTINGS: SettingsData = {
  schoolProfile: {
    name: 'Demo High School',
    address: '123 Education Street',
    city: 'Learning City',
    state: 'Knowledge State',
    postal_code: '12345',
    country: 'Education Country',
    phone: '******-0123',
    email: '<EMAIL>',
    website: 'https://demohighschool.edu',
    established_year: 1995,
    principal_name: 'Dr. Jane Smith',
    description: 'A premier educational institution committed to excellence in learning and character development.',
    motto: 'Excellence in Education, Character in Life',
  },
  usersRoles: {
    default_student_role: 'STUDENT',
    default_teacher_role: 'TEACHER',
    default_parent_role: 'PARENT',
    custom_roles: [
      {
        role_name: 'DEPARTMENT_HEAD',
        permissions: ['VIEW_REPORTS', 'MANAGE_TEACHERS', 'APPROVE_CURRICULUM'],
        description: 'Department head with administrative privileges',
      },
    ],
    max_login_attempts: 5,
    session_timeout: 120,
    password_policy: {
      min_length: 8,
      require_uppercase: true,
      require_lowercase: true,
      require_numbers: true,
      require_symbols: false,
      password_expiry_days: 90,
    },
  },
  fees: {
    currency: 'USD',
    fee_structures: [
      {
        name: 'Tuition Fee',
        amount: 5000,
        frequency: 'ANNUALLY',
        due_date: '2024-08-15',
        late_fee: 50,
        grace_period_days: 7,
      },
    ],
    payment_methods: ['ONLINE', 'BANK_TRANSFER', 'CASH'],
    late_fee_policy: {
      enabled: true,
      percentage: 5,
    },
    discount_policy: {
      sibling_discount: 10,
      early_payment_discount: 5,
      scholarship_enabled: true,
    },
  },
  academicStructure: {
    academic_year_start: '2024-08-01',
    academic_year_end: '2025-07-31',
    grade_levels: [
      {
        name: 'Grade 9',
        level: 9,
        age_range: { min: 14, max: 15 },
        subjects: ['Mathematics', 'English', 'Science', 'History'],
      },
    ],
    class_duration_minutes: 45,
    break_duration_minutes: 15,
    working_days: ['MONDAY', 'TUESDAY', 'WEDNESDAY', 'THURSDAY', 'FRIDAY'],
    school_start_time: '08:00',
    school_end_time: '15:00',
    terms_per_year: 3,
  },
  examTerms: {
    terms: [
      {
        name: 'First Term',
        start_date: '2024-08-01',
        end_date: '2024-12-15',
        exam_start_date: '2024-12-01',
        exam_end_date: '2024-12-15',
        result_publish_date: '2024-12-20',
        weightage: 40,
      },
    ],
    grading_system: {
      type: 'PERCENTAGE',
      passing_marks: 50,
      grade_ranges: [
        { grade: 'A+', min_marks: 90, max_marks: 100 },
        { grade: 'A', min_marks: 80, max_marks: 89 },
        { grade: 'B', min_marks: 70, max_marks: 79 },
        { grade: 'C', min_marks: 60, max_marks: 69 },
        { grade: 'D', min_marks: 50, max_marks: 59 },
        { grade: 'F', min_marks: 0, max_marks: 49 },
      ],
    },
    exam_rules: {
      allow_reexam: true,
      max_reexam_attempts: 2,
      attendance_requirement: 75,
    },
  },
  notifications: {
    channels: [
      { type: 'EMAIL', enabled: true },
      { type: 'SMS', enabled: false },
      { type: 'PUSH', enabled: true },
      { type: 'IN_APP', enabled: true },
    ],
    email_settings: {
      smtp_host: 'smtp.gmail.com',
      smtp_port: 587,
      smtp_username: '<EMAIL>',
      smtp_password: '********',
      from_email: '<EMAIL>',
      from_name: 'Demo High School',
    },
    sms_settings: {
      provider: 'TWILIO',
      api_key: '********',
      sender_id: 'DemoSchool',
    },
    notification_types: [
      {
        type: 'ATTENDANCE_ALERT',
        enabled: true,
        channels: ['EMAIL', 'SMS'],
        template: 'Your child was absent today.',
      },
    ],
  },
  idCardNote: {
    note_text: 'This card must be carried at all times on school premises.',
    show_on_student_card: true,
    show_on_teacher_card: true,
    show_on_staff_card: true,
    font_size: 'SMALL',
    position: 'BOTTOM',
    required_fields: ['PHOTO', 'NAME', 'ID', 'CLASS'],
    card_template: 'TEMPLATE_1',
    background_color: '#ffffff',
    text_color: '#000000',
  },
  sessions: {
    sessions: [
      {
        name: '2024-2025',
        start_date: '2024-08-01',
        end_date: '2025-07-31',
        is_current: true,
        description: 'Current academic session',
      },
    ],
    auto_promote_students: false,
    session_transition_date: '2025-07-31',
    archive_old_sessions: true,
    archive_after_years: 5,
  },
};

// Settings API functions
export const settingsApi = {
  // Get all settings
  getAllSettings: async (): Promise<SettingsData> => {
    try {
      const response = await apiService.get('/api/settings');
      return response.data;
    } catch (error) {
      console.warn('Using mock settings data');
      return MOCK_SETTINGS;
    }
  },

  // School Profile
  getSchoolProfile: async (): Promise<SchoolProfile> => {
    try {
      const response = await apiService.get(ENDPOINTS.SCHOOL_PROFILE);
      return response.data;
    } catch (error) {
      console.warn('Using mock school profile data');
      return MOCK_SETTINGS.schoolProfile;
    }
  },

  updateSchoolProfile: async (data: Partial<SchoolProfile>): Promise<SchoolProfile> => {
    try {
      const response = await apiService.put(ENDPOINTS.SCHOOL_PROFILE, data);
      return response.data;
    } catch (error) {
      console.warn('Mock update - returning updated data');
      return { ...MOCK_SETTINGS.schoolProfile, ...data };
    }
  },

  // Users & Roles
  getUsersRoles: async (): Promise<UsersRoles> => {
    try {
      const response = await apiService.get(ENDPOINTS.USERS_ROLES);
      return response.data;
    } catch (error) {
      console.warn('Using mock users roles data');
      return MOCK_SETTINGS.usersRoles;
    }
  },

  updateUsersRoles: async (data: Partial<UsersRoles>): Promise<UsersRoles> => {
    try {
      const response = await apiService.put(ENDPOINTS.USERS_ROLES, data);
      return response.data;
    } catch (error) {
      console.warn('Mock update - returning updated data');
      return { ...MOCK_SETTINGS.usersRoles, ...data };
    }
  },

  // Fees & Billing
  getFees: async (): Promise<Fees> => {
    try {
      const response = await apiService.get(ENDPOINTS.FEES);
      return response.data;
    } catch (error) {
      console.warn('Using mock fees data');
      return MOCK_SETTINGS.fees;
    }
  },

  updateFees: async (data: Partial<Fees>): Promise<Fees> => {
    try {
      const response = await apiService.put(ENDPOINTS.FEES, data);
      return response.data;
    } catch (error) {
      console.warn('Mock update - returning updated data');
      return { ...MOCK_SETTINGS.fees, ...data };
    }
  },

  // Academic Structure
  getAcademicStructure: async (): Promise<AcademicStructure> => {
    try {
      const response = await apiService.get(ENDPOINTS.ACADEMIC_STRUCTURE);
      return response.data;
    } catch (error) {
      console.warn('Using mock academic structure data');
      return MOCK_SETTINGS.academicStructure;
    }
  },

  updateAcademicStructure: async (data: Partial<AcademicStructure>): Promise<AcademicStructure> => {
    try {
      const response = await apiService.put(ENDPOINTS.ACADEMIC_STRUCTURE, data);
      return response.data;
    } catch (error) {
      console.warn('Mock update - returning updated data');
      return { ...MOCK_SETTINGS.academicStructure, ...data };
    }
  },

  // File upload
  uploadFile: async (file: File, type: string): Promise<{ url: string; filename: string }> => {
    try {
      const formData = new FormData();
      formData.append('file', file);
      formData.append('type', type);

      const response = await apiService.post(ENDPOINTS.UPLOAD, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });
      return response.data;
    } catch (error) {
      console.warn('Mock file upload');
      return {
        url: URL.createObjectURL(file),
        filename: file.name,
      };
    }
  },
};
