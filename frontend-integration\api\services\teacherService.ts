/**
 * Teacher Service - Production-Grade FastAPI Integration
 *
 * Handles all teacher-related API calls with:
 * - Real FastAPI backend integration (/api/v1/teachers)
 * - JWT Bearer authentication
 * - Proper error handling and data transformation
 * - Dummy data fallback for development
 * - CRUD operations with optimistic updates
 * - Filtering, pagination, and statistics
 */

import {
  createMockTeacher,
  deleteMockTeacher,
  filterMockTeachers,
  getMockTeacherById,
  mockTeachers,
  paginateMockTeachers,
  updateMockTeacher,
} from '@/lib/mockTeachers';
import { Teacher, TeacherCreate, TeacherUpdate } from '@/schemas/zodSchemas';
import type { PaginatedResponse } from '@/types';
import { api as apiClient } from '../apiClient';

// Configuration - Use real API by default, fallback to dummy data in development
const USE_DUMMY_DATA = process.env.NEXT_PUBLIC_USE_DUMMY_DATA === 'true' || false;

// FastAPI endpoints - Normalized for FastAPI routing conventions
const ENDPOINTS = {
  teachers: '/teachers/', // Collection endpoint - keep trailing slash
  teacher: (id: string) => `/teachers/${id}`, // Individual resource - no trailing slash
  teacherStats: '/teachers/stats', // Sub-route - no trailing slash
  teacherSearch: '/teachers/search', // Sub-route - no trailing slash
  teacherBulk: '/teachers/bulk', // Sub-route - no trailing slash
} as const;

// Types for API parameters
export interface TeacherFilters {
  search?: string;
  department?: string;
  status?: 'ACTIVE' | 'INACTIVE';
  page?: number;
  pageSize?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export interface TeacherStats {
  total: number;
  active: number;
  inactive: number;
  departments: number;
  averageExperience: number;
}

// Teacher Service Functions
export const teacherService = {
  // Get all teachers with filtering and pagination
  async getTeachers(filters: TeacherFilters = {}): Promise<PaginatedResponse<Teacher>> {
    if (USE_DUMMY_DATA) {
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 800));

      // Apply filters to mock data
      const filterParams: {
        search?: string;
        department?: string;
        status?: string;
        subject?: string;
      } = {};

      if (filters.search) {
        filterParams.search = filters.search;
      }
      if (filters.department) {
        filterParams.department = filters.department;
      }
      if (filters.status) {
        filterParams.status = filters.status;
      }

      const filteredTeachers = filterMockTeachers(filterParams);

      // Apply pagination
      const paginatedTeachers = paginateMockTeachers(
        filteredTeachers,
        filters.page || 1,
        filters.pageSize || 12
      );

      return paginatedTeachers;
    }

    // Real API call with proper error handling
    try {
      console.log(`🔗 TeacherService.getTeachers calling: ${ENDPOINTS.teachers}`);
      const response = await apiClient.get<any>(ENDPOINTS.teachers, {
        params: filters,
      });

      // Transform backend response to frontend format if needed
      const transformedData = {
        data: response.data.data || response.data,
        total: response.data.total || response.data.length || 0,
        page: response.data.page || filters.page || 1,
        pageSize: response.data.pageSize || filters.pageSize || 12,
        totalPages:
          response.data.totalPages ||
          Math.ceil((response.data.total || 0) / (filters.pageSize || 12)),
      };

      console.log(`✅ TeacherService.getTeachers success:`, transformedData);
      return transformedData;
    } catch (error: any) {
      console.error(`❌ TeacherService.getTeachers error:`, error);
      throw new Error(error.response?.data?.detail || error.message || 'Failed to fetch teachers');
    }
  },

  // Get single teacher by ID
  async getTeacher(id: string): Promise<Teacher> {
    if (USE_DUMMY_DATA) {
      await new Promise(resolve => setTimeout(resolve, 500));

      const teacher = getMockTeacherById(id);
      if (!teacher) {
        throw new Error(`Teacher with ID ${id} not found`);
      }

      return teacher;
    }

    try {
      console.log(`🔗 TeacherService.getTeacher calling: ${ENDPOINTS.teacher(id)}`);
      const response = await apiClient.get<any>(ENDPOINTS.teacher(id));

      // Transform backend response to frontend format if needed
      const teacher = response.data;
      console.log(`✅ TeacherService.getTeacher success:`, teacher);
      return teacher;
    } catch (error: any) {
      console.error(`❌ TeacherService.getTeacher error:`, error);
      if (error.response?.status === 404) {
        throw new Error(`Teacher with ID ${id} not found`);
      }
      throw new Error(error.response?.data?.detail || error.message || 'Failed to fetch teacher');
    }
  },

  // Create new teacher
  async createTeacher(teacherData: TeacherCreate): Promise<Teacher> {
    if (USE_DUMMY_DATA) {
      await new Promise(resolve => setTimeout(resolve, 1000));

      const newTeacher = createMockTeacher(teacherData);
      return newTeacher;
    }

    try {
      console.log(`🔗 TeacherService.createTeacher calling: ${ENDPOINTS.teachers}`);
      console.log(`📤 TeacherService.createTeacher data:`, teacherData);

      const response = await apiClient.post<any>(ENDPOINTS.teachers, teacherData);
      const newTeacher = response.data;

      console.log(`✅ TeacherService.createTeacher success:`, newTeacher);
      return newTeacher;
    } catch (error: any) {
      console.error(`❌ TeacherService.createTeacher error:`, error);
      throw new Error(error.response?.data?.detail || error.message || 'Failed to create teacher');
    }
  },

  // Update existing teacher
  async updateTeacher(id: string, teacherData: TeacherUpdate): Promise<Teacher> {
    if (USE_DUMMY_DATA) {
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Filter out undefined values to satisfy exactOptionalPropertyTypes
      const filteredData: Partial<Teacher> = {};
      Object.entries(teacherData).forEach(([key, value]) => {
        if (value !== undefined) {
          (filteredData as any)[key] = value;
        }
      });

      const updatedTeacher = updateMockTeacher(id, filteredData);
      if (!updatedTeacher) {
        throw new Error(`Teacher with ID ${id} not found`);
      }

      return updatedTeacher;
    }

    try {
      console.log(`🔗 TeacherService.updateTeacher calling: ${ENDPOINTS.teacher(id)}`);
      console.log(`📤 TeacherService.updateTeacher data:`, teacherData);

      const response = await apiClient.put<any>(ENDPOINTS.teacher(id), teacherData);
      const updatedTeacher = response.data;

      console.log(`✅ TeacherService.updateTeacher success:`, updatedTeacher);
      return updatedTeacher;
    } catch (error: any) {
      console.error(`❌ TeacherService.updateTeacher error:`, error);
      if (error.response?.status === 404) {
        throw new Error(`Teacher with ID ${id} not found`);
      }
      throw new Error(error.response?.data?.detail || error.message || 'Failed to update teacher');
    }
  },

  // Delete teacher
  async deleteTeacher(id: string): Promise<{ success: boolean }> {
    if (USE_DUMMY_DATA) {
      await new Promise(resolve => setTimeout(resolve, 800));

      const deleteSuccess = deleteMockTeacher(id);
      if (!deleteSuccess) {
        throw new Error(`Teacher with ID ${id} not found`);
      }

      return { success: true };
    }

    try {
      console.log(`🔗 TeacherService.deleteTeacher calling: ${ENDPOINTS.teacher(id)}`);

      await apiClient.delete(ENDPOINTS.teacher(id));

      console.log(`✅ TeacherService.deleteTeacher success for ID: ${id}`);
      return { success: true };
    } catch (error: any) {
      console.error(`❌ TeacherService.deleteTeacher error:`, error);
      if (error.response?.status === 404) {
        throw new Error(`Teacher with ID ${id} not found`);
      }
      throw new Error(error.response?.data?.detail || error.message || 'Failed to delete teacher');
    }
  },

  // Get teacher statistics
  async getTeacherStats(): Promise<TeacherStats> {
    if (USE_DUMMY_DATA) {
      await new Promise(resolve => setTimeout(resolve, 300));

      const allTeachers = mockTeachers;
      const activeTeachers = allTeachers.filter(teacher => teacher.status === 'ACTIVE');
      const departmentCount = new Set(allTeachers.map(teacher => teacher.department)).size;

      // Calculate average experience (years since hire date)
      const currentYear = new Date().getFullYear();
      const totalExperience = allTeachers.reduce((sum, teacher) => {
        if (!teacher.hire_date) return sum;
        const hireYear = new Date(teacher.hire_date).getFullYear();
        return sum + (currentYear - hireYear);
      }, 0);

      return {
        total: allTeachers.length,
        active: activeTeachers.length,
        inactive: allTeachers.length - activeTeachers.length,
        departments: departmentCount,
        averageExperience: Math.round(totalExperience / allTeachers.length),
      };
    }

    try {
      console.log(`🔗 TeacherService.getTeacherStats calling: ${ENDPOINTS.teacherStats}`);

      const response = await apiClient.get<any>(ENDPOINTS.teacherStats);
      const stats = response.data;

      console.log(`✅ TeacherService.getTeacherStats success:`, stats);
      return stats;
    } catch (error: any) {
      console.error(`❌ TeacherService.getTeacherStats error:`, error);
      throw new Error(
        error.response?.data?.detail || error.message || 'Failed to fetch teacher statistics'
      );
    }
  },

  // Bulk operations
  async bulkUpdateTeachers(
    updates: Array<{ id: string; data: TeacherUpdate }>
  ): Promise<Teacher[]> {
    if (USE_DUMMY_DATA) {
      await new Promise(resolve => setTimeout(resolve, 2000));

      const updatedTeachers = updates.map(({ id, data: teacherUpdateData }) => {
        // Filter out undefined values to satisfy exactOptionalPropertyTypes
        const filteredData: Partial<Teacher> = {};
        Object.entries(teacherUpdateData).forEach(([key, value]) => {
          if (value !== undefined) {
            (filteredData as any)[key] = value;
          }
        });

        const updatedTeacher = updateMockTeacher(id, filteredData);
        if (!updatedTeacher) {
          throw new Error(`Teacher with ID ${id} not found`);
        }
        return updatedTeacher;
      });

      return updatedTeachers;
    }

    const bulkUpdateResponse = await apiClient.put<Teacher[]>('/teachers/bulk', { updates });
    return bulkUpdateResponse.data;
  },

  async bulkDeleteTeachers(ids: string[]): Promise<{ success: boolean; deleted: number }> {
    if (USE_DUMMY_DATA) {
      await new Promise(resolve => setTimeout(resolve, 1500));

      let deletedCount = 0;
      ids.forEach(teacherId => {
        if (deleteMockTeacher(teacherId)) {
          deletedCount++;
        }
      });

      return { success: true, deleted: deletedCount };
    }

    const bulkDeleteResponse = await apiClient.delete<{ success: boolean; deleted: number }>(
      '/teachers/bulk',
      {
        data: { ids },
      }
    );
    return bulkDeleteResponse.data;
  },

  // Search teachers
  async searchTeachers(query: string, limit = 10): Promise<Teacher[]> {
    if (USE_DUMMY_DATA) {
      await new Promise(resolve => setTimeout(resolve, 300));

      const filteredTeachers = filterMockTeachers(query ? { search: query } : {});
      return filteredTeachers.slice(0, limit);
    }

    const searchResponse = await apiClient.get<Teacher[]>('/teachers/search', {
      params: { q: query, limit },
    });
    return searchResponse.data;
  },

  // Get teachers by department
  async getTeachersByDepartment(department: string): Promise<Teacher[]> {
    if (USE_DUMMY_DATA) {
      await new Promise(resolve => setTimeout(resolve, 400));

      return filterMockTeachers(department ? { department } : {});
    }

    const departmentResponse = await apiClient.get<Teacher[]>('/teachers/by-department', {
      params: { department },
    });
    return departmentResponse.data;
  },
};

// Export individual functions for convenience
export const {
  getTeachers,
  getTeacher,
  createTeacher,
  updateTeacher,
  deleteTeacher,
  getTeacherStats,
  bulkUpdateTeachers,
  bulkDeleteTeachers,
  searchTeachers,
  getTeachersByDepartment,
} = teacherService;
