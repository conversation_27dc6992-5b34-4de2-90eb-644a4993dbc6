/**
 * Test Teachers Endpoints
 * 
 * Tests different teachers endpoints to isolate the 500 error issue
 */

const API_URL = 'http://127.0.0.1:8000';

async function testEndpoint(path, description) {
  console.log(`\n🧪 Testing ${description}`);
  console.log(`   URL: ${API_URL}${path}`);
  
  try {
    const response = await fetch(`${API_URL}${path}`);
    
    console.log(`   Status: ${response.status} ${response.statusText}`);
    
    if (response.ok) {
      const data = await response.json();
      console.log(`   ✅ Success: ${JSON.stringify(data).substring(0, 100)}...`);
      return { success: true, data, status: response.status };
    } else {
      const errorText = await response.text();
      console.log(`   ❌ Error: ${errorText.substring(0, 200)}...`);
      return { success: false, error: errorText, status: response.status };
    }
  } catch (error) {
    console.log(`   💥 Network Error: ${error.message}`);
    return { success: false, error: error.message, status: 'NETWORK_ERROR' };
  }
}

async function runTests() {
  console.log('🔍 Testing Teachers Endpoints');
  console.log('=' * 50);
  
  const tests = [
    // Working endpoints (for comparison)
    { path: '/health', desc: 'Health Check (working)' },
    { path: '/dev/test-db', desc: 'Database Test (working)' },
    
    // Problem endpoint
    { path: '/api/v1/teachers/', desc: 'Teachers Main (500 error)' },
    
    // Test endpoints (add these to your backend)
    { path: '/api/v1/teachers/test', desc: 'Teachers Test (minimal)' },
    { path: '/api/v1/teachers/count', desc: 'Teachers Count (database)' },
    { path: '/api/v1/teachers/first', desc: 'Teachers First Record' },
    { path: '/api/v1/teachers/minimal', desc: 'Teachers Minimal (mock)' },
    { path: '/api/v1/debug/teachers-query', desc: 'Teachers Query Debug' },
    
    // Alternative paths to test
    { path: '/api/v1/teachers', desc: 'Teachers (no trailing slash)' },
    { path: '/teachers/', desc: 'Teachers (no api prefix)' },
    { path: '/teachers', desc: 'Teachers (no prefix, no slash)' }
  ];
  
  const results = [];
  
  for (const test of tests) {
    const result = await testEndpoint(test.path, test.desc);
    results.push({ ...test, ...result });
    
    // Small delay between requests
    await new Promise(resolve => setTimeout(resolve, 200));
  }
  
  // Summary
  console.log('\n📋 Test Results Summary');
  console.log('=' * 30);
  
  const working = results.filter(r => r.success);
  const failing = results.filter(r => !r.success);
  
  console.log(`✅ Working endpoints: ${working.length}`);
  working.forEach(r => console.log(`   - ${r.desc}: ${r.status}`));
  
  console.log(`\n❌ Failing endpoints: ${failing.length}`);
  failing.forEach(r => console.log(`   - ${r.desc}: ${r.status} (${r.error?.substring(0, 50)}...)`));
  
  // Analysis
  console.log('\n🔍 Analysis');
  console.log('-' * 20);
  
  if (working.length > 0) {
    console.log('✅ Server is responding to some endpoints');
  }
  
  const teachersMain = results.find(r => r.path === '/api/v1/teachers/');
  if (teachersMain && !teachersMain.success) {
    console.log('❌ Main teachers endpoint is failing');
    
    if (teachersMain.status === 500) {
      console.log('💡 500 error suggests backend code issue, not connectivity');
      console.log('   - Check your teachers endpoint implementation');
      console.log('   - Look for database query errors');
      console.log('   - Verify column names and data types');
    }
  }
  
  const testEndpoints = results.filter(r => r.path.includes('/test') || r.path.includes('/minimal'));
  const workingTests = testEndpoints.filter(r => r.success);
  
  if (workingTests.length > 0) {
    console.log('✅ Test endpoints working - issue is in main endpoint logic');
  } else if (testEndpoints.length > 0) {
    console.log('❌ Test endpoints also failing - broader backend issue');
  } else {
    console.log('⚠️ Test endpoints not found - add them to your backend');
  }
  
  console.log('\n🛠️ Next Steps');
  console.log('-' * 15);
  console.log('1. Add the debug endpoints to your FastAPI backend');
  console.log('2. Check your backend server logs for detailed error messages');
  console.log('3. Test the specific database query causing the issue');
  console.log('4. Verify column names match your database schema');
  
  return results;
}

// Run the tests
runTests().catch(console.error);
