'use client';

import { useEffect } from 'react';

/**
 * Dashboard Error Boundary
 *
 * Handles errors that occur on the dashboard page
 */
interface DashboardErrorProps {
  error: Error & { digest?: string };
  reset: () => void;
}

export default function DashboardError({ error, reset }: DashboardErrorProps) {
  useEffect(() => {
    console.error('Dashboard error:', error);
  }, [error]);

  return (
    <div className='p-6'>
      <div className='max-w-md mx-auto text-center'>
        {/* Error icon */}
        <div className='mx-auto h-16 w-16 bg-red-100 rounded-full flex items-center justify-center mb-4'>
          <span className='text-red-600 text-3xl'>⚠️</span>
        </div>

        {/* Error message */}
        <h2 className='text-2xl font-bold text-gray-800 mb-4'>Dashboard Error</h2>

        <p className='text-gray-600 mb-6'>
          We encountered an error while loading your dashboard. This might be a temporary issue.
        </p>

        {/* Error details (development only) */}
        {process.env.NODE_ENV === 'development' && (
          <div className='bg-red-50 border border-red-200 rounded-lg p-4 mb-6 text-left'>
            <h3 className='font-semibold text-red-800 mb-2'>Error Details:</h3>
            <p className='text-sm text-red-700 font-mono break-all'>{error.message}</p>
            {error.digest && <p className='text-xs text-red-600 mt-2'>Error ID: {error.digest}</p>}
          </div>
        )}

        {/* Action buttons */}
        <div className='flex flex-col sm:flex-row gap-3 justify-center'>
          <button
            onClick={reset}
            className='px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors'
          >
            Try Again
          </button>

          <button
            onClick={() => window.location.reload()}
            className='px-6 py-3 bg-gray-200 text-gray-800 rounded-lg hover:bg-gray-300 transition-colors'
          >
            Refresh Page
          </button>
        </div>

        {/* Help text */}
        <p className='text-sm text-gray-500 mt-6'>
          If the problem persists, please contact your system administrator.
        </p>
      </div>
    </div>
  );
}
