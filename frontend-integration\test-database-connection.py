#!/usr/bin/env python3
"""
Database Connection Test Script

Run this script to test your database connection independently of FastAPI.
This helps isolate whether the issue is with the database or the API endpoint.

Usage: python test-database-connection.py
"""

import os
import sys
import traceback
from datetime import datetime

def test_environment_variables():
    """Check if required environment variables are set"""
    print("🔍 Checking environment variables...")
    
    # Common database environment variable names
    db_vars = [
        "DATABASE_URL",
        "DB_HOST", "DB_PORT", "DB_NAME", "DB_USER", "DB_PASSWORD",
        "POSTGRES_URL", "POSTGRESQL_URL",
        "MYSQL_URL", "SQLITE_URL"
    ]
    
    found_vars = {}
    for var in db_vars:
        value = os.getenv(var)
        if value:
            # Hide sensitive info
            if "password" in var.lower() or "url" in var.lower():
                display_value = value[:10] + "..." + value[-5:] if len(value) > 15 else "***"
            else:
                display_value = value
            found_vars[var] = display_value
            print(f"  ✅ {var}: {display_value}")
    
    if not found_vars:
        print("  ❌ No database environment variables found!")
        print("  💡 Make sure to set DATABASE_URL or DB_* variables")
        return False
    
    return True

def test_postgresql_connection():
    """Test PostgreSQL connection"""
    print("\n🐘 Testing PostgreSQL connection...")
    
    try:
        import psycopg2
        from psycopg2 import sql
        
        # Try different connection methods
        connection_methods = [
            ("DATABASE_URL", os.getenv("DATABASE_URL")),
            ("Individual vars", {
                "host": os.getenv("DB_HOST", "localhost"),
                "port": os.getenv("DB_PORT", "5432"),
                "database": os.getenv("DB_NAME"),
                "user": os.getenv("DB_USER"),
                "password": os.getenv("DB_PASSWORD")
            })
        ]
        
        for method_name, connection_info in connection_methods:
            if not connection_info:
                continue
                
            print(f"  Trying {method_name}...")
            
            try:
                if isinstance(connection_info, str):
                    # DATABASE_URL format
                    conn = psycopg2.connect(connection_info)
                else:
                    # Individual parameters
                    conn = psycopg2.connect(**connection_info)
                
                cursor = conn.cursor()
                
                # Test basic query
                cursor.execute("SELECT version();")
                version = cursor.fetchone()[0]
                print(f"  ✅ Connected! PostgreSQL version: {version[:50]}...")
                
                # Test teachers table
                try:
                    cursor.execute("SELECT COUNT(*) FROM teachers;")
                    count = cursor.fetchone()[0]
                    print(f"  ✅ Teachers table found with {count} records")
                except Exception as e:
                    print(f"  ⚠️ Teachers table issue: {e}")
                    
                    # Try to list tables
                    try:
                        cursor.execute("""
                            SELECT table_name 
                            FROM information_schema.tables 
                            WHERE table_schema = 'public'
                        """)
                        tables = cursor.fetchall()
                        print(f"  📋 Available tables: {[t[0] for t in tables]}")
                    except Exception as list_error:
                        print(f"  ❌ Could not list tables: {list_error}")
                
                cursor.close()
                conn.close()
                return True
                
            except Exception as e:
                print(f"  ❌ {method_name} failed: {e}")
                continue
        
        print("  ❌ All PostgreSQL connection methods failed")
        return False
        
    except ImportError:
        print("  ⚠️ psycopg2 not installed. Install with: pip install psycopg2-binary")
        return False

def test_sqlite_connection():
    """Test SQLite connection"""
    print("\n💾 Testing SQLite connection...")
    
    try:
        import sqlite3
        
        # Common SQLite database locations
        db_paths = [
            os.getenv("SQLITE_URL", "").replace("sqlite:///", ""),
            "database.db",
            "app.db", 
            "school.db",
            "teachers.db"
        ]
        
        for db_path in db_paths:
            if not db_path or not os.path.exists(db_path):
                continue
                
            print(f"  Trying {db_path}...")
            
            try:
                conn = sqlite3.connect(db_path)
                cursor = conn.cursor()
                
                # Test basic query
                cursor.execute("SELECT sqlite_version();")
                version = cursor.fetchone()[0]
                print(f"  ✅ Connected! SQLite version: {version}")
                
                # Test teachers table
                try:
                    cursor.execute("SELECT COUNT(*) FROM teachers;")
                    count = cursor.fetchone()[0]
                    print(f"  ✅ Teachers table found with {count} records")
                except Exception as e:
                    print(f"  ⚠️ Teachers table issue: {e}")
                    
                    # List tables
                    try:
                        cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
                        tables = cursor.fetchall()
                        print(f"  📋 Available tables: {[t[0] for t in tables]}")
                    except Exception as list_error:
                        print(f"  ❌ Could not list tables: {list_error}")
                
                cursor.close()
                conn.close()
                return True
                
            except Exception as e:
                print(f"  ❌ SQLite connection failed: {e}")
                continue
        
        print("  ❌ No SQLite databases found")
        return False
        
    except ImportError:
        print("  ⚠️ sqlite3 not available")
        return False

def main():
    """Main test function"""
    print("🔧 Database Connection Test")
    print("=" * 50)
    print(f"Timestamp: {datetime.now()}")
    print(f"Python version: {sys.version}")
    print()
    
    # Test 1: Environment variables
    env_ok = test_environment_variables()
    
    # Test 2: Database connections
    postgres_ok = test_postgresql_connection()
    sqlite_ok = test_sqlite_connection()
    
    # Summary
    print("\n📋 Test Summary")
    print("=" * 30)
    print(f"Environment variables: {'✅' if env_ok else '❌'}")
    print(f"PostgreSQL connection: {'✅' if postgres_ok else '❌'}")
    print(f"SQLite connection: {'✅' if sqlite_ok else '❌'}")
    
    if postgres_ok or sqlite_ok:
        print("\n🎉 Database connection successful!")
        print("💡 The issue might be in your FastAPI endpoint code.")
        print("   Check your teachers endpoint implementation.")
    else:
        print("\n❌ Database connection failed!")
        print("💡 Possible solutions:")
        print("   1. Check your database server is running")
        print("   2. Verify connection credentials")
        print("   3. Ensure database and tables exist")
        print("   4. Check firewall/network settings")
    
    print(f"\n🕒 Test completed at {datetime.now()}")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n⏹️ Test interrupted by user")
    except Exception as e:
        print(f"\n💥 Unexpected error: {e}")
        print(f"Traceback: {traceback.format_exc()}")
