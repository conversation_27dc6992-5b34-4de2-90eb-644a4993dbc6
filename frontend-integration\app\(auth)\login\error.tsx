"use client";

import { useEffect } from "react";

/**
 * Login Page Error Boundary
 * 
 * Handles errors that occur on the login page
 */
interface LoginErrorProps {
  error: Error & { digest?: string };
  reset: () => void;
}

export default function LoginError({ error, reset }: LoginErrorProps) {
  useEffect(() => {
    console.error("Login page error:", error);
  }, [error]);

  return (
    <div className="space-y-6">
      {/* Error icon */}
      <div className="text-center">
        <div className="mx-auto h-12 w-12 bg-red-100 rounded-full flex items-center justify-center mb-4">
          <span className="text-red-600 text-2xl">⚠️</span>
        </div>
        <h2 className="text-xl font-semibold text-gray-800 mb-2">
          Login Error
        </h2>
        <p className="text-gray-600">
          We encountered an error while loading the login page.
        </p>
      </div>

      {/* Error details (development only) */}
      {process.env.NODE_ENV === "development" && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <h3 className="font-semibold text-red-800 mb-2">Error Details:</h3>
          <p className="text-sm text-red-700 font-mono break-all">
            {error.message}
          </p>
        </div>
      )}

      {/* Action buttons */}
      <div className="flex flex-col gap-3">
        <button
          onClick={reset}
          className="w-full px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
        >
          Try Again
        </button>
        
        <button
          onClick={() => window.location.href = "/"}
          className="w-full px-4 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300 transition-colors"
        >
          Go Home
        </button>
      </div>
    </div>
  );
}
