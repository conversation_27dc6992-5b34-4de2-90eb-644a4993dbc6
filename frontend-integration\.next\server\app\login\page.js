/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/login/page";
exports.ids = ["app/login/page"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("assert");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tty":
/*!**********************!*\
  !*** external "tty" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tty");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Flogin%2Fpage&page=%2Flogin%2Fpage&appPaths=%2Flogin%2Fpage&pagePath=private-next-app-dir%2Flogin%2Fpage.tsx&appDir=C%3A%5CUsers%5CImran%20Bhai%5COneDrive%5CPictures%5Cschool_pro%5Cschool-pro-backend%5Cfrontend-integration%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CImran%20Bhai%5COneDrive%5CPictures%5Cschool_pro%5Cschool-pro-backend%5Cfrontend-integration&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Flogin%2Fpage&page=%2Flogin%2Fpage&appPaths=%2Flogin%2Fpage&pagePath=private-next-app-dir%2Flogin%2Fpage.tsx&appDir=C%3A%5CUsers%5CImran%20Bhai%5COneDrive%5CPictures%5Cschool_pro%5Cschool-pro-backend%5Cfrontend-integration%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CImran%20Bhai%5COneDrive%5CPictures%5Cschool_pro%5Cschool-pro-backend%5Cfrontend-integration&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?d969\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'login',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/login/page.tsx */ \"(rsc)/./app/login/page.tsx\")), \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Pictures\\\\school_pro\\\\school-pro-backend\\\\frontend-integration\\\\app\\\\login\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\")), \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Pictures\\\\school_pro\\\\school-pro-backend\\\\frontend-integration\\\\app\\\\layout.tsx\"],\n'error': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/error.tsx */ \"(rsc)/./app/error.tsx\")), \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Pictures\\\\school_pro\\\\school-pro-backend\\\\frontend-integration\\\\app\\\\error.tsx\"],\n'loading': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/loading.tsx */ \"(rsc)/./app/loading.tsx\")), \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Pictures\\\\school_pro\\\\school-pro-backend\\\\frontend-integration\\\\app\\\\loading.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/not-found.tsx */ \"(rsc)/./app/not-found.tsx\")), \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Pictures\\\\school_pro\\\\school-pro-backend\\\\frontend-integration\\\\app\\\\not-found.tsx\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Pictures\\\\school_pro\\\\school-pro-backend\\\\frontend-integration\\\\app\\\\login\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/login/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/login/page\",\n        pathname: \"/login\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Flogin%2Fpage&page=%2Flogin%2Fpage&appPaths=%2Flogin%2Fpage&pagePath=private-next-app-dir%2Flogin%2Fpage.tsx&appDir=C%3A%5CUsers%5CImran%20Bhai%5COneDrive%5CPictures%5Cschool_pro%5Cschool-pro-backend%5Cfrontend-integration%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CImran%20Bhai%5COneDrive%5CPictures%5Cschool_pro%5Cschool-pro-backend%5Cfrontend-integration&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CImran%20Bhai%5C%5COneDrive%5C%5CPictures%5C%5Cschool_pro%5C%5Cschool-pro-backend%5C%5Cfrontend-integration%5C%5Capp%5C%5Cerror.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CImran%20Bhai%5C%5COneDrive%5C%5CPictures%5C%5Cschool_pro%5C%5Cschool-pro-backend%5C%5Cfrontend-integration%5C%5Capp%5C%5Cerror.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/error.tsx */ \"(ssr)/./app/error.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q0ltcmFuJTIwQmhhaSU1QyU1Q09uZURyaXZlJTVDJTVDUGljdHVyZXMlNUMlNUNzY2hvb2xfcHJvJTVDJTVDc2Nob29sLXByby1iYWNrZW5kJTVDJTVDZnJvbnRlbmQtaW50ZWdyYXRpb24lNUMlNUNhcHAlNUMlNUNlcnJvci50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLDBJQUFvSiIsInNvdXJjZXMiOlsid2VicGFjazovL3NjaG9vbC1tYW5hZ2VtZW50LWZyb250ZW5kLz8yMGQzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcSW1yYW4gQmhhaVxcXFxPbmVEcml2ZVxcXFxQaWN0dXJlc1xcXFxzY2hvb2xfcHJvXFxcXHNjaG9vbC1wcm8tYmFja2VuZFxcXFxmcm9udGVuZC1pbnRlZ3JhdGlvblxcXFxhcHBcXFxcZXJyb3IudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CImran%20Bhai%5C%5COneDrive%5C%5CPictures%5C%5Cschool_pro%5C%5Cschool-pro-backend%5C%5Cfrontend-integration%5C%5Capp%5C%5Cerror.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CImran%20Bhai%5C%5COneDrive%5C%5CPictures%5C%5Cschool_pro%5C%5Cschool-pro-backend%5C%5Cfrontend-integration%5C%5Capp%5C%5Clogin%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CImran%20Bhai%5C%5COneDrive%5C%5CPictures%5C%5Cschool_pro%5C%5Cschool-pro-backend%5C%5Cfrontend-integration%5C%5Capp%5C%5Clogin%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/login/page.tsx */ \"(ssr)/./app/login/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q0ltcmFuJTIwQmhhaSU1QyU1Q09uZURyaXZlJTVDJTVDUGljdHVyZXMlNUMlNUNzY2hvb2xfcHJvJTVDJTVDc2Nob29sLXByby1iYWNrZW5kJTVDJTVDZnJvbnRlbmQtaW50ZWdyYXRpb24lNUMlNUNhcHAlNUMlNUNsb2dpbiU1QyU1Q3BhZ2UudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxvSkFBMEoiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9zY2hvb2wtbWFuYWdlbWVudC1mcm9udGVuZC8/NTRiNyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXEltcmFuIEJoYWlcXFxcT25lRHJpdmVcXFxcUGljdHVyZXNcXFxcc2Nob29sX3Byb1xcXFxzY2hvb2wtcHJvLWJhY2tlbmRcXFxcZnJvbnRlbmQtaW50ZWdyYXRpb25cXFxcYXBwXFxcXGxvZ2luXFxcXHBhZ2UudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CImran%20Bhai%5C%5COneDrive%5C%5CPictures%5C%5Cschool_pro%5C%5Cschool-pro-backend%5C%5Cfrontend-integration%5C%5Capp%5C%5Clogin%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CImran%20Bhai%5C%5COneDrive%5C%5CPictures%5C%5Cschool_pro%5C%5Cschool-pro-backend%5C%5Cfrontend-integration%5C%5Ccomponents%5C%5Cproviders.tsx%22%2C%22ids%22%3A%5B%22Providers%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CImran%20Bhai%5C%5COneDrive%5C%5CPictures%5C%5Cschool_pro%5C%5Cschool-pro-backend%5C%5Cfrontend-integration%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CImran%20Bhai%5C%5COneDrive%5C%5CPictures%5C%5Cschool_pro%5C%5Cschool-pro-backend%5C%5Cfrontend-integration%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CImran%20Bhai%5C%5COneDrive%5C%5CPictures%5C%5Cschool_pro%5C%5Cschool-pro-backend%5C%5Cfrontend-integration%5C%5Cnode_modules%5C%5Csonner%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CImran%20Bhai%5C%5COneDrive%5C%5CPictures%5C%5Cschool_pro%5C%5Cschool-pro-backend%5C%5Cfrontend-integration%5C%5Ccomponents%5C%5Cproviders.tsx%22%2C%22ids%22%3A%5B%22Providers%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CImran%20Bhai%5C%5COneDrive%5C%5CPictures%5C%5Cschool_pro%5C%5Cschool-pro-backend%5C%5Cfrontend-integration%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CImran%20Bhai%5C%5COneDrive%5C%5CPictures%5C%5Cschool_pro%5C%5Cschool-pro-backend%5C%5Cfrontend-integration%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CImran%20Bhai%5C%5COneDrive%5C%5CPictures%5C%5Cschool_pro%5C%5Cschool-pro-backend%5C%5Cfrontend-integration%5C%5Cnode_modules%5C%5Csonner%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/providers.tsx */ \"(ssr)/./components/providers.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/sonner/dist/index.mjs */ \"(ssr)/./node_modules/sonner/dist/index.mjs\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CImran%20Bhai%5C%5COneDrive%5C%5CPictures%5C%5Cschool_pro%5C%5Cschool-pro-backend%5C%5Cfrontend-integration%5C%5Ccomponents%5C%5Cproviders.tsx%22%2C%22ids%22%3A%5B%22Providers%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CImran%20Bhai%5C%5COneDrive%5C%5CPictures%5C%5Cschool_pro%5C%5Cschool-pro-backend%5C%5Cfrontend-integration%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CImran%20Bhai%5C%5COneDrive%5C%5CPictures%5C%5Cschool_pro%5C%5Cschool-pro-backend%5C%5Cfrontend-integration%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CImran%20Bhai%5C%5COneDrive%5C%5CPictures%5C%5Cschool_pro%5C%5Cschool-pro-backend%5C%5Cfrontend-integration%5C%5Cnode_modules%5C%5Csonner%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CImran%20Bhai%5C%5COneDrive%5C%5CPictures%5C%5Cschool_pro%5C%5Cschool-pro-backend%5C%5Cfrontend-integration%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CImran%20Bhai%5C%5COneDrive%5C%5CPictures%5C%5Cschool_pro%5C%5Cschool-pro-backend%5C%5Cfrontend-integration%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CImran%20Bhai%5C%5COneDrive%5C%5CPictures%5C%5Cschool_pro%5C%5Cschool-pro-backend%5C%5Cfrontend-integration%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CImran%20Bhai%5C%5COneDrive%5C%5CPictures%5C%5Cschool_pro%5C%5Cschool-pro-backend%5C%5Cfrontend-integration%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CImran%20Bhai%5C%5COneDrive%5C%5CPictures%5C%5Cschool_pro%5C%5Cschool-pro-backend%5C%5Cfrontend-integration%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CImran%20Bhai%5C%5COneDrive%5C%5CPictures%5C%5Cschool_pro%5C%5Cschool-pro-backend%5C%5Cfrontend-integration%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CImran%20Bhai%5C%5COneDrive%5C%5CPictures%5C%5Cschool_pro%5C%5Cschool-pro-backend%5C%5Cfrontend-integration%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CImran%20Bhai%5C%5COneDrive%5C%5CPictures%5C%5Cschool_pro%5C%5Cschool-pro-backend%5C%5Cfrontend-integration%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CImran%20Bhai%5C%5COneDrive%5C%5CPictures%5C%5Cschool_pro%5C%5Cschool-pro-backend%5C%5Cfrontend-integration%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CImran%20Bhai%5C%5COneDrive%5C%5CPictures%5C%5Cschool_pro%5C%5Cschool-pro-backend%5C%5Cfrontend-integration%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CImran%20Bhai%5C%5COneDrive%5C%5CPictures%5C%5Cschool_pro%5C%5Cschool-pro-backend%5C%5Cfrontend-integration%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CImran%20Bhai%5C%5COneDrive%5C%5CPictures%5C%5Cschool_pro%5C%5Cschool-pro-backend%5C%5Cfrontend-integration%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CImran%20Bhai%5C%5COneDrive%5C%5CPictures%5C%5Cschool_pro%5C%5Cschool-pro-backend%5C%5Cfrontend-integration%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CImran%20Bhai%5C%5COneDrive%5C%5CPictures%5C%5Cschool_pro%5C%5Cschool-pro-backend%5C%5Cfrontend-integration%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CImran%20Bhai%5C%5COneDrive%5C%5CPictures%5C%5Cschool_pro%5C%5Cschool-pro-backend%5C%5Cfrontend-integration%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CImran%20Bhai%5C%5COneDrive%5C%5CPictures%5C%5Cschool_pro%5C%5Cschool-pro-backend%5C%5Cfrontend-integration%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CImran%20Bhai%5C%5COneDrive%5C%5CPictures%5C%5Cschool_pro%5C%5Cschool-pro-backend%5C%5Cfrontend-integration%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CImran%20Bhai%5C%5COneDrive%5C%5CPictures%5C%5Cschool_pro%5C%5Cschool-pro-backend%5C%5Cfrontend-integration%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CImran%20Bhai%5C%5COneDrive%5C%5CPictures%5C%5Cschool_pro%5C%5Cschool-pro-backend%5C%5Cfrontend-integration%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Clink.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CImran%20Bhai%5C%5COneDrive%5C%5CPictures%5C%5Cschool_pro%5C%5Cschool-pro-backend%5C%5Cfrontend-integration%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Clink.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/link.js */ \"(ssr)/./node_modules/next/dist/client/link.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q0ltcmFuJTIwQmhhaSU1QyU1Q09uZURyaXZlJTVDJTVDUGljdHVyZXMlNUMlNUNzY2hvb2xfcHJvJTVDJTVDc2Nob29sLXByby1iYWNrZW5kJTVDJTVDZnJvbnRlbmQtaW50ZWdyYXRpb24lNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZGlzdCU1QyU1Q2NsaWVudCU1QyU1Q2xpbmsuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjIqJTIyJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxnTUFBK0siLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9zY2hvb2wtbWFuYWdlbWVudC1mcm9udGVuZC8/MDk3MiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXEltcmFuIEJoYWlcXFxcT25lRHJpdmVcXFxcUGljdHVyZXNcXFxcc2Nob29sX3Byb1xcXFxzY2hvb2wtcHJvLWJhY2tlbmRcXFxcZnJvbnRlbmQtaW50ZWdyYXRpb25cXFxcbm9kZV9tb2R1bGVzXFxcXG5leHRcXFxcZGlzdFxcXFxjbGllbnRcXFxcbGluay5qc1wiKTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CImran%20Bhai%5C%5COneDrive%5C%5CPictures%5C%5Cschool_pro%5C%5Cschool-pro-backend%5C%5Cfrontend-integration%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Clink.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./api/apiClient.ts":
/*!**************************!*\
  !*** ./api/apiClient.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   api: () => (/* binding */ api),\n/* harmony export */   apiUtils: () => (/* binding */ apiUtils),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! axios */ \"(ssr)/./node_modules/axios/lib/axios.js\");\n/**\n * API Client - Fixed URL handling and authentication\n */ \n// Base URL configuration - use Next.js proxy to avoid CORS issues\nconst BASE_URL = \"/api/v1\"; // Let Next.js proxy handle the backend connection\n// Create axios instance with proper URL handling\nconst apiClient = axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].create({\n    baseURL: BASE_URL,\n    timeout: 30000,\n    headers: {\n        \"Content-Type\": \"application/json\"\n    }\n});\n// Request interceptor for authentication\napiClient.interceptors.request.use((config)=>{\n    // Get token from localStorage\n    const token = localStorage.getItem(\"access_token\") || localStorage.getItem(\"auth.token\");\n    if (token) {\n        config.headers.Authorization = `Bearer ${token}`;\n    }\n    // Let axios handle URL combination automatically\n    // No manual URL manipulation needed\n    console.log(`🔗 API Request: ${config.method?.toUpperCase()} ${config.url}`, {\n        headers: config.headers,\n        data: config.data\n    });\n    return config;\n}, (error)=>{\n    console.error(\"❌ Request interceptor error:\", error);\n    return Promise.reject(error);\n});\n// Response interceptor for error handling\napiClient.interceptors.response.use((response)=>{\n    console.log(`✅ API Response: ${response.status} ${response.config.url}`, response.data);\n    return response;\n}, (error)=>{\n    console.error(`❌ API Error: ${error.response?.status} ${error.config?.url}`, {\n        status: error.response?.status,\n        data: error.response?.data,\n        message: error.message\n    });\n    // Handle 401 Unauthorized\n    if (error.response?.status === 401) {\n        localStorage.removeItem(\"access_token\");\n        localStorage.removeItem(\"auth.token\");\n        window.location.href = \"/login\";\n    }\n    return Promise.reject(error);\n});\n// API utility functions\nconst apiUtils = {\n    get: (url, config)=>apiClient.get(url, config).then((response)=>response.data),\n    post: (url, data, config)=>apiClient.post(url, data, config).then((response)=>response.data),\n    put: (url, data, config)=>apiClient.put(url, data, config).then((response)=>response.data),\n    patch: (url, data, config)=>apiClient.patch(url, data, config).then((response)=>response.data),\n    delete: (url, config)=>apiClient.delete(url, config).then((response)=>response.data)\n};\nconst api = apiClient;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (apiClient);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./api/apiClient.ts\n");

/***/ }),

/***/ "(ssr)/./app/error.tsx":
/*!***********************!*\
  !*** ./app/error.tsx ***!
  \***********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Error)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction Error({ error, reset }) {\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Log the error to an error reporting service\n        console.error(\"Global error:\", error);\n    }, [\n        error\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen flex items-center justify-center bg-gray-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-center max-w-md mx-auto p-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mx-auto h-16 w-16 bg-red-100 rounded-full flex items-center justify-center mb-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-red-600 text-3xl\",\n                        children: \"⚠️\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Pictures\\\\school_pro\\\\school-pro-backend\\\\frontend-integration\\\\app\\\\error.tsx\",\n                        lineNumber: 27,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Pictures\\\\school_pro\\\\school-pro-backend\\\\frontend-integration\\\\app\\\\error.tsx\",\n                    lineNumber: 26,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                    className: \"text-2xl font-bold text-gray-800 mb-4\",\n                    children: \"Something went wrong!\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Pictures\\\\school_pro\\\\school-pro-backend\\\\frontend-integration\\\\app\\\\error.tsx\",\n                    lineNumber: 31,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-gray-600 mb-6\",\n                    children: \"We encountered an unexpected error. Please try again or contact support if the problem persists.\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Pictures\\\\school_pro\\\\school-pro-backend\\\\frontend-integration\\\\app\\\\error.tsx\",\n                    lineNumber: 35,\n                    columnNumber: 9\n                }, this),\n                 true && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-red-50 border border-red-200 rounded-lg p-4 mb-6 text-left\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"font-semibold text-red-800 mb-2\",\n                            children: \"Error Details:\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Pictures\\\\school_pro\\\\school-pro-backend\\\\frontend-integration\\\\app\\\\error.tsx\",\n                            lineNumber: 42,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm text-red-700 font-mono break-all\",\n                            children: error.message\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Pictures\\\\school_pro\\\\school-pro-backend\\\\frontend-integration\\\\app\\\\error.tsx\",\n                            lineNumber: 43,\n                            columnNumber: 13\n                        }, this),\n                        error.digest && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xs text-red-600 mt-2\",\n                            children: [\n                                \"Error ID: \",\n                                error.digest\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Pictures\\\\school_pro\\\\school-pro-backend\\\\frontend-integration\\\\app\\\\error.tsx\",\n                            lineNumber: 47,\n                            columnNumber: 15\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Pictures\\\\school_pro\\\\school-pro-backend\\\\frontend-integration\\\\app\\\\error.tsx\",\n                    lineNumber: 41,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col sm:flex-row gap-3 justify-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: reset,\n                            className: \"px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\",\n                            children: \"Try Again\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Pictures\\\\school_pro\\\\school-pro-backend\\\\frontend-integration\\\\app\\\\error.tsx\",\n                            lineNumber: 56,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>window.location.href = \"/\",\n                            className: \"px-6 py-3 bg-gray-200 text-gray-800 rounded-lg hover:bg-gray-300 transition-colors\",\n                            children: \"Go Home\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Pictures\\\\school_pro\\\\school-pro-backend\\\\frontend-integration\\\\app\\\\error.tsx\",\n                            lineNumber: 63,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Pictures\\\\school_pro\\\\school-pro-backend\\\\frontend-integration\\\\app\\\\error.tsx\",\n                    lineNumber: 55,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-sm text-gray-500 mt-6\",\n                    children: [\n                        \"Need help? Contact\",\n                        \" \",\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                            href: \"mailto:<EMAIL>\",\n                            className: \"text-blue-600 hover:underline\",\n                            children: \"<EMAIL>\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Pictures\\\\school_pro\\\\school-pro-backend\\\\frontend-integration\\\\app\\\\error.tsx\",\n                            lineNumber: 74,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Pictures\\\\school_pro\\\\school-pro-backend\\\\frontend-integration\\\\app\\\\error.tsx\",\n                    lineNumber: 72,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Pictures\\\\school_pro\\\\school-pro-backend\\\\frontend-integration\\\\app\\\\error.tsx\",\n            lineNumber: 24,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Pictures\\\\school_pro\\\\school-pro-backend\\\\frontend-integration\\\\app\\\\error.tsx\",\n        lineNumber: 23,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/error.tsx\n");

/***/ }),

/***/ "(ssr)/./app/login/page.tsx":
/*!****************************!*\
  !*** ./app/login/page.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ LoginPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _hooks_useAuthQuery__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/hooks/useAuthQuery */ \"(ssr)/./hooks/useAuthQuery.ts\");\n/**\n * Login Page - Professional implementation with React Query\n *\n * Features:\n * - Form handling with validation\n * - React Query auth hooks\n * - Proper error handling and user feedback\n * - Redirect after successful login\n * - Cookie-based authentication\n */ /* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n// Demo credentials for testing\nconst DEMO_CREDENTIALS = {\n    username: \"<EMAIL>\",\n    password: \"admin123\"\n};\nfunction LoginPage() {\n    const [username, setUsername] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [password, setPassword] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [rememberMe, setRememberMe] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_1__.useRouter)();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_1__.useSearchParams)();\n    const redirectTo = searchParams.get(\"redirect\") || \"/dashboard\";\n    const loginMutation = (0,_hooks_useAuthQuery__WEBPACK_IMPORTED_MODULE_3__.useLogin)({\n        onSuccess: ()=>{\n            router.push(redirectTo);\n        }\n    });\n    const { isAuthenticated, isLoading: authLoading } = (0,_hooks_useAuthQuery__WEBPACK_IMPORTED_MODULE_3__.useIsAuthenticated)();\n    // Redirect if already authenticated\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (isAuthenticated && !authLoading) {\n            router.push(redirectTo);\n        }\n    }, [\n        isAuthenticated,\n        authLoading,\n        router,\n        redirectTo\n    ]);\n    const handleLogin = async (e)=>{\n        e.preventDefault();\n        if (!username || !password) {\n            return;\n        }\n        loginMutation.mutate({\n            username,\n            password\n        });\n    };\n    const fillDemoCredentials = ()=>{\n        setUsername(DEMO_CREDENTIALS.username);\n        setPassword(DEMO_CREDENTIALS.password);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50 flex items-center justify-center p-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"w-full max-w-md\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white/80 backdrop-blur-sm shadow-2xl rounded-2xl border-0 p-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mx-auto w-16 h-16 bg-blue-600 rounded-full flex items-center justify-center mb-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-8 h-8 text-white\",\n                                    fill: \"none\",\n                                    stroke: \"currentColor\",\n                                    viewBox: \"0 0 24 24\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 2,\n                                            d: \"M12 14l9-5-9-5-9 5 9 5z\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Pictures\\\\school_pro\\\\school-pro-backend\\\\frontend-integration\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 80,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 2,\n                                            d: \"M12 14l6.16-3.422a12.083 12.083 0 01.665 6.479A11.952 11.952 0 0012 20.055a11.952 11.952 0 00-6.824-2.998 12.078 12.078 0 01.665-6.479L12 14z\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Pictures\\\\school_pro\\\\school-pro-backend\\\\frontend-integration\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 86,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 2,\n                                            d: \"M12 14l9-5-9-5-9 5 9 5zm0 0l6.16-3.422a12.083 12.083 0 01.665 6.479A11.952 11.952 0 0012 20.055a11.952 11.952 0 00-6.824-2.998 12.078 12.078 0 01.665-6.479L12 14z\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Pictures\\\\school_pro\\\\school-pro-backend\\\\frontend-integration\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 92,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Pictures\\\\school_pro\\\\school-pro-backend\\\\frontend-integration\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 74,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Pictures\\\\school_pro\\\\school-pro-backend\\\\frontend-integration\\\\app\\\\login\\\\page.tsx\",\n                                lineNumber: 73,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-2xl font-bold text-gray-900\",\n                                children: \"School Management\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Pictures\\\\school_pro\\\\school-pro-backend\\\\frontend-integration\\\\app\\\\login\\\\page.tsx\",\n                                lineNumber: 100,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"mt-2 text-gray-600\",\n                                children: \"Sign in to your account\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Pictures\\\\school_pro\\\\school-pro-backend\\\\frontend-integration\\\\app\\\\login\\\\page.tsx\",\n                                lineNumber: 101,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Pictures\\\\school_pro\\\\school-pro-backend\\\\frontend-integration\\\\app\\\\login\\\\page.tsx\",\n                        lineNumber: 72,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-sm text-yellow-800\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-between items-center mb-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"font-medium\",\n                                            children: \"Demo Credentials:\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Pictures\\\\school_pro\\\\school-pro-backend\\\\frontend-integration\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 108,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"button\",\n                                            onClick: fillDemoCredentials,\n                                            className: \"text-xs bg-yellow-200 hover:bg-yellow-300 px-2 py-1 rounded transition-colors\",\n                                            children: \"Fill Form\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Pictures\\\\school_pro\\\\school-pro-backend\\\\frontend-integration\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 109,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Pictures\\\\school_pro\\\\school-pro-backend\\\\frontend-integration\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 107,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: [\n                                        \"Username: \",\n                                        DEMO_CREDENTIALS.username\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Pictures\\\\school_pro\\\\school-pro-backend\\\\frontend-integration\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 117,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: [\n                                        \"Password: \",\n                                        DEMO_CREDENTIALS.password\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Pictures\\\\school_pro\\\\school-pro-backend\\\\frontend-integration\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 118,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Pictures\\\\school_pro\\\\school-pro-backend\\\\frontend-integration\\\\app\\\\login\\\\page.tsx\",\n                            lineNumber: 106,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Pictures\\\\school_pro\\\\school-pro-backend\\\\frontend-integration\\\\app\\\\login\\\\page.tsx\",\n                        lineNumber: 105,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                        className: \"space-y-6\",\n                        onSubmit: handleLogin,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        htmlFor: \"username\",\n                                        className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                        children: \"Email Address\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Pictures\\\\school_pro\\\\school-pro-backend\\\\frontend-integration\\\\app\\\\login\\\\page.tsx\",\n                                        lineNumber: 125,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        id: \"username\",\n                                        name: \"username\",\n                                        type: \"text\",\n                                        autoComplete: \"username\",\n                                        required: true,\n                                        value: username,\n                                        onChange: (e)=>setUsername(e.target.value),\n                                        className: \"w-full h-12 px-4 border border-gray-200 rounded-lg placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors\",\n                                        placeholder: \"<EMAIL>\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Pictures\\\\school_pro\\\\school-pro-backend\\\\frontend-integration\\\\app\\\\login\\\\page.tsx\",\n                                        lineNumber: 128,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Pictures\\\\school_pro\\\\school-pro-backend\\\\frontend-integration\\\\app\\\\login\\\\page.tsx\",\n                                lineNumber: 124,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        htmlFor: \"password\",\n                                        className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                        children: \"Password\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Pictures\\\\school_pro\\\\school-pro-backend\\\\frontend-integration\\\\app\\\\login\\\\page.tsx\",\n                                        lineNumber: 142,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        id: \"password\",\n                                        name: \"password\",\n                                        type: \"password\",\n                                        autoComplete: \"current-password\",\n                                        required: true,\n                                        value: password,\n                                        onChange: (e)=>setPassword(e.target.value),\n                                        className: \"w-full h-12 px-4 border border-gray-200 rounded-lg placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors\",\n                                        placeholder: \"Enter your password\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Pictures\\\\school_pro\\\\school-pro-backend\\\\frontend-integration\\\\app\\\\login\\\\page.tsx\",\n                                        lineNumber: 145,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Pictures\\\\school_pro\\\\school-pro-backend\\\\frontend-integration\\\\app\\\\login\\\\page.tsx\",\n                                lineNumber: 141,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                id: \"remember-me\",\n                                                name: \"remember-me\",\n                                                type: \"checkbox\",\n                                                checked: rememberMe,\n                                                onChange: (e)=>setRememberMe(e.target.checked),\n                                                className: \"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Pictures\\\\school_pro\\\\school-pro-backend\\\\frontend-integration\\\\app\\\\login\\\\page.tsx\",\n                                                lineNumber: 160,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                htmlFor: \"remember-me\",\n                                                className: \"ml-2 block text-sm text-gray-900\",\n                                                children: \"Remember me\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Pictures\\\\school_pro\\\\school-pro-backend\\\\frontend-integration\\\\app\\\\login\\\\page.tsx\",\n                                                lineNumber: 168,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Pictures\\\\school_pro\\\\school-pro-backend\\\\frontend-integration\\\\app\\\\login\\\\page.tsx\",\n                                        lineNumber: 159,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"#\",\n                                            className: \"font-medium text-blue-600 hover:text-blue-500 transition-colors\",\n                                            children: \"Forgot your password?\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Pictures\\\\school_pro\\\\school-pro-backend\\\\frontend-integration\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 174,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Pictures\\\\school_pro\\\\school-pro-backend\\\\frontend-integration\\\\app\\\\login\\\\page.tsx\",\n                                        lineNumber: 173,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Pictures\\\\school_pro\\\\school-pro-backend\\\\frontend-integration\\\\app\\\\login\\\\page.tsx\",\n                                lineNumber: 158,\n                                columnNumber: 13\n                            }, this),\n                            loginMutation.error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-red-50 border border-red-200 rounded-lg p-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm text-red-800\",\n                                    children: loginMutation.error instanceof Error ? loginMutation.error.message : \"Login failed. Please try again.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Pictures\\\\school_pro\\\\school-pro-backend\\\\frontend-integration\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 186,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Pictures\\\\school_pro\\\\school-pro-backend\\\\frontend-integration\\\\app\\\\login\\\\page.tsx\",\n                                lineNumber: 185,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"submit\",\n                                disabled: loginMutation.isPending || !username || !password,\n                                className: \"w-full h-12 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-lg transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed\",\n                                children: loginMutation.isPending ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Pictures\\\\school_pro\\\\school-pro-backend\\\\frontend-integration\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 202,\n                                            columnNumber: 19\n                                        }, this),\n                                        \"Signing in...\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Pictures\\\\school_pro\\\\school-pro-backend\\\\frontend-integration\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 201,\n                                    columnNumber: 17\n                                }, this) : \"Sign In\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Pictures\\\\school_pro\\\\school-pro-backend\\\\frontend-integration\\\\app\\\\login\\\\page.tsx\",\n                                lineNumber: 195,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Pictures\\\\school_pro\\\\school-pro-backend\\\\frontend-integration\\\\app\\\\login\\\\page.tsx\",\n                        lineNumber: 123,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center mt-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"px-3 py-1 bg-green-100 text-green-800 text-xs rounded-full\",\n                            children: \"Production Ready - Cookie-based Auth\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Pictures\\\\school_pro\\\\school-pro-backend\\\\frontend-integration\\\\app\\\\login\\\\page.tsx\",\n                            lineNumber: 213,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Pictures\\\\school_pro\\\\school-pro-backend\\\\frontend-integration\\\\app\\\\login\\\\page.tsx\",\n                        lineNumber: 212,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center text-sm text-gray-600 mt-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            children: [\n                                \"Don't have an account?\",\n                                \" \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: \"#\",\n                                    className: \"font-medium text-blue-600 hover:text-blue-500 transition-colors\",\n                                    children: \"Contact administrator\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Pictures\\\\school_pro\\\\school-pro-backend\\\\frontend-integration\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 222,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Pictures\\\\school_pro\\\\school-pro-backend\\\\frontend-integration\\\\app\\\\login\\\\page.tsx\",\n                            lineNumber: 220,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Pictures\\\\school_pro\\\\school-pro-backend\\\\frontend-integration\\\\app\\\\login\\\\page.tsx\",\n                        lineNumber: 219,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Pictures\\\\school_pro\\\\school-pro-backend\\\\frontend-integration\\\\app\\\\login\\\\page.tsx\",\n                lineNumber: 70,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Pictures\\\\school_pro\\\\school-pro-backend\\\\frontend-integration\\\\app\\\\login\\\\page.tsx\",\n            lineNumber: 69,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Pictures\\\\school_pro\\\\school-pro-backend\\\\frontend-integration\\\\app\\\\login\\\\page.tsx\",\n        lineNumber: 68,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9hcHAvbG9naW4vcGFnZS50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OztBQUFBOzs7Ozs7Ozs7Q0FTQztBQUk0RDtBQUNqQjtBQUV3QjtBQUVwRSwrQkFBK0I7QUFDL0IsTUFBTU0sbUJBQW1CO0lBQ3ZCQyxVQUFVO0lBQ1ZDLFVBQVU7QUFDWjtBQUVlLFNBQVNDO0lBQ3RCLE1BQU0sQ0FBQ0YsVUFBVUcsWUFBWSxHQUFHUCwrQ0FBUUEsQ0FBQztJQUN6QyxNQUFNLENBQUNLLFVBQVVHLFlBQVksR0FBR1IsK0NBQVFBLENBQUM7SUFDekMsTUFBTSxDQUFDUyxZQUFZQyxjQUFjLEdBQUdWLCtDQUFRQSxDQUFDO0lBRTdDLE1BQU1XLFNBQVNkLDBEQUFTQTtJQUN4QixNQUFNZSxlQUFlZCxnRUFBZUE7SUFDcEMsTUFBTWUsYUFBYUQsYUFBYUUsR0FBRyxDQUFDLGVBQWU7SUFFbkQsTUFBTUMsZ0JBQWdCYiw2REFBUUEsQ0FBQztRQUM3QmMsV0FBVztZQUNUTCxPQUFPTSxJQUFJLENBQUNKO1FBQ2Q7SUFDRjtJQUVBLE1BQU0sRUFBRUssZUFBZSxFQUFFQyxXQUFXQyxXQUFXLEVBQUUsR0FBR25CLHVFQUFrQkE7SUFFdEUsb0NBQW9DO0lBQ3BDRixnREFBU0EsQ0FBQztRQUNSLElBQUltQixtQkFBbUIsQ0FBQ0UsYUFBYTtZQUNuQ1QsT0FBT00sSUFBSSxDQUFDSjtRQUNkO0lBQ0YsR0FBRztRQUFDSztRQUFpQkU7UUFBYVQ7UUFBUUU7S0FBVztJQUVyRCxNQUFNUSxjQUFjLE9BQU9DO1FBQ3pCQSxFQUFFQyxjQUFjO1FBRWhCLElBQUksQ0FBQ25CLFlBQVksQ0FBQ0MsVUFBVTtZQUMxQjtRQUNGO1FBRUFVLGNBQWNTLE1BQU0sQ0FBQztZQUNuQnBCO1lBQ0FDO1FBQ0Y7SUFDRjtJQUVBLE1BQU1vQixzQkFBc0I7UUFDMUJsQixZQUFZSixpQkFBaUJDLFFBQVE7UUFDckNJLFlBQVlMLGlCQUFpQkUsUUFBUTtJQUN2QztJQUVBLHFCQUNFLDhEQUFDcUI7UUFBSUMsV0FBVTtrQkFDYiw0RUFBQ0Q7WUFBSUMsV0FBVTtzQkFDYiw0RUFBQ0Q7Z0JBQUlDLFdBQVU7O2tDQUViLDhEQUFDRDt3QkFBSUMsV0FBVTs7MENBQ2IsOERBQUNEO2dDQUFJQyxXQUFVOzBDQUNiLDRFQUFDQztvQ0FDQ0QsV0FBVTtvQ0FDVkUsTUFBSztvQ0FDTEMsUUFBTztvQ0FDUEMsU0FBUTs7c0RBRVIsOERBQUNDOzRDQUNDQyxlQUFjOzRDQUNkQyxnQkFBZTs0Q0FDZkMsYUFBYTs0Q0FDYkMsR0FBRTs7Ozs7O3NEQUVKLDhEQUFDSjs0Q0FDQ0MsZUFBYzs0Q0FDZEMsZ0JBQWU7NENBQ2ZDLGFBQWE7NENBQ2JDLEdBQUU7Ozs7OztzREFFSiw4REFBQ0o7NENBQ0NDLGVBQWM7NENBQ2RDLGdCQUFlOzRDQUNmQyxhQUFhOzRDQUNiQyxHQUFFOzs7Ozs7Ozs7Ozs7Ozs7OzswQ0FJUiw4REFBQ0M7Z0NBQUdWLFdBQVU7MENBQW1DOzs7Ozs7MENBQ2pELDhEQUFDVztnQ0FBRVgsV0FBVTswQ0FBcUI7Ozs7Ozs7Ozs7OztrQ0FJcEMsOERBQUNEO3dCQUFJQyxXQUFVO2tDQUNiLDRFQUFDRDs0QkFBSUMsV0FBVTs7OENBQ2IsOERBQUNEO29DQUFJQyxXQUFVOztzREFDYiw4REFBQ1c7NENBQUVYLFdBQVU7c0RBQWM7Ozs7OztzREFDM0IsOERBQUNZOzRDQUNDQyxNQUFLOzRDQUNMQyxTQUFTaEI7NENBQ1RFLFdBQVU7c0RBQ1g7Ozs7Ozs7Ozs7Ozs4Q0FJSCw4REFBQ1c7O3dDQUFFO3dDQUFXbkMsaUJBQWlCQyxRQUFROzs7Ozs7OzhDQUN2Qyw4REFBQ2tDOzt3Q0FBRTt3Q0FBV25DLGlCQUFpQkUsUUFBUTs7Ozs7Ozs7Ozs7Ozs7Ozs7O2tDQUszQyw4REFBQ3FDO3dCQUFLZixXQUFVO3dCQUFZZ0IsVUFBVXRCOzswQ0FDcEMsOERBQUNLOztrREFDQyw4REFBQ2tCO3dDQUFNQyxTQUFRO3dDQUFXbEIsV0FBVTtrREFBK0M7Ozs7OztrREFHbkYsOERBQUNtQjt3Q0FDQ0MsSUFBRzt3Q0FDSEMsTUFBSzt3Q0FDTFIsTUFBSzt3Q0FDTFMsY0FBYTt3Q0FDYkMsUUFBUTt3Q0FDUkMsT0FBTy9DO3dDQUNQZ0QsVUFBVTlCLENBQUFBLElBQUtmLFlBQVllLEVBQUUrQixNQUFNLENBQUNGLEtBQUs7d0NBQ3pDeEIsV0FBVTt3Q0FDVjJCLGFBQVk7Ozs7Ozs7Ozs7OzswQ0FJaEIsOERBQUM1Qjs7a0RBQ0MsOERBQUNrQjt3Q0FBTUMsU0FBUTt3Q0FBV2xCLFdBQVU7a0RBQStDOzs7Ozs7a0RBR25GLDhEQUFDbUI7d0NBQ0NDLElBQUc7d0NBQ0hDLE1BQUs7d0NBQ0xSLE1BQUs7d0NBQ0xTLGNBQWE7d0NBQ2JDLFFBQVE7d0NBQ1JDLE9BQU85Qzt3Q0FDUCtDLFVBQVU5QixDQUFBQSxJQUFLZCxZQUFZYyxFQUFFK0IsTUFBTSxDQUFDRixLQUFLO3dDQUN6Q3hCLFdBQVU7d0NBQ1YyQixhQUFZOzs7Ozs7Ozs7Ozs7MENBSWhCLDhEQUFDNUI7Z0NBQUlDLFdBQVU7O2tEQUNiLDhEQUFDRDt3Q0FBSUMsV0FBVTs7MERBQ2IsOERBQUNtQjtnREFDQ0MsSUFBRztnREFDSEMsTUFBSztnREFDTFIsTUFBSztnREFDTGUsU0FBUzlDO2dEQUNUMkMsVUFBVTlCLENBQUFBLElBQUtaLGNBQWNZLEVBQUUrQixNQUFNLENBQUNFLE9BQU87Z0RBQzdDNUIsV0FBVTs7Ozs7OzBEQUVaLDhEQUFDaUI7Z0RBQU1DLFNBQVE7Z0RBQWNsQixXQUFVOzBEQUFtQzs7Ozs7Ozs7Ozs7O2tEQUs1RSw4REFBQ0Q7d0NBQUlDLFdBQVU7a0RBQ2IsNEVBQUM2Qjs0Q0FDQ0MsTUFBSzs0Q0FDTDlCLFdBQVU7c0RBQ1g7Ozs7Ozs7Ozs7Ozs7Ozs7OzRCQU9KWixjQUFjMkMsS0FBSyxrQkFDbEIsOERBQUNoQztnQ0FBSUMsV0FBVTswQ0FDYiw0RUFBQ0Q7b0NBQUlDLFdBQVU7OENBQ1paLGNBQWMyQyxLQUFLLFlBQVlDLFFBQzVCNUMsY0FBYzJDLEtBQUssQ0FBQ0UsT0FBTyxHQUMzQjs7Ozs7Ozs7Ozs7MENBTVYsOERBQUNyQjtnQ0FDQ0MsTUFBSztnQ0FDTHFCLFVBQVU5QyxjQUFjK0MsU0FBUyxJQUFJLENBQUMxRCxZQUFZLENBQUNDO2dDQUNuRHNCLFdBQVU7MENBRVRaLGNBQWMrQyxTQUFTLGlCQUN0Qiw4REFBQ3BDO29DQUFJQyxXQUFVOztzREFDYiw4REFBQ0Q7NENBQUlDLFdBQVU7Ozs7Ozt3Q0FBbUU7Ozs7OzsyQ0FJcEY7Ozs7Ozs7Ozs7OztrQ0FNTiw4REFBQ0Q7d0JBQUlDLFdBQVU7a0NBQ2IsNEVBQUNvQzs0QkFBS3BDLFdBQVU7c0NBQTZEOzs7Ozs7Ozs7OztrQ0FNL0UsOERBQUNEO3dCQUFJQyxXQUFVO2tDQUNiLDRFQUFDVzs7Z0NBQUU7Z0NBQ3NCOzhDQUN2Qiw4REFBQ2tCO29DQUNDQyxNQUFLO29DQUNMOUIsV0FBVTs4Q0FDWDs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBU2YiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9zY2hvb2wtbWFuYWdlbWVudC1mcm9udGVuZC8uL2FwcC9sb2dpbi9wYWdlLnRzeD80OTBhIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogTG9naW4gUGFnZSAtIFByb2Zlc3Npb25hbCBpbXBsZW1lbnRhdGlvbiB3aXRoIFJlYWN0IFF1ZXJ5XG4gKlxuICogRmVhdHVyZXM6XG4gKiAtIEZvcm0gaGFuZGxpbmcgd2l0aCB2YWxpZGF0aW9uXG4gKiAtIFJlYWN0IFF1ZXJ5IGF1dGggaG9va3NcbiAqIC0gUHJvcGVyIGVycm9yIGhhbmRsaW5nIGFuZCB1c2VyIGZlZWRiYWNrXG4gKiAtIFJlZGlyZWN0IGFmdGVyIHN1Y2Nlc3NmdWwgbG9naW5cbiAqIC0gQ29va2llLWJhc2VkIGF1dGhlbnRpY2F0aW9uXG4gKi9cblxuJ3VzZSBjbGllbnQnO1xuXG5pbXBvcnQgeyB1c2VSb3V0ZXIsIHVzZVNlYXJjaFBhcmFtcyB9IGZyb20gJ25leHQvbmF2aWdhdGlvbic7XG5pbXBvcnQgeyB1c2VFZmZlY3QsIHVzZVN0YXRlIH0gZnJvbSAncmVhY3QnO1xuXG5pbXBvcnQgeyB1c2VJc0F1dGhlbnRpY2F0ZWQsIHVzZUxvZ2luIH0gZnJvbSAnQC9ob29rcy91c2VBdXRoUXVlcnknO1xuXG4vLyBEZW1vIGNyZWRlbnRpYWxzIGZvciB0ZXN0aW5nXG5jb25zdCBERU1PX0NSRURFTlRJQUxTID0ge1xuICB1c2VybmFtZTogJ2FkbWluQHNjaG9vbC5lZHUnLFxuICBwYXNzd29yZDogJ2FkbWluMTIzJyxcbn07XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIExvZ2luUGFnZSgpIHtcbiAgY29uc3QgW3VzZXJuYW1lLCBzZXRVc2VybmFtZV0gPSB1c2VTdGF0ZSgnJyk7XG4gIGNvbnN0IFtwYXNzd29yZCwgc2V0UGFzc3dvcmRdID0gdXNlU3RhdGUoJycpO1xuICBjb25zdCBbcmVtZW1iZXJNZSwgc2V0UmVtZW1iZXJNZV0gPSB1c2VTdGF0ZShmYWxzZSk7XG5cbiAgY29uc3Qgcm91dGVyID0gdXNlUm91dGVyKCk7XG4gIGNvbnN0IHNlYXJjaFBhcmFtcyA9IHVzZVNlYXJjaFBhcmFtcygpO1xuICBjb25zdCByZWRpcmVjdFRvID0gc2VhcmNoUGFyYW1zLmdldCgncmVkaXJlY3QnKSB8fCAnL2Rhc2hib2FyZCc7XG5cbiAgY29uc3QgbG9naW5NdXRhdGlvbiA9IHVzZUxvZ2luKHtcbiAgICBvblN1Y2Nlc3M6ICgpID0+IHtcbiAgICAgIHJvdXRlci5wdXNoKHJlZGlyZWN0VG8pO1xuICAgIH0sXG4gIH0pO1xuXG4gIGNvbnN0IHsgaXNBdXRoZW50aWNhdGVkLCBpc0xvYWRpbmc6IGF1dGhMb2FkaW5nIH0gPSB1c2VJc0F1dGhlbnRpY2F0ZWQoKTtcblxuICAvLyBSZWRpcmVjdCBpZiBhbHJlYWR5IGF1dGhlbnRpY2F0ZWRcbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBpZiAoaXNBdXRoZW50aWNhdGVkICYmICFhdXRoTG9hZGluZykge1xuICAgICAgcm91dGVyLnB1c2gocmVkaXJlY3RUbyk7XG4gICAgfVxuICB9LCBbaXNBdXRoZW50aWNhdGVkLCBhdXRoTG9hZGluZywgcm91dGVyLCByZWRpcmVjdFRvXSk7XG5cbiAgY29uc3QgaGFuZGxlTG9naW4gPSBhc3luYyAoZTogUmVhY3QuRm9ybUV2ZW50KSA9PiB7XG4gICAgZS5wcmV2ZW50RGVmYXVsdCgpO1xuXG4gICAgaWYgKCF1c2VybmFtZSB8fCAhcGFzc3dvcmQpIHtcbiAgICAgIHJldHVybjtcbiAgICB9XG5cbiAgICBsb2dpbk11dGF0aW9uLm11dGF0ZSh7XG4gICAgICB1c2VybmFtZSxcbiAgICAgIHBhc3N3b3JkLFxuICAgIH0pO1xuICB9O1xuXG4gIGNvbnN0IGZpbGxEZW1vQ3JlZGVudGlhbHMgPSAoKSA9PiB7XG4gICAgc2V0VXNlcm5hbWUoREVNT19DUkVERU5USUFMUy51c2VybmFtZSk7XG4gICAgc2V0UGFzc3dvcmQoREVNT19DUkVERU5USUFMUy5wYXNzd29yZCk7XG4gIH07XG5cbiAgcmV0dXJuIChcbiAgICA8ZGl2IGNsYXNzTmFtZT0nbWluLWgtc2NyZWVuIGJnLWdyYWRpZW50LXRvLWJyIGZyb20tYmx1ZS01MCB2aWEtd2hpdGUgdG8taW5kaWdvLTUwIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIHAtNCc+XG4gICAgICA8ZGl2IGNsYXNzTmFtZT0ndy1mdWxsIG1heC13LW1kJz5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9J2JnLXdoaXRlLzgwIGJhY2tkcm9wLWJsdXItc20gc2hhZG93LTJ4bCByb3VuZGVkLTJ4bCBib3JkZXItMCBwLTgnPlxuICAgICAgICAgIHsvKiBIZWFkZXIgKi99XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9J3RleHQtY2VudGVyIG1iLTgnPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9J214LWF1dG8gdy0xNiBoLTE2IGJnLWJsdWUtNjAwIHJvdW5kZWQtZnVsbCBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciBtYi00Jz5cbiAgICAgICAgICAgICAgPHN2Z1xuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT0ndy04IGgtOCB0ZXh0LXdoaXRlJ1xuICAgICAgICAgICAgICAgIGZpbGw9J25vbmUnXG4gICAgICAgICAgICAgICAgc3Ryb2tlPSdjdXJyZW50Q29sb3InXG4gICAgICAgICAgICAgICAgdmlld0JveD0nMCAwIDI0IDI0J1xuICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgPHBhdGhcbiAgICAgICAgICAgICAgICAgIHN0cm9rZUxpbmVjYXA9J3JvdW5kJ1xuICAgICAgICAgICAgICAgICAgc3Ryb2tlTGluZWpvaW49J3JvdW5kJ1xuICAgICAgICAgICAgICAgICAgc3Ryb2tlV2lkdGg9ezJ9XG4gICAgICAgICAgICAgICAgICBkPSdNMTIgMTRsOS01LTktNS05IDUgOSA1eidcbiAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgIDxwYXRoXG4gICAgICAgICAgICAgICAgICBzdHJva2VMaW5lY2FwPSdyb3VuZCdcbiAgICAgICAgICAgICAgICAgIHN0cm9rZUxpbmVqb2luPSdyb3VuZCdcbiAgICAgICAgICAgICAgICAgIHN0cm9rZVdpZHRoPXsyfVxuICAgICAgICAgICAgICAgICAgZD0nTTEyIDE0bDYuMTYtMy40MjJhMTIuMDgzIDEyLjA4MyAwIDAxLjY2NSA2LjQ3OUExMS45NTIgMTEuOTUyIDAgMDAxMiAyMC4wNTVhMTEuOTUyIDExLjk1MiAwIDAwLTYuODI0LTIuOTk4IDEyLjA3OCAxMi4wNzggMCAwMS42NjUtNi40NzlMMTIgMTR6J1xuICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgPHBhdGhcbiAgICAgICAgICAgICAgICAgIHN0cm9rZUxpbmVjYXA9J3JvdW5kJ1xuICAgICAgICAgICAgICAgICAgc3Ryb2tlTGluZWpvaW49J3JvdW5kJ1xuICAgICAgICAgICAgICAgICAgc3Ryb2tlV2lkdGg9ezJ9XG4gICAgICAgICAgICAgICAgICBkPSdNMTIgMTRsOS01LTktNS05IDUgOSA1em0wIDBsNi4xNi0zLjQyMmExMi4wODMgMTIuMDgzIDAgMDEuNjY1IDYuNDc5QTExLjk1MiAxMS45NTIgMCAwMDEyIDIwLjA1NWExMS45NTIgMTEuOTUyIDAgMDAtNi44MjQtMi45OTggMTIuMDc4IDEyLjA3OCAwIDAxLjY2NS02LjQ3OUwxMiAxNHonXG4gICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgPC9zdmc+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDxoMSBjbGFzc05hbWU9J3RleHQtMnhsIGZvbnQtYm9sZCB0ZXh0LWdyYXktOTAwJz5TY2hvb2wgTWFuYWdlbWVudDwvaDE+XG4gICAgICAgICAgICA8cCBjbGFzc05hbWU9J210LTIgdGV4dC1ncmF5LTYwMCc+U2lnbiBpbiB0byB5b3VyIGFjY291bnQ8L3A+XG4gICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICB7LyogRGVtbyBjcmVkZW50aWFscyBpbmZvICovfVxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPSdiZy15ZWxsb3ctNTAgYm9yZGVyIGJvcmRlci15ZWxsb3ctMjAwIHJvdW5kZWQtbGcgcC00IG1iLTYnPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9J3RleHQtc20gdGV4dC15ZWxsb3ctODAwJz5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9J2ZsZXgganVzdGlmeS1iZXR3ZWVuIGl0ZW1zLWNlbnRlciBtYi0yJz5cbiAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9J2ZvbnQtbWVkaXVtJz5EZW1vIENyZWRlbnRpYWxzOjwvcD5cbiAgICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgICB0eXBlPSdidXR0b24nXG4gICAgICAgICAgICAgICAgICBvbkNsaWNrPXtmaWxsRGVtb0NyZWRlbnRpYWxzfVxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPSd0ZXh0LXhzIGJnLXllbGxvdy0yMDAgaG92ZXI6YmcteWVsbG93LTMwMCBweC0yIHB5LTEgcm91bmRlZCB0cmFuc2l0aW9uLWNvbG9ycydcbiAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICBGaWxsIEZvcm1cbiAgICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDxwPlVzZXJuYW1lOiB7REVNT19DUkVERU5USUFMUy51c2VybmFtZX08L3A+XG4gICAgICAgICAgICAgIDxwPlBhc3N3b3JkOiB7REVNT19DUkVERU5USUFMUy5wYXNzd29yZH08L3A+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgIHsvKiBMb2dpbiBGb3JtICovfVxuICAgICAgICAgIDxmb3JtIGNsYXNzTmFtZT0nc3BhY2UteS02JyBvblN1Ym1pdD17aGFuZGxlTG9naW59PlxuICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgPGxhYmVsIGh0bWxGb3I9J3VzZXJuYW1lJyBjbGFzc05hbWU9J2Jsb2NrIHRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1ncmF5LTcwMCBtYi0yJz5cbiAgICAgICAgICAgICAgICBFbWFpbCBBZGRyZXNzXG4gICAgICAgICAgICAgIDwvbGFiZWw+XG4gICAgICAgICAgICAgIDxpbnB1dFxuICAgICAgICAgICAgICAgIGlkPSd1c2VybmFtZSdcbiAgICAgICAgICAgICAgICBuYW1lPSd1c2VybmFtZSdcbiAgICAgICAgICAgICAgICB0eXBlPSd0ZXh0J1xuICAgICAgICAgICAgICAgIGF1dG9Db21wbGV0ZT0ndXNlcm5hbWUnXG4gICAgICAgICAgICAgICAgcmVxdWlyZWRcbiAgICAgICAgICAgICAgICB2YWx1ZT17dXNlcm5hbWV9XG4gICAgICAgICAgICAgICAgb25DaGFuZ2U9e2UgPT4gc2V0VXNlcm5hbWUoZS50YXJnZXQudmFsdWUpfVxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT0ndy1mdWxsIGgtMTIgcHgtNCBib3JkZXIgYm9yZGVyLWdyYXktMjAwIHJvdW5kZWQtbGcgcGxhY2Vob2xkZXItZ3JheS00MDAgZm9jdXM6b3V0bGluZS1ub25lIGZvY3VzOnJpbmctMiBmb2N1czpyaW5nLWJsdWUtNTAwIGZvY3VzOmJvcmRlci1ibHVlLTUwMCB0cmFuc2l0aW9uLWNvbG9ycydcbiAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj0nYWRtaW5Ac2Nob29sLmNvbSdcbiAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICA8bGFiZWwgaHRtbEZvcj0ncGFzc3dvcmQnIGNsYXNzTmFtZT0nYmxvY2sgdGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWdyYXktNzAwIG1iLTInPlxuICAgICAgICAgICAgICAgIFBhc3N3b3JkXG4gICAgICAgICAgICAgIDwvbGFiZWw+XG4gICAgICAgICAgICAgIDxpbnB1dFxuICAgICAgICAgICAgICAgIGlkPSdwYXNzd29yZCdcbiAgICAgICAgICAgICAgICBuYW1lPSdwYXNzd29yZCdcbiAgICAgICAgICAgICAgICB0eXBlPSdwYXNzd29yZCdcbiAgICAgICAgICAgICAgICBhdXRvQ29tcGxldGU9J2N1cnJlbnQtcGFzc3dvcmQnXG4gICAgICAgICAgICAgICAgcmVxdWlyZWRcbiAgICAgICAgICAgICAgICB2YWx1ZT17cGFzc3dvcmR9XG4gICAgICAgICAgICAgICAgb25DaGFuZ2U9e2UgPT4gc2V0UGFzc3dvcmQoZS50YXJnZXQudmFsdWUpfVxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT0ndy1mdWxsIGgtMTIgcHgtNCBib3JkZXIgYm9yZGVyLWdyYXktMjAwIHJvdW5kZWQtbGcgcGxhY2Vob2xkZXItZ3JheS00MDAgZm9jdXM6b3V0bGluZS1ub25lIGZvY3VzOnJpbmctMiBmb2N1czpyaW5nLWJsdWUtNTAwIGZvY3VzOmJvcmRlci1ibHVlLTUwMCB0cmFuc2l0aW9uLWNvbG9ycydcbiAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj0nRW50ZXIgeW91ciBwYXNzd29yZCdcbiAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT0nZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuJz5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9J2ZsZXggaXRlbXMtY2VudGVyJz5cbiAgICAgICAgICAgICAgICA8aW5wdXRcbiAgICAgICAgICAgICAgICAgIGlkPSdyZW1lbWJlci1tZSdcbiAgICAgICAgICAgICAgICAgIG5hbWU9J3JlbWVtYmVyLW1lJ1xuICAgICAgICAgICAgICAgICAgdHlwZT0nY2hlY2tib3gnXG4gICAgICAgICAgICAgICAgICBjaGVja2VkPXtyZW1lbWJlck1lfVxuICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9e2UgPT4gc2V0UmVtZW1iZXJNZShlLnRhcmdldC5jaGVja2VkKX1cbiAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT0naC00IHctNCB0ZXh0LWJsdWUtNjAwIGZvY3VzOnJpbmctYmx1ZS01MDAgYm9yZGVyLWdyYXktMzAwIHJvdW5kZWQnXG4gICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICA8bGFiZWwgaHRtbEZvcj0ncmVtZW1iZXItbWUnIGNsYXNzTmFtZT0nbWwtMiBibG9jayB0ZXh0LXNtIHRleHQtZ3JheS05MDAnPlxuICAgICAgICAgICAgICAgICAgUmVtZW1iZXIgbWVcbiAgICAgICAgICAgICAgICA8L2xhYmVsPlxuICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT0ndGV4dC1zbSc+XG4gICAgICAgICAgICAgICAgPGFcbiAgICAgICAgICAgICAgICAgIGhyZWY9JyMnXG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9J2ZvbnQtbWVkaXVtIHRleHQtYmx1ZS02MDAgaG92ZXI6dGV4dC1ibHVlLTUwMCB0cmFuc2l0aW9uLWNvbG9ycydcbiAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICBGb3Jnb3QgeW91ciBwYXNzd29yZD9cbiAgICAgICAgICAgICAgICA8L2E+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgIHsvKiBFcnJvciBNZXNzYWdlICovfVxuICAgICAgICAgICAge2xvZ2luTXV0YXRpb24uZXJyb3IgJiYgKFxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT0nYmctcmVkLTUwIGJvcmRlciBib3JkZXItcmVkLTIwMCByb3VuZGVkLWxnIHAtNCc+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9J3RleHQtc20gdGV4dC1yZWQtODAwJz5cbiAgICAgICAgICAgICAgICAgIHtsb2dpbk11dGF0aW9uLmVycm9yIGluc3RhbmNlb2YgRXJyb3JcbiAgICAgICAgICAgICAgICAgICAgPyBsb2dpbk11dGF0aW9uLmVycm9yLm1lc3NhZ2VcbiAgICAgICAgICAgICAgICAgICAgOiAnTG9naW4gZmFpbGVkLiBQbGVhc2UgdHJ5IGFnYWluLid9XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgKX1cblxuICAgICAgICAgICAgey8qIFN1Ym1pdCBCdXR0b24gKi99XG4gICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgIHR5cGU9J3N1Ym1pdCdcbiAgICAgICAgICAgICAgZGlzYWJsZWQ9e2xvZ2luTXV0YXRpb24uaXNQZW5kaW5nIHx8ICF1c2VybmFtZSB8fCAhcGFzc3dvcmR9XG4gICAgICAgICAgICAgIGNsYXNzTmFtZT0ndy1mdWxsIGgtMTIgYmctYmx1ZS02MDAgaG92ZXI6YmctYmx1ZS03MDAgdGV4dC13aGl0ZSBmb250LW1lZGl1bSByb3VuZGVkLWxnIHRyYW5zaXRpb24tY29sb3JzIGZvY3VzOm91dGxpbmUtbm9uZSBmb2N1czpyaW5nLTIgZm9jdXM6cmluZy1vZmZzZXQtMiBmb2N1czpyaW5nLWJsdWUtNTAwIGRpc2FibGVkOm9wYWNpdHktNTAgZGlzYWJsZWQ6Y3Vyc29yLW5vdC1hbGxvd2VkJ1xuICAgICAgICAgICAgPlxuICAgICAgICAgICAgICB7bG9naW5NdXRhdGlvbi5pc1BlbmRpbmcgPyAoXG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9J2ZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyJz5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPSdhbmltYXRlLXNwaW4gcm91bmRlZC1mdWxsIGgtNCB3LTQgYm9yZGVyLWItMiBib3JkZXItd2hpdGUgbXItMicgLz5cbiAgICAgICAgICAgICAgICAgIFNpZ25pbmcgaW4uLi5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgKSA6IChcbiAgICAgICAgICAgICAgICAnU2lnbiBJbidcbiAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgIDwvZm9ybT5cblxuICAgICAgICAgIHsvKiBEZXZlbG9wbWVudCBNb2RlIEluZGljYXRvciAqL31cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT0ndGV4dC1jZW50ZXIgbXQtNic+XG4gICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9J3B4LTMgcHktMSBiZy1ncmVlbi0xMDAgdGV4dC1ncmVlbi04MDAgdGV4dC14cyByb3VuZGVkLWZ1bGwnPlxuICAgICAgICAgICAgICBQcm9kdWN0aW9uIFJlYWR5IC0gQ29va2llLWJhc2VkIEF1dGhcbiAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgIHsvKiBBZGRpdGlvbmFsIGxpbmtzICovfVxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPSd0ZXh0LWNlbnRlciB0ZXh0LXNtIHRleHQtZ3JheS02MDAgbXQtNic+XG4gICAgICAgICAgICA8cD5cbiAgICAgICAgICAgICAgRG9uJ3QgaGF2ZSBhbiBhY2NvdW50P3snICd9XG4gICAgICAgICAgICAgIDxhXG4gICAgICAgICAgICAgICAgaHJlZj0nIydcbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9J2ZvbnQtbWVkaXVtIHRleHQtYmx1ZS02MDAgaG92ZXI6dGV4dC1ibHVlLTUwMCB0cmFuc2l0aW9uLWNvbG9ycydcbiAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgIENvbnRhY3QgYWRtaW5pc3RyYXRvclxuICAgICAgICAgICAgICA8L2E+XG4gICAgICAgICAgICA8L3A+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9kaXY+XG4gICAgPC9kaXY+XG4gICk7XG59XG4iXSwibmFtZXMiOlsidXNlUm91dGVyIiwidXNlU2VhcmNoUGFyYW1zIiwidXNlRWZmZWN0IiwidXNlU3RhdGUiLCJ1c2VJc0F1dGhlbnRpY2F0ZWQiLCJ1c2VMb2dpbiIsIkRFTU9fQ1JFREVOVElBTFMiLCJ1c2VybmFtZSIsInBhc3N3b3JkIiwiTG9naW5QYWdlIiwic2V0VXNlcm5hbWUiLCJzZXRQYXNzd29yZCIsInJlbWVtYmVyTWUiLCJzZXRSZW1lbWJlck1lIiwicm91dGVyIiwic2VhcmNoUGFyYW1zIiwicmVkaXJlY3RUbyIsImdldCIsImxvZ2luTXV0YXRpb24iLCJvblN1Y2Nlc3MiLCJwdXNoIiwiaXNBdXRoZW50aWNhdGVkIiwiaXNMb2FkaW5nIiwiYXV0aExvYWRpbmciLCJoYW5kbGVMb2dpbiIsImUiLCJwcmV2ZW50RGVmYXVsdCIsIm11dGF0ZSIsImZpbGxEZW1vQ3JlZGVudGlhbHMiLCJkaXYiLCJjbGFzc05hbWUiLCJzdmciLCJmaWxsIiwic3Ryb2tlIiwidmlld0JveCIsInBhdGgiLCJzdHJva2VMaW5lY2FwIiwic3Ryb2tlTGluZWpvaW4iLCJzdHJva2VXaWR0aCIsImQiLCJoMSIsInAiLCJidXR0b24iLCJ0eXBlIiwib25DbGljayIsImZvcm0iLCJvblN1Ym1pdCIsImxhYmVsIiwiaHRtbEZvciIsImlucHV0IiwiaWQiLCJuYW1lIiwiYXV0b0NvbXBsZXRlIiwicmVxdWlyZWQiLCJ2YWx1ZSIsIm9uQ2hhbmdlIiwidGFyZ2V0IiwicGxhY2Vob2xkZXIiLCJjaGVja2VkIiwiYSIsImhyZWYiLCJlcnJvciIsIkVycm9yIiwibWVzc2FnZSIsImRpc2FibGVkIiwiaXNQZW5kaW5nIiwic3BhbiJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./app/login/page.tsx\n");

/***/ }),

/***/ "(ssr)/./components/providers.tsx":
/*!**********************************!*\
  !*** ./components/providers.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Providers: () => (/* binding */ Providers)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @tanstack/react-query */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/queryClient.js\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @tanstack/react-query */ \"(ssr)/./node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js\");\n/* harmony import */ var _tanstack_react_query_devtools__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @tanstack/react-query-devtools */ \"(ssr)/./node_modules/@tanstack/react-query-devtools/build/modern/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_providers_AuthProvider__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/providers/AuthProvider */ \"(ssr)/./components/providers/AuthProvider.tsx\");\n/**\n * Providers Component\n *\n * Wraps the app with all necessary providers:\n * - React Query for data fetching\n * - Theme provider for dark/light mode\n * - Auth provider for authentication state\n */ /* __next_internal_client_entry_do_not_use__ Providers auto */ \n\n\n\n\nfunction Providers({ children }) {\n    // Create a new QueryClient instance\n    const [queryClient] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(()=>new _tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.QueryClient({\n            defaultOptions: {\n                queries: {\n                    // Stale time: 5 minutes\n                    staleTime: 5 * 60 * 1000,\n                    // Cache time: 10 minutes\n                    gcTime: 10 * 60 * 1000,\n                    // Retry failed requests 3 times\n                    retry: 3,\n                    // Retry delay with exponential backoff\n                    retryDelay: (attemptIndex)=>Math.min(1000 * 2 ** attemptIndex, 30000),\n                    // Refetch on window focus in production\n                    refetchOnWindowFocus: \"development\" === \"production\",\n                    // Don't refetch on reconnect in development\n                    refetchOnReconnect: \"development\" === \"production\"\n                },\n                mutations: {\n                    // Retry failed mutations once\n                    retry: 1,\n                    // Retry delay for mutations\n                    retryDelay: 1000\n                }\n            }\n        }));\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_tanstack_react_query__WEBPACK_IMPORTED_MODULE_4__.QueryClientProvider, {\n        client: queryClient,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_providers_AuthProvider__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n            children: [\n                children,\n                 true && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_tanstack_react_query_devtools__WEBPACK_IMPORTED_MODULE_5__.ReactQueryDevtools, {\n                    initialIsOpen: false\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Pictures\\\\school_pro\\\\school-pro-backend\\\\frontend-integration\\\\components\\\\providers.tsx\",\n                    lineNumber: 53,\n                    columnNumber: 52\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Pictures\\\\school_pro\\\\school-pro-backend\\\\frontend-integration\\\\components\\\\providers.tsx\",\n            lineNumber: 50,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Pictures\\\\school_pro\\\\school-pro-backend\\\\frontend-integration\\\\components\\\\providers.tsx\",\n        lineNumber: 49,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/providers.tsx\n");

/***/ }),

/***/ "(ssr)/./components/providers/AuthProvider.tsx":
/*!***********************************************!*\
  !*** ./components/providers/AuthProvider.tsx ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _hooks_useAuthInitializer__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/hooks/useAuthInitializer */ \"(ssr)/./hooks/useAuthInitializer.ts\");\n/**\n * Auth Provider Component\n * Initializes authentication state and provides loading states\n */ /* __next_internal_client_entry_do_not_use__ AuthProvider,default auto */ \n\n\nconst AuthProvider = ({ children })=>{\n    const { isInitialized, isLoading } = (0,_hooks_useAuthInitializer__WEBPACK_IMPORTED_MODULE_2__.useAuthInitializer)();\n    // Show loading spinner while auth is initializing\n    if (!isInitialized && isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Pictures\\\\school_pro\\\\school-pro-backend\\\\frontend-integration\\\\components\\\\providers\\\\AuthProvider.tsx\",\n                        lineNumber: 23,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: \"Initializing authentication...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Pictures\\\\school_pro\\\\school-pro-backend\\\\frontend-integration\\\\components\\\\providers\\\\AuthProvider.tsx\",\n                        lineNumber: 24,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Pictures\\\\school_pro\\\\school-pro-backend\\\\frontend-integration\\\\components\\\\providers\\\\AuthProvider.tsx\",\n                lineNumber: 22,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Pictures\\\\school_pro\\\\school-pro-backend\\\\frontend-integration\\\\components\\\\providers\\\\AuthProvider.tsx\",\n            lineNumber: 21,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: children\n    }, void 0, false);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (AuthProvider);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/providers/AuthProvider.tsx\n");

/***/ }),

/***/ "(ssr)/./hooks/useAuthInitializer.ts":
/*!*************************************!*\
  !*** ./hooks/useAuthInitializer.ts ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   useAuthInitializer: () => (/* binding */ useAuthInitializer)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _stores_authStore__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/stores/authStore */ \"(ssr)/./stores/authStore.ts\");\n/**\n * Auth Initializer Hook\n * Automatically fetches user data when the app loads if a token exists\n */ /* __next_internal_client_entry_do_not_use__ useAuthInitializer,default auto */ \n\nconst useAuthInitializer = ()=>{\n    const { token, user, fetchUser, isLoading } = (0,_stores_authStore__WEBPACK_IMPORTED_MODULE_1__.useAuthStore)();\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        // If we have a token but no user data, fetch it\n        if (token && !user && !isLoading) {\n            console.log(\"\\uD83D\\uDD04 Auth initializer: Token found but no user data, fetching...\");\n            fetchUser();\n        }\n    }, [\n        token,\n        user,\n        fetchUser,\n        isLoading\n    ]);\n    // Also check localStorage on mount (in case store hasn't hydrated yet)\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        const checkStoredToken = ()=>{\n            if (true) return;\n            const storedToken = localStorage.getItem(\"access_token\" || 0);\n            const storedRole = localStorage.getItem(\"role\" || 0);\n            // If we have stored auth data but store is empty, restore it\n            if (storedToken && storedRole && !token) {\n                console.log(\"\\uD83D\\uDD04 Auth initializer: Restoring auth from localStorage\");\n                const { setAuth } = _stores_authStore__WEBPACK_IMPORTED_MODULE_1__.useAuthStore.getState();\n                setAuth({\n                    token: storedToken,\n                    role: storedRole\n                });\n            }\n        };\n        // Small delay to ensure store has hydrated\n        const timer = setTimeout(checkStoredToken, 100);\n        return ()=>clearTimeout(timer);\n    }, [\n        token\n    ]);\n    return {\n        isInitialized: !isLoading && (!!user || !token),\n        isLoading,\n        hasToken: !!token,\n        hasUser: !!user\n    };\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (useAuthInitializer);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./hooks/useAuthInitializer.ts\n");

/***/ }),

/***/ "(ssr)/./hooks/useAuthQuery.ts":
/*!*******************************!*\
  !*** ./hooks/useAuthQuery.ts ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authKeys: () => (/* binding */ authKeys),\n/* harmony export */   useChangePassword: () => (/* binding */ useChangePassword),\n/* harmony export */   useCurrentUser: () => (/* binding */ useCurrentUser),\n/* harmony export */   useIsAuthenticated: () => (/* binding */ useIsAuthenticated),\n/* harmony export */   useLogin: () => (/* binding */ useLogin),\n/* harmony export */   useLogout: () => (/* binding */ useLogout),\n/* harmony export */   useMe: () => (/* binding */ useMe),\n/* harmony export */   useRegister: () => (/* binding */ useRegister),\n/* harmony export */   useUpdateMe: () => (/* binding */ useUpdateMe)\n/* harmony export */ });\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @tanstack/react-query */ \"(ssr)/./node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @tanstack/react-query */ \"(ssr)/./node_modules/@tanstack/react-query/build/modern/useMutation.js\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @tanstack/react-query */ \"(ssr)/./node_modules/@tanstack/react-query/build/modern/useQuery.js\");\n/* harmony import */ var _services_auth__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/services/auth */ \"(ssr)/./services/auth.ts\");\n/* harmony import */ var _stores_authStore__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/stores/authStore */ \"(ssr)/./stores/authStore.ts\");\n/**\n * Authentication React Query Hooks\n *\n * Stable query keys and proper cache management\n */ \n\n\n// ============================================================================\n// QUERY KEYS\n// ============================================================================\nconst authKeys = {\n    all: [\n        \"auth\"\n    ],\n    me: ()=>[\n            ...authKeys.all,\n            \"me\"\n        ]\n};\n// ============================================================================\n// AUTH HOOKS\n// ============================================================================\n/**\n * Login mutation\n */ // JWT token decoder utility\nconst decodeJWT = (token)=>{\n    try {\n        const payload = JSON.parse(atob(token.split(\".\")[1]));\n        return payload;\n    } catch (error) {\n        console.error(\"Failed to decode JWT token:\", error);\n        return null;\n    }\n};\nfunction useLogin(options) {\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__.useQueryClient)();\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.useMutation)({\n        mutationFn: _services_auth__WEBPACK_IMPORTED_MODULE_0__.login,\n        onSuccess: async (data)=>{\n            console.log(\"\\uD83D\\uDD10 Login successful, processing token...\", data);\n            // Extract role from JWT token\n            let role = null;\n            let userId = null;\n            try {\n                const payload = JSON.parse(atob(data.access_token.split(\".\")[1]));\n                role = payload.role;\n                userId = payload.sub || payload.user_id;\n                console.log(\"\\uD83D\\uDD0D Extracted from JWT:\", {\n                    role,\n                    userId\n                });\n            } catch (error) {\n                console.warn(\"⚠️ Failed to decode JWT token:\", error);\n            }\n            // Use Zustand store to handle login\n            const { setAuth } = _stores_authStore__WEBPACK_IMPORTED_MODULE_1__.useAuthStore.getState();\n            setAuth({\n                token: data.access_token,\n                role,\n                userId\n            });\n            // Set cookie via session API (for SSR compatibility)\n            try {\n                await fetch(\"/api/session/login\", {\n                    method: \"POST\",\n                    headers: {\n                        \"Content-Type\": \"application/json\"\n                    },\n                    body: JSON.stringify({\n                        token: data.access_token\n                    })\n                });\n                console.log(\"\\uD83C\\uDF6A Session cookie set successfully\");\n            } catch (error) {\n                console.warn(\"⚠️ Failed to set session cookie:\", error);\n            // Don't fail the login if cookie setting fails\n            }\n            // Invalidate me query to refetch user data\n            queryClient.invalidateQueries({\n                queryKey: authKeys.me()\n            });\n            options?.onSuccess?.(data);\n        },\n        onError: options?.onError\n    });\n}\n/**\n * Logout mutation\n */ function useLogout(options) {\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__.useQueryClient)();\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.useMutation)({\n        mutationFn: _services_auth__WEBPACK_IMPORTED_MODULE_0__.logout,\n        onSuccess: async ()=>{\n            console.log(\"\\uD83D\\uDEAA Logout successful, clearing stored data...\");\n            // Use Zustand store to handle logout (includes localStorage and cookie cleanup)\n            const { clear } = _stores_authStore__WEBPACK_IMPORTED_MODULE_1__.useAuthStore.getState();\n            clear();\n            // Clear cookie via session API\n            try {\n                await fetch(\"/api/session/logout\", {\n                    method: \"POST\"\n                });\n                console.log(\"\\uD83C\\uDF6A Session cookie cleared\");\n            } catch (error) {\n                console.warn(\"⚠️ Failed to clear session cookie:\", error);\n            }\n            // Clear all auth-related queries\n            queryClient.removeQueries({\n                queryKey: authKeys.all\n            });\n            options?.onSuccess?.();\n        },\n        onError: options?.onError\n    });\n}\n/**\n * Get current user query\n */ function useMe(options) {\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_4__.useQuery)({\n        queryKey: authKeys.me(),\n        queryFn: _services_auth__WEBPACK_IMPORTED_MODULE_0__.me,\n        staleTime: options?.staleTime ?? 10000,\n        retry: options?.retry ?? 0,\n        enabled: options?.enabled\n    });\n}\n/**\n * Update current user mutation\n */ function useUpdateMe() {\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__.useQueryClient)();\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.useMutation)({\n        mutationFn: _services_auth__WEBPACK_IMPORTED_MODULE_0__.updateMe,\n        onSuccess: (updatedUser)=>{\n            // Update the me query cache\n            queryClient.setQueryData(authKeys.me(), updatedUser);\n        }\n    });\n}\n/**\n * Change password mutation\n */ function useChangePassword() {\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.useMutation)({\n        mutationFn: _services_auth__WEBPACK_IMPORTED_MODULE_0__.changePassword\n    });\n}\n/**\n * Register mutation (only available in users namespace)\n */ function useRegister(options) {\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__.useQueryClient)();\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.useMutation)({\n        mutationFn: _services_auth__WEBPACK_IMPORTED_MODULE_0__.register,\n        onSuccess: async (data)=>{\n            // Set cookie via session API\n            await fetch(\"/api/session/login\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    token: data.access_token\n                })\n            });\n            // Invalidate me query to refetch user data\n            queryClient.invalidateQueries({\n                queryKey: authKeys.me()\n            });\n            options?.onSuccess?.(data);\n        },\n        onError: options?.onError\n    });\n}\n// ============================================================================\n// UTILITY HOOKS\n// ============================================================================\n/**\n * Check if user is authenticated\n */ function useIsAuthenticated() {\n    const { data: user, isLoading } = useMe({\n        enabled: true\n    });\n    return {\n        isAuthenticated: !!user,\n        isLoading,\n        user\n    };\n}\n/**\n * Get current user with loading state\n */ function useCurrentUser() {\n    return useMe({\n        enabled: true\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./hooks/useAuthQuery.ts\n");

/***/ }),

/***/ "(ssr)/./lib/auth-config.ts":
/*!****************************!*\
  !*** ./lib/auth-config.ts ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AUTH_CONFIG: () => (/* binding */ AUTH_CONFIG),\n/* harmony export */   AUTH_ENDPOINTS: () => (/* binding */ AUTH_ENDPOINTS),\n/* harmony export */   USERS_ENDPOINTS: () => (/* binding */ USERS_ENDPOINTS),\n/* harmony export */   buildAuthPath: () => (/* binding */ buildAuthPath),\n/* harmony export */   buildUsersPath: () => (/* binding */ buildUsersPath),\n/* harmony export */   detectAuthNamespace: () => (/* binding */ detectAuthNamespace),\n/* harmony export */   getBackendUrl: () => (/* binding */ getBackendUrl),\n/* harmony export */   validateNamespaceConfig: () => (/* binding */ validateNamespaceConfig)\n/* harmony export */ });\n/**\n * Auth Configuration and Namespace Management\n *\n * Handles the two possible backend namespaces:\n * - Mode A: /api/v1/auth/*\n * - Mode B: /api/v1/users/auth/*\n */ // Get the auth namespace from environment\nconst AUTH_NAMESPACE = \"auth\" || 0;\nconst API_BASE = \"/api/v1\" || 0;\nconst API_VERSION = process.env.NEXT_PUBLIC_API_VERSION || \"v1\";\nconst AUTH_CONFIG = {\n    namespace: AUTH_NAMESPACE,\n    apiBase: API_BASE,\n    apiVersion: API_VERSION\n};\n/**\n * Build API path based on the configured namespace\n * Note: API client now uses /api/v1 as baseURL, so we only need the endpoint path\n */ function buildAuthPath(endpoint) {\n    const cleanEndpoint = endpoint.startsWith(\"/\") ? endpoint.slice(1) : endpoint;\n    // Handle special case for no prefix\n    if (AUTH_NAMESPACE === \"none\") {\n        return `/${cleanEndpoint}`;\n    }\n    // Handle standard namespaces - no /api/v1 prefix since apiClient handles it\n    if (AUTH_NAMESPACE === \"auth\") {\n        return `/auth/${cleanEndpoint}`;\n    } else {\n        return `/users/auth/${cleanEndpoint}`;\n    }\n}\n/**\n * Build admin users path (always under users namespace)\n * Note: API client now uses /api/v1 as baseURL, so we only need the endpoint path\n */ function buildUsersPath(endpoint) {\n    const cleanEndpoint = endpoint.startsWith(\"/\") ? endpoint.slice(1) : endpoint;\n    // Handle special case for no prefix\n    if (AUTH_NAMESPACE === \"none\") {\n        return `/admin/${cleanEndpoint}`;\n    }\n    return `/users/admin/${cleanEndpoint}`;\n}\n/**\n * Get full backend URL for a path\n */ function getBackendUrl(path) {\n    const cleanPath = path.startsWith(\"/\") ? path : `/${path}`;\n    return `${API_BASE}${cleanPath}`;\n}\n/**\n * Auth endpoints configuration\n */ const AUTH_ENDPOINTS = {\n    login: buildAuthPath(\"login\"),\n    logout: buildAuthPath(\"logout\"),\n    me: buildAuthPath(\"me\"),\n    updateMe: buildAuthPath(\"me\"),\n    changePassword: buildAuthPath(\"change-password\"),\n    register: buildAuthPath(\"register\")\n};\n/**\n * Users admin endpoints configuration\n */ const USERS_ENDPOINTS = {\n    list: buildUsersPath(\"users\"),\n    get: (id)=>buildUsersPath(`users/${id}`),\n    update: (id)=>buildUsersPath(`users/${id}`),\n    deactivate: (id)=>buildUsersPath(`users/${id}/deactivate`),\n    activate: (id)=>buildUsersPath(`users/${id}/activate`),\n    stats: buildUsersPath(\"stats/users\")\n};\n/**\n * Detect which namespace is available by testing endpoints\n */ async function detectAuthNamespace() {\n    const testEndpoints = [\n        {\n            namespace: \"none\",\n            url: getBackendUrl(\"/me\")\n        },\n        {\n            namespace: \"auth\",\n            url: getBackendUrl(\"/api/v1/auth/me\")\n        },\n        {\n            namespace: \"users\",\n            url: getBackendUrl(\"/api/v1/users/auth/me\")\n        },\n        // Also test without version prefix in case backend doesn't use v1\n        {\n            namespace: \"auth-no-version\",\n            url: getBackendUrl(\"/api/auth/me\")\n        },\n        {\n            namespace: \"users-no-version\",\n            url: getBackendUrl(\"/api/users/auth/me\")\n        }\n    ];\n    const details = [];\n    let detectedNamespace = null;\n    for (const { namespace, url } of testEndpoints){\n        try {\n            console.log(`🔍 Testing ${namespace} namespace: ${url}`);\n            const response = await fetch(url, {\n                method: \"GET\",\n                headers: {\n                    Accept: \"application/json\"\n                }\n            });\n            let responseData = null;\n            try {\n                responseData = await response.json();\n            } catch (e) {\n                responseData = await response.text();\n            }\n            details.push({\n                namespace,\n                url,\n                status: response.status,\n                response: responseData\n            });\n            console.log(`📊 ${namespace} namespace result:`, {\n                status: response.status,\n                url,\n                response: responseData\n            });\n            // We expect 401 for unauthenticated requests, not 404\n            if (response.status === 401) {\n                // Map the detected namespace to the standard format\n                if (namespace === \"none\") {\n                    detectedNamespace = \"none\";\n                } else if (namespace.startsWith(\"auth\")) {\n                    detectedNamespace = \"auth\";\n                } else if (namespace.startsWith(\"users\")) {\n                    detectedNamespace = \"users\";\n                }\n                console.log(`✅ Detected working namespace: ${namespace} -> ${detectedNamespace}`);\n                break;\n            }\n        } catch (error) {\n            details.push({\n                namespace,\n                url,\n                status: \"NETWORK_ERROR\",\n                response: String(error)\n            });\n            console.error(`❌ Network error testing ${namespace}:`, error);\n        }\n    }\n    return {\n        namespace: detectedNamespace,\n        details\n    };\n}\n/**\n * Validate current namespace configuration\n */ async function validateNamespaceConfig() {\n    const detection = await detectAuthNamespace();\n    const currentNamespace = AUTH_NAMESPACE;\n    const isValid = detection.namespace === currentNamespace;\n    let suggestion;\n    if (!isValid && detection.namespace) {\n        suggestion = `Set NEXT_PUBLIC_AUTH_NAMESPACE=${detection.namespace} in your .env.local file`;\n    } else if (!detection.namespace) {\n        suggestion = \"Backend may not be running or endpoints are not available. Check the details below.\";\n    }\n    return {\n        isValid,\n        detectedNamespace: detection.namespace,\n        currentNamespace,\n        suggestion,\n        details: detection.details\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./lib/auth-config.ts\n");

/***/ }),

/***/ "(ssr)/./services/auth.ts":
/*!**************************!*\
  !*** ./services/auth.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   changePassword: () => (/* binding */ changePassword),\n/* harmony export */   login: () => (/* binding */ login),\n/* harmony export */   logout: () => (/* binding */ logout),\n/* harmony export */   me: () => (/* binding */ me),\n/* harmony export */   register: () => (/* binding */ register),\n/* harmony export */   updateMe: () => (/* binding */ updateMe)\n/* harmony export */ });\n/* harmony import */ var _api_apiClient__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/api/apiClient */ \"(ssr)/./api/apiClient.ts\");\n/* harmony import */ var _lib_auth_config__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/auth-config */ \"(ssr)/./lib/auth-config.ts\");\n/**\n * Authentication Services\n *\n * Thin service layer for auth operations.\n * Returns backend types as-is, no transformations.\n */ \n\n/**\n * Login user with credentials\n */ async function login(payload) {\n    // Backend expects application/x-www-form-urlencoded format\n    const formData = new URLSearchParams();\n    // Use username field (backend expects 'username', not 'email')\n    const username = payload.username || payload.email;\n    if (!username) {\n        throw new Error(\"Username or email is required\");\n    }\n    formData.append(\"username\", username);\n    formData.append(\"password\", payload.password);\n    const response = await _api_apiClient__WEBPACK_IMPORTED_MODULE_0__.api.post(_lib_auth_config__WEBPACK_IMPORTED_MODULE_1__.AUTH_ENDPOINTS.login, formData.toString(), {\n        headers: {\n            \"Content-Type\": \"application/x-www-form-urlencoded\"\n        }\n    });\n    return response.data;\n}\n/**\n * Logout current user\n */ async function logout() {\n    await _api_apiClient__WEBPACK_IMPORTED_MODULE_0__.api.post(_lib_auth_config__WEBPACK_IMPORTED_MODULE_1__.AUTH_ENDPOINTS.logout);\n}\n/**\n * Get current user profile\n */ async function me() {\n    const response = await _api_apiClient__WEBPACK_IMPORTED_MODULE_0__.api.get(_lib_auth_config__WEBPACK_IMPORTED_MODULE_1__.AUTH_ENDPOINTS.me);\n    return response.data;\n}\n/**\n * Update current user profile\n */ async function updateMe(data) {\n    const response = await _api_apiClient__WEBPACK_IMPORTED_MODULE_0__.api.put(_lib_auth_config__WEBPACK_IMPORTED_MODULE_1__.AUTH_ENDPOINTS.updateMe, data);\n    return response.data;\n}\n/**\n * Change current user password\n */ async function changePassword(data) {\n    await _api_apiClient__WEBPACK_IMPORTED_MODULE_0__.api.post(_lib_auth_config__WEBPACK_IMPORTED_MODULE_1__.AUTH_ENDPOINTS.changePassword, data);\n}\n/**\n * Register new user (only available in users namespace)\n */ async function register(payload) {\n    const response = await _api_apiClient__WEBPACK_IMPORTED_MODULE_0__.api.post(_lib_auth_config__WEBPACK_IMPORTED_MODULE_1__.AUTH_ENDPOINTS.register, payload);\n    return response.data;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./services/auth.ts\n");

/***/ }),

/***/ "(ssr)/./stores/authStore.ts":
/*!*****************************!*\
  !*** ./stores/authStore.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   canCreateTeacher: () => (/* binding */ canCreateTeacher),\n/* harmony export */   canDeleteTeacher: () => (/* binding */ canDeleteTeacher),\n/* harmony export */   canEditTeacher: () => (/* binding */ canEditTeacher),\n/* harmony export */   useAuth: () => (/* binding */ useAuth),\n/* harmony export */   useAuthStore: () => (/* binding */ useAuthStore)\n/* harmony export */ });\n/* harmony import */ var zustand__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! zustand */ \"(ssr)/./node_modules/zustand/esm/react.mjs\");\n/* harmony import */ var zustand_middleware__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! zustand/middleware */ \"(ssr)/./node_modules/zustand/esm/middleware.mjs\");\n/**\n * Authentication Store - Production-Grade Zustand with JWT + Cookie Support\n *\n * Features:\n * - JWT token management with persistence\n * - Role-based access control\n * - Cookie integration for SSR\n * - Clean API for auth state management\n */ /* __next_internal_client_entry_do_not_use__ useAuthStore,canCreateTeacher,canEditTeacher,canDeleteTeacher,useAuth auto */ \n\n// Cookie utilities for SSR compatibility\nconst cookieUtils = {\n    set: (name, value, days = 7)=>{\n        if (typeof document === \"undefined\") return;\n        const expires = new Date(Date.now() + days * 24 * 60 * 60 * 1000).toUTCString();\n        document.cookie = `${name}=${value}; expires=${expires}; path=/; SameSite=Lax`;\n    },\n    get: (name)=>{\n        if (typeof document === \"undefined\") return null;\n        const value = `; ${document.cookie}`;\n        const parts = value.split(`; ${name}=`);\n        if (parts.length === 2) return parts.pop()?.split(\";\").shift() || null;\n        return null;\n    },\n    remove: (name)=>{\n        if (typeof document === \"undefined\") return;\n        document.cookie = `${name}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;`;\n    }\n};\n// Create the auth store with persistence\nconst useAuthStore = (0,zustand__WEBPACK_IMPORTED_MODULE_0__.create)()((0,zustand_middleware__WEBPACK_IMPORTED_MODULE_1__.persist)((set, get)=>({\n        // Initial state\n        token: null,\n        role: null,\n        userId: null,\n        user: null,\n        isLoading: false,\n        // Set authentication data\n        setAuth: ({ token, role, userId, user })=>{\n            console.log(\"\\uD83D\\uDD10 AuthStore.setAuth called\", {\n                hasToken: !!token,\n                role,\n                userId\n            });\n            // Store in localStorage for API client\n            if (false) {}\n            // Store in cookie for SSR/middleware\n            cookieUtils.set(\"access_token\" || 0, token);\n            cookieUtils.set(\"role\" || 0, role || \"\");\n            // Update store state\n            set({\n                token,\n                role,\n                userId: userId || null,\n                user: user || null\n            });\n            console.log(\"✅ Auth data stored successfully\");\n            // Automatically fetch user data if not provided\n            if (!user && token) {\n                get().fetchUser();\n            }\n        },\n        // Fetch user data from backend\n        fetchUser: async ()=>{\n            const { token } = get();\n            if (!token) {\n                console.warn(\"⚠️ No token available for user fetch\");\n                return;\n            }\n            set({\n                isLoading: true\n            });\n            try {\n                console.log(\"\\uD83D\\uDD04 Fetching user data from backend...\");\n                // Use the backend API URL\n                const baseUrl = \"http://127.0.0.1:8000\" || 0;\n                const response = await fetch(`${baseUrl}/auth/me`, {\n                    method: \"GET\",\n                    headers: {\n                        Authorization: `Bearer ${token}`,\n                        \"Content-Type\": \"application/json\"\n                    }\n                });\n                if (response.ok) {\n                    const userData = await response.json();\n                    console.log(\"✅ User data fetched successfully:\", userData);\n                    set({\n                        user: userData,\n                        role: userData.role || get().role,\n                        userId: userData.id || get().userId,\n                        isLoading: false\n                    });\n                } else {\n                    console.error(\"❌ Failed to fetch user data:\", response.status, response.statusText);\n                    // If 401, clear auth data\n                    if (response.status === 401) {\n                        console.log(\"\\uD83D\\uDEAA Token invalid, clearing auth data\");\n                        get().clear();\n                    } else {\n                        set({\n                            isLoading: false\n                        });\n                    }\n                }\n            } catch (error) {\n                console.error(\"❌ Error fetching user data:\", error);\n                set({\n                    isLoading: false\n                });\n            }\n        },\n        // Clear authentication data\n        clear: ()=>{\n            console.log(\"\\uD83D\\uDEAA AuthStore.clear called\");\n            // Clear localStorage\n            if (false) {}\n            // Clear cookies\n            cookieUtils.remove(\"access_token\" || 0);\n            cookieUtils.remove(\"role\" || 0);\n            // Clear store state\n            set({\n                token: null,\n                role: null,\n                userId: null,\n                user: null,\n                isLoading: false\n            });\n            console.log(\"✅ Auth data cleared\");\n        },\n        // Role checking utilities\n        isAdmin: ()=>{\n            const { role } = get();\n            return role === \"ADMIN\" || role === \"SUPER_ADMIN\";\n        },\n        isSuperAdmin: ()=>{\n            const { role } = get();\n            return role === \"SUPER_ADMIN\";\n        }\n    }), {\n    name: \"auth\",\n    storage: (0,zustand_middleware__WEBPACK_IMPORTED_MODULE_1__.createJSONStorage)(()=>localStorage),\n    partialize: (state)=>({\n            token: state.token,\n            role: state.role,\n            userId: state.userId,\n            user: state.user\n        })\n}));\n// Permission utilities\nconst canCreateTeacher = (role)=>role === \"SUPER_ADMIN\" || role === \"ADMIN\";\nconst canEditTeacher = (role)=>role === \"SUPER_ADMIN\" || role === \"ADMIN\";\nconst canDeleteTeacher = (role)=>role === \"SUPER_ADMIN\" || role === \"ADMIN\";\n// Backward compatibility alias\nconst useAuth = useAuthStore;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./stores/authStore.ts\n");

/***/ }),

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"453da7028c0e\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9zY2hvb2wtbWFuYWdlbWVudC1mcm9udGVuZC8uL2FwcC9nbG9iYWxzLmNzcz81ZmQxIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiNDUzZGE3MDI4YzBlXCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/error.tsx":
/*!***********************!*\
  !*** ./app/error.tsx ***!
  \***********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\OneDrive\Pictures\school_pro\school-pro-backend\frontend-integration\app\error.tsx#default`));


/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata),\n/* harmony export */   viewport: () => (/* binding */ viewport)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _components_providers__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/providers */ \"(rsc)/./components/providers.tsx\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! sonner */ \"(rsc)/./node_modules/sonner/dist/index.mjs\");\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./app/globals.css\");\n\n\n\n\n\nconst metadata = {\n    title: \"School Management System\",\n    description: \"Comprehensive school management system for students, teachers, and administrators\",\n    keywords: [\n        \"school\",\n        \"management\",\n        \"education\",\n        \"students\",\n        \"teachers\",\n        \"administration\"\n    ],\n    authors: [\n        {\n            name: \"School Management Team\"\n        }\n    ]\n};\nconst viewport = {\n    width: \"device-width\",\n    initialScale: 1\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        suppressHydrationWarning: true,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: (next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4___default().className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_providers__WEBPACK_IMPORTED_MODULE_1__.Providers, {\n                children: [\n                    children,\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(sonner__WEBPACK_IMPORTED_MODULE_2__.Toaster, {\n                        position: \"top-right\",\n                        richColors: true,\n                        closeButton: true,\n                        toastOptions: {\n                            duration: 4000\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Pictures\\\\school_pro\\\\school-pro-backend\\\\frontend-integration\\\\app\\\\layout.tsx\",\n                        lineNumber: 27,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Pictures\\\\school_pro\\\\school-pro-backend\\\\frontend-integration\\\\app\\\\layout.tsx\",\n                lineNumber: 25,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Pictures\\\\school_pro\\\\school-pro-backend\\\\frontend-integration\\\\app\\\\layout.tsx\",\n            lineNumber: 24,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Pictures\\\\school_pro\\\\school-pro-backend\\\\frontend-integration\\\\app\\\\layout.tsx\",\n        lineNumber: 23,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./app/loading.tsx":
/*!*************************!*\
  !*** ./app/loading.tsx ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Loading)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/**\n * Global Loading Component\n * \n * This loading component is shown while pages are loading\n * Provides a consistent loading experience across the app\n */ \nfunction Loading() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen flex items-center justify-center bg-gray-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-center\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mx-auto h-16 w-16 bg-blue-600 rounded-full flex items-center justify-center mb-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-white font-bold text-2xl\",\n                        children: \"\\uD83C\\uDF93\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Pictures\\\\school_pro\\\\school-pro-backend\\\\frontend-integration\\\\app\\\\loading.tsx\",\n                        lineNumber: 13,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Pictures\\\\school_pro\\\\school-pro-backend\\\\frontend-integration\\\\app\\\\loading.tsx\",\n                    lineNumber: 12,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Pictures\\\\school_pro\\\\school-pro-backend\\\\frontend-integration\\\\app\\\\loading.tsx\",\n                    lineNumber: 17,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                    className: \"text-xl font-semibold text-gray-800 mb-2\",\n                    children: \"School Management System\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Pictures\\\\school_pro\\\\school-pro-backend\\\\frontend-integration\\\\app\\\\loading.tsx\",\n                    lineNumber: 20,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-gray-600\",\n                    children: \"Loading...\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Pictures\\\\school_pro\\\\school-pro-backend\\\\frontend-integration\\\\app\\\\loading.tsx\",\n                    lineNumber: 23,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Pictures\\\\school_pro\\\\school-pro-backend\\\\frontend-integration\\\\app\\\\loading.tsx\",\n            lineNumber: 10,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Pictures\\\\school_pro\\\\school-pro-backend\\\\frontend-integration\\\\app\\\\loading.tsx\",\n        lineNumber: 9,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvbG9hZGluZy50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQUE7Ozs7O0NBS0M7QUFDYyxTQUFTQTtJQUN0QixxQkFDRSw4REFBQ0M7UUFBSUMsV0FBVTtrQkFDYiw0RUFBQ0Q7WUFBSUMsV0FBVTs7OEJBRWIsOERBQUNEO29CQUFJQyxXQUFVOzhCQUNiLDRFQUFDQzt3QkFBS0QsV0FBVTtrQ0FBZ0M7Ozs7Ozs7Ozs7OzhCQUlsRCw4REFBQ0Q7b0JBQUlDLFdBQVU7Ozs7Ozs4QkFHZiw4REFBQ0U7b0JBQUdGLFdBQVU7OEJBQTJDOzs7Ozs7OEJBR3pELDhEQUFDRztvQkFBRUgsV0FBVTs4QkFBZ0I7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBSXJDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vc2Nob29sLW1hbmFnZW1lbnQtZnJvbnRlbmQvLi9hcHAvbG9hZGluZy50c3g/YzUyYSJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEdsb2JhbCBMb2FkaW5nIENvbXBvbmVudFxuICogXG4gKiBUaGlzIGxvYWRpbmcgY29tcG9uZW50IGlzIHNob3duIHdoaWxlIHBhZ2VzIGFyZSBsb2FkaW5nXG4gKiBQcm92aWRlcyBhIGNvbnNpc3RlbnQgbG9hZGluZyBleHBlcmllbmNlIGFjcm9zcyB0aGUgYXBwXG4gKi9cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIExvYWRpbmcoKSB7XG4gIHJldHVybiAoXG4gICAgPGRpdiBjbGFzc05hbWU9XCJtaW4taC1zY3JlZW4gZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgYmctZ3JheS01MFwiPlxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlclwiPlxuICAgICAgICB7LyogU2Nob29sIGxvZ28vaWNvbiAqL31cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJteC1hdXRvIGgtMTYgdy0xNiBiZy1ibHVlLTYwMCByb3VuZGVkLWZ1bGwgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgbWItNFwiPlxuICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtd2hpdGUgZm9udC1ib2xkIHRleHQtMnhsXCI+8J+Okzwvc3Bhbj5cbiAgICAgICAgPC9kaXY+XG4gICAgICAgIFxuICAgICAgICB7LyogTG9hZGluZyBzcGlubmVyICovfVxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImFuaW1hdGUtc3BpbiByb3VuZGVkLWZ1bGwgaC0xMiB3LTEyIGJvcmRlci1iLTIgYm9yZGVyLWJsdWUtNjAwIG14LWF1dG8gbWItNFwiPjwvZGl2PlxuICAgICAgICBcbiAgICAgICAgey8qIExvYWRpbmcgdGV4dCAqL31cbiAgICAgICAgPGgyIGNsYXNzTmFtZT1cInRleHQteGwgZm9udC1zZW1pYm9sZCB0ZXh0LWdyYXktODAwIG1iLTJcIj5cbiAgICAgICAgICBTY2hvb2wgTWFuYWdlbWVudCBTeXN0ZW1cbiAgICAgICAgPC9oMj5cbiAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTYwMFwiPkxvYWRpbmcuLi48L3A+XG4gICAgICA8L2Rpdj5cbiAgICA8L2Rpdj5cbiAgKTtcbn1cbiJdLCJuYW1lcyI6WyJMb2FkaW5nIiwiZGl2IiwiY2xhc3NOYW1lIiwic3BhbiIsImgyIiwicCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./app/loading.tsx\n");

/***/ }),

/***/ "(rsc)/./app/login/page.tsx":
/*!****************************!*\
  !*** ./app/login/page.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js\");\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\\Users\\<USER>\\OneDrive\\Pictures\\school_pro\\school-pro-backend\\frontend-integration\\app\\login\\page.tsx#default`));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(rsc)/./app/login/page.tsx\n");

/***/ }),

/***/ "(rsc)/./app/not-found.tsx":
/*!***************************!*\
  !*** ./app/not-found.tsx ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ NotFound)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(rsc)/./node_modules/next/dist/api/link.js\");\n\n\n/**\n * Global 404 Not Found Page\n * \n * This page is shown when a route doesn't exist\n * Provides helpful navigation options to get users back on track\n */ function NotFound() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen flex items-center justify-center bg-gray-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-center max-w-md mx-auto p-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mx-auto h-24 w-24 bg-gray-100 rounded-full flex items-center justify-center mb-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-gray-400 text-4xl\",\n                        children: \"\\uD83D\\uDCDA\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Pictures\\\\school_pro\\\\school-pro-backend\\\\frontend-integration\\\\app\\\\not-found.tsx\",\n                        lineNumber: 15,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Pictures\\\\school_pro\\\\school-pro-backend\\\\frontend-integration\\\\app\\\\not-found.tsx\",\n                    lineNumber: 14,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                    className: \"text-6xl font-bold text-gray-800 mb-4\",\n                    children: \"404\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Pictures\\\\school_pro\\\\school-pro-backend\\\\frontend-integration\\\\app\\\\not-found.tsx\",\n                    lineNumber: 19,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                    className: \"text-2xl font-semibold text-gray-700 mb-4\",\n                    children: \"Page Not Found\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Pictures\\\\school_pro\\\\school-pro-backend\\\\frontend-integration\\\\app\\\\not-found.tsx\",\n                    lineNumber: 20,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-gray-600 mb-8\",\n                    children: \"The page you're looking for doesn't exist or has been moved. Let's get you back to where you need to be.\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Pictures\\\\school_pro\\\\school-pro-backend\\\\frontend-integration\\\\app\\\\not-found.tsx\",\n                    lineNumber: 24,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col sm:flex-row gap-3 justify-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                            href: \"/\",\n                            className: \"px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\",\n                            children: \"Go Home\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Pictures\\\\school_pro\\\\school-pro-backend\\\\frontend-integration\\\\app\\\\not-found.tsx\",\n                            lineNumber: 31,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                            href: \"/dashboard\",\n                            className: \"px-6 py-3 bg-gray-200 text-gray-800 rounded-lg hover:bg-gray-300 transition-colors\",\n                            children: \"Dashboard\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Pictures\\\\school_pro\\\\school-pro-backend\\\\frontend-integration\\\\app\\\\not-found.tsx\",\n                            lineNumber: 38,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Pictures\\\\school_pro\\\\school-pro-backend\\\\frontend-integration\\\\app\\\\not-found.tsx\",\n                    lineNumber: 30,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-8 pt-6 border-t border-gray-200\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm text-gray-500 mb-3\",\n                            children: \"Quick Links:\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Pictures\\\\school_pro\\\\school-pro-backend\\\\frontend-integration\\\\app\\\\not-found.tsx\",\n                            lineNumber: 48,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-wrap justify-center gap-4 text-sm\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                    href: \"/dashboard/students\",\n                                    className: \"text-blue-600 hover:underline\",\n                                    children: \"Students\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Pictures\\\\school_pro\\\\school-pro-backend\\\\frontend-integration\\\\app\\\\not-found.tsx\",\n                                    lineNumber: 50,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                    href: \"/dashboard/teachers\",\n                                    className: \"text-blue-600 hover:underline\",\n                                    children: \"Teachers\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Pictures\\\\school_pro\\\\school-pro-backend\\\\frontend-integration\\\\app\\\\not-found.tsx\",\n                                    lineNumber: 53,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                    href: \"/dashboard/classes\",\n                                    className: \"text-blue-600 hover:underline\",\n                                    children: \"Classes\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Pictures\\\\school_pro\\\\school-pro-backend\\\\frontend-integration\\\\app\\\\not-found.tsx\",\n                                    lineNumber: 56,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                    href: \"/dashboard/attendance\",\n                                    className: \"text-blue-600 hover:underline\",\n                                    children: \"Attendance\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Pictures\\\\school_pro\\\\school-pro-backend\\\\frontend-integration\\\\app\\\\not-found.tsx\",\n                                    lineNumber: 59,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Pictures\\\\school_pro\\\\school-pro-backend\\\\frontend-integration\\\\app\\\\not-found.tsx\",\n                            lineNumber: 49,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Pictures\\\\school_pro\\\\school-pro-backend\\\\frontend-integration\\\\app\\\\not-found.tsx\",\n                    lineNumber: 47,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Pictures\\\\school_pro\\\\school-pro-backend\\\\frontend-integration\\\\app\\\\not-found.tsx\",\n            lineNumber: 12,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Pictures\\\\school_pro\\\\school-pro-backend\\\\frontend-integration\\\\app\\\\not-found.tsx\",\n        lineNumber: 11,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/not-found.tsx\n");

/***/ }),

/***/ "(rsc)/./components/providers.tsx":
/*!**********************************!*\
  !*** ./components/providers.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Providers: () => (/* binding */ e0)\n/* harmony export */ });\n/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js\");\n\n\nconst e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\\Users\\<USER>\\OneDrive\\Pictures\\school_pro\\school-pro-backend\\frontend-integration\\components\\providers.tsx#Providers`);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./components/providers.tsx\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/@tanstack","vendor-chunks/next","vendor-chunks/sonner","vendor-chunks/zustand","vendor-chunks/@swc","vendor-chunks/axios","vendor-chunks/asynckit","vendor-chunks/math-intrinsics","vendor-chunks/es-errors","vendor-chunks/call-bind-apply-helpers","vendor-chunks/debug","vendor-chunks/get-proto","vendor-chunks/mime-db","vendor-chunks/has-symbols","vendor-chunks/gopd","vendor-chunks/function-bind","vendor-chunks/form-data","vendor-chunks/follow-redirects","vendor-chunks/supports-color","vendor-chunks/proxy-from-env","vendor-chunks/ms","vendor-chunks/mime-types","vendor-chunks/hasown","vendor-chunks/has-tostringtag","vendor-chunks/has-flag","vendor-chunks/get-intrinsic","vendor-chunks/es-set-tostringtag","vendor-chunks/es-object-atoms","vendor-chunks/es-define-property","vendor-chunks/dunder-proto","vendor-chunks/delayed-stream","vendor-chunks/combined-stream"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Flogin%2Fpage&page=%2Flogin%2Fpage&appPaths=%2Flogin%2Fpage&pagePath=private-next-app-dir%2Flogin%2Fpage.tsx&appDir=C%3A%5CUsers%5CImran%20Bhai%5COneDrive%5CPictures%5Cschool_pro%5Cschool-pro-backend%5Cfrontend-integration%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CImran%20Bhai%5COneDrive%5CPictures%5Cschool_pro%5Cschool-pro-backend%5Cfrontend-integration&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();