#!/usr/bin/env python3
"""
Debug Teachers Endpoint Script

Add this code to your FastAPI backend to debug the /api/v1/teachers/ endpoint.
This will help identify the exact issue causing the 500 error.
"""

import logging
import traceback
from datetime import datetime
from fastapi import HTTPException
import json

# Configure detailed logging
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Add this to your FastAPI app (replace your existing teachers endpoint)
@app.get("/api/v1/teachers/")
async def get_teachers_debug():
    """
    Debug version of teachers endpoint with comprehensive error handling
    """
    logger.info("=== TEACHERS ENDPOINT DEBUG START ===")
    logger.info(f"Timestamp: {datetime.now()}")
    
    try:
        # Step 1: Test basic database connection
        logger.info("Step 1: Testing database connection...")
        
        # TODO: Replace with your actual database connection test
        # Example for different database types:
        
        # For SQLAlchemy:
        # from your_database import engine, SessionLocal
        # db = SessionLocal()
        # db.execute("SELECT 1").fetchone()
        # logger.info("✅ Database connection successful")
        
        # For asyncpg:
        # await database.execute("SELECT 1")
        # logger.info("✅ Database connection successful")
        
        logger.info("✅ Database connection test passed (simulated)")
        
        # Step 2: Test teachers table access
        logger.info("Step 2: Testing teachers table access...")
        
        # TODO: Replace with your actual table check
        # Example queries to test:
        
        # Check if table exists:
        # result = await database.fetch_one(
        #     "SELECT COUNT(*) FROM information_schema.tables WHERE table_name = 'teachers'"
        # )
        # if result[0] == 0:
        #     raise Exception("Teachers table does not exist")
        
        # Count records:
        # count = await database.fetch_val("SELECT COUNT(*) FROM teachers")
        # logger.info(f"✅ Teachers table has {count} records")
        
        logger.info("✅ Teachers table access test passed (simulated)")
        
        # Step 3: Test actual query
        logger.info("Step 3: Testing teachers query...")
        
        # TODO: Replace with your actual teachers query
        # This is where the error is likely occurring
        
        # Common query patterns:
        # Option 1: Simple select all
        # teachers = await database.fetch_all("SELECT * FROM teachers")
        
        # Option 2: Select specific columns
        # teachers = await database.fetch_all("""
        #     SELECT id, name, email, subject, department, status, created_at, updated_at
        #     FROM teachers
        #     ORDER BY created_at DESC
        # """)
        
        # Option 3: With joins (if you have related tables)
        # teachers = await database.fetch_all("""
        #     SELECT t.*, d.name as department_name
        #     FROM teachers t
        #     LEFT JOIN departments d ON t.department_id = d.id
        # """)
        
        # For now, return mock data to test the endpoint structure
        teachers_data = [
            {
                "id": 1,
                "name": "John Doe",
                "email": "<EMAIL>",
                "subject": "Mathematics",
                "department": "Science",
                "status": "ACTIVE",
                "created_at": "2024-01-01T00:00:00Z",
                "updated_at": "2024-01-01T00:00:00Z"
            },
            {
                "id": 2,
                "name": "Jane Smith", 
                "email": "<EMAIL>",
                "subject": "English Literature",
                "department": "Humanities",
                "status": "ACTIVE",
                "created_at": "2024-01-01T00:00:00Z",
                "updated_at": "2024-01-01T00:00:00Z"
            }
        ]
        
        logger.info(f"✅ Query successful, returning {len(teachers_data)} teachers")
        
        # Step 4: Test data serialization
        logger.info("Step 4: Testing data serialization...")
        
        try:
            # Test if data can be JSON serialized
            json_test = json.dumps(teachers_data)
            logger.info("✅ Data serialization successful")
        except Exception as serialize_error:
            logger.error(f"❌ Data serialization failed: {serialize_error}")
            raise HTTPException(
                status_code=500,
                detail=f"Data serialization error: {str(serialize_error)}"
            )
        
        logger.info("=== TEACHERS ENDPOINT DEBUG SUCCESS ===")
        return teachers_data
        
    except Exception as e:
        # Comprehensive error logging
        logger.error("=== TEACHERS ENDPOINT DEBUG ERROR ===")
        logger.error(f"Error type: {type(e).__name__}")
        logger.error(f"Error message: {str(e)}")
        logger.error(f"Full traceback:")
        logger.error(traceback.format_exc())
        
        # Analyze common error patterns
        error_str = str(e).lower()
        
        if "connection" in error_str or "connect" in error_str:
            logger.error("🔍 DIAGNOSIS: Database connection error")
            logger.error("💡 Check: Database server running, connection string, credentials")
            
        elif "table" in error_str or "relation" in error_str:
            logger.error("🔍 DIAGNOSIS: Table/relation error")
            logger.error("💡 Check: Table name spelling, schema, table exists")
            
        elif "column" in error_str or "field" in error_str:
            logger.error("🔍 DIAGNOSIS: Column/field error")
            logger.error("💡 Check: Column names in query match database schema")
            
        elif "permission" in error_str or "access" in error_str:
            logger.error("🔍 DIAGNOSIS: Permission/access error")
            logger.error("💡 Check: Database user permissions, table access rights")
            
        elif "syntax" in error_str or "sql" in error_str:
            logger.error("🔍 DIAGNOSIS: SQL syntax error")
            logger.error("💡 Check: SQL query syntax, database-specific SQL dialect")
            
        else:
            logger.error("🔍 DIAGNOSIS: Unknown error")
            logger.error("💡 Check: Full error details above")
        
        # Return detailed error for debugging (remove in production)
        raise HTTPException(
            status_code=500,
            detail={
                "error": "Teachers endpoint debug error",
                "type": type(e).__name__,
                "message": str(e),
                "timestamp": str(datetime.now()),
                "suggestion": "Check server logs for detailed error information"
            }
        )

# Alternative: Minimal working endpoint for immediate testing
@app.get("/api/v1/teachers/minimal")
async def get_teachers_minimal():
    """
    Minimal teachers endpoint that should always work
    Use this to test if the endpoint structure is correct
    """
    return [
        {"id": "1", "name": "Test Teacher 1", "email": "<EMAIL>"},
        {"id": "2", "name": "Test Teacher 2", "email": "<EMAIL>"}
    ]

# Database query test endpoint
@app.get("/api/v1/debug/teachers-query")
async def debug_teachers_query():
    """
    Test different query approaches to identify the issue
    """
    results = {}
    
    try:
        # Test 1: Simple count
        # count = await database.fetch_val("SELECT COUNT(*) FROM teachers")
        # results["count_query"] = {"success": True, "count": count}
        results["count_query"] = {"success": True, "count": "simulated_32"}
        
    except Exception as e:
        results["count_query"] = {"success": False, "error": str(e)}
    
    try:
        # Test 2: Select first record
        # first_teacher = await database.fetch_one("SELECT * FROM teachers LIMIT 1")
        # results["first_record"] = {"success": True, "data": dict(first_teacher)}
        results["first_record"] = {"success": True, "data": "simulated"}
        
    except Exception as e:
        results["first_record"] = {"success": False, "error": str(e)}
    
    try:
        # Test 3: Select specific columns
        # teachers = await database.fetch_all("SELECT id, name, email FROM teachers LIMIT 5")
        # results["specific_columns"] = {"success": True, "count": len(teachers)}
        results["specific_columns"] = {"success": True, "count": "simulated_5"}
        
    except Exception as e:
        results["specific_columns"] = {"success": False, "error": str(e)}
    
    return results
