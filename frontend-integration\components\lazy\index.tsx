/**
 * Lazy Loading Components
 *
 * Dynamic imports for non-critical components to improve initial bundle size
 * and loading performance. Components are loaded only when needed.
 */

import dynamic from 'next/dynamic';

// Loading fallback component
const LoadingFallback = () => (
  <div className="flex items-center justify-center p-8">
    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
  </div>
);

// Skeleton fallback for form components
const FormLoadingFallback = () => (
  <div className="w-full max-w-2xl mx-auto">
    <div className="animate-pulse">
      <div className="bg-white rounded-lg shadow p-6 space-y-6">
        <div className="space-y-2">
          <div className="h-6 bg-gray-200 rounded w-1/4"></div>
          <div className="h-4 bg-gray-200 rounded w-1/2"></div>
        </div>
        <div className="space-y-4">
          <div className="h-10 bg-gray-200 rounded"></div>
          <div className="h-10 bg-gray-200 rounded"></div>
          <div className="h-10 bg-gray-200 rounded"></div>
        </div>
        <div className="flex justify-end space-x-4">
          <div className="h-10 bg-gray-200 rounded w-20"></div>
          <div className="h-10 bg-gray-200 rounded w-24"></div>
        </div>
      </div>
    </div>
  </div>
);

// Chart loading fallback
const ChartLoadingFallback = () => (
  <div className="w-full h-64 bg-gray-100 rounded-lg flex items-center justify-center">
    <div className="text-center">
      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-2"></div>
      <p className="text-gray-500 text-sm">Loading chart...</p>
    </div>
  </div>
);

// Table loading fallback
const TableLoadingFallback = () => (
  <div className="w-full">
    <div className="animate-pulse">
      <div className="bg-white rounded-lg shadow overflow-hidden">
        <div className="px-6 py-4 border-b border-gray-200">
          <div className="h-6 bg-gray-200 rounded w-1/4"></div>
        </div>
        <div className="divide-y divide-gray-200">
          {[...Array(5)].map((_, i) => (
            <div key={i} className="px-6 py-4 flex space-x-4">
              <div className="h-4 bg-gray-200 rounded flex-1"></div>
              <div className="h-4 bg-gray-200 rounded flex-1"></div>
              <div className="h-4 bg-gray-200 rounded flex-1"></div>
              <div className="h-4 bg-gray-200 rounded w-20"></div>
            </div>
          ))}
        </div>
      </div>
    </div>
  </div>
);

// Page loading fallback
const PageLoadingFallback = () => (
  <div className="min-h-screen flex items-center justify-center">
    <div className="text-center">
      <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
      <p className="text-gray-500">Loading...</p>
    </div>
  </div>
);

// Card loading fallback
const CardLoadingFallback = () => (
  <div className="bg-white rounded-lg shadow p-6">
    <div className="animate-pulse">
      <div className="flex space-x-4">
        <div className="rounded-full bg-gray-200 h-12 w-12"></div>
        <div className="flex-1 space-y-2">
          <div className="h-4 bg-gray-200 rounded w-3/4"></div>
          <div className="h-4 bg-gray-200 rounded w-1/2"></div>
        </div>
      </div>
      <div className="mt-4 space-y-2">
        <div className="h-4 bg-gray-200 rounded"></div>
        <div className="h-4 bg-gray-200 rounded w-5/6"></div>
      </div>
    </div>
  </div>
);

// =============================================================================
// LAZY LOADED COMPONENTS
// =============================================================================

// Form Components
export const LazyTeacherForm = dynamic(
  () => import('../forms/TeacherForm'),
  {
    loading: FormLoadingFallback,
    ssr: false, // Forms are client-side only
  }
);

export const LazyStudentForm = dynamic(
  () => import('../forms/StudentForm'),
  {
    loading: FormLoadingFallback,
    ssr: false,
  }
);

export const LazyClassForm = dynamic(
  () => import('../forms/ClassForm'),
  {
    loading: FormLoadingFallback,
    ssr: false,
  }
);

export const LazySubjectForm = dynamic(
  () => import('../forms/SubjectForm'),
  {
    loading: FormLoadingFallback,
    ssr: false,
  }
);

// UI Components
export const LazyDataTable = dynamic(
  () => import('../ui/data-table'),
  {
    loading: TableLoadingFallback,
    ssr: true,
  }
);

export const LazyEntityCard = dynamic(
  () => import('../ui/entity-card'),
  {
    loading: CardLoadingFallback,
    ssr: true,
  }
);

// Shared Components
export const LazyListCard = dynamic(
  () => import('../shared/ListCard'),
  {
    loading: CardLoadingFallback,
    ssr: true,
  }
);

export const LazyPageHeader = dynamic(
  () => import('../shared/page-header'),
  {
    loading: LoadingFallback,
    ssr: true,
  }
);

// Development Components
export const LazyPerformanceMonitor = dynamic(
  () => import('../dev/PerformanceMonitor'),
  {
    loading: LoadingFallback,
    ssr: false,
  }
);

// Optimized Components
export const LazyOptimizedDataTable = dynamic(
  () => import('../optimized/OptimizedDataTable'),
  {
    loading: TableLoadingFallback,
    ssr: true,
  }
);

export const LazyOptimizedFormField = dynamic(
  () => import('../optimized/OptimizedFormField'),
  {
    loading: LoadingFallback,
    ssr: false,
  }
);

// =============================================================================
// PRELOAD FUNCTIONS
// =============================================================================



// =============================================================================
// FALLBACK COMPONENTS EXPORT
// =============================================================================

export {
    CardLoadingFallback, ChartLoadingFallback, FormLoadingFallback, LoadingFallback, PageLoadingFallback, TableLoadingFallback
};

// =============================================================================
// COMPONENT REGISTRY
// =============================================================================

export const LazyComponentRegistry = {
  // Forms
  TeacherForm: LazyTeacherForm,
  StudentForm: LazyStudentForm,
  ClassForm: LazyClassForm,
  SubjectForm: LazySubjectForm,

  // UI Components
  DataTable: LazyDataTable,
  EntityCard: LazyEntityCard,

  // Shared Components
  ListCard: LazyListCard,
  PageHeader: LazyPageHeader,

  // Development
  PerformanceMonitor: LazyPerformanceMonitor,

  // Optimized Components
  OptimizedDataTable: LazyOptimizedDataTable,
  OptimizedFormField: LazyOptimizedFormField,
} as const;

export type LazyComponentName = keyof typeof LazyComponentRegistry;
