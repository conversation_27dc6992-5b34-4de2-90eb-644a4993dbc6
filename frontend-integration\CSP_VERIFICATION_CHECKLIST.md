# CSP Verification Checklist

## 🚀 **Quick Verification Steps**

After implementing the CSP fixes, use this checklist to verify everything is working correctly.

### 1. **Browser Network → Document Headers**
- [ ] Open Browser DevTools → Network tab
- [ ] Reload the page
- [ ] Click on the main document request
- [ ] Check Response Headers
- [ ] **Verify**: `content-security-policy` includes your `connect-src`

**Expected CSP Header:**
```
content-security-policy: default-src 'self'; connect-src 'self' http://localhost:8000 http://127.0.0.1:8000 ws://localhost:3000 ws://127.0.0.1:3000; img-src 'self' data: blob:; style-src 'self' 'unsafe-inline'; script-src 'self' 'unsafe-eval'; frame-ancestors 'none'; base-uri 'self';
```

### 2. **API Call Verification**
- [ ] Navigate to `/dashboard/students`
- [ ] Open Browser DevTools → Console tab
- [ ] Click "Add Student" button
- [ ] Fill out the form and submit
- [ ] **Check Console**: Should see `[API ▶]` and `[API ✓]` logs
- [ ] **Check Network**: Should see POST to `http://localhost:8000/api/v1/students/`

**Expected Console Output:**
```
[API ▶] {method: "POST", url: "/students/", fullUrl: "http://localhost:8000/api/v1/students/", ...}
[STUDENT CREATE] Sending payload: {admission_number: "STU001", name: "John", ...}
[API ✓] {status: 201, method: "POST", url: "/students/", ...}
[STUDENT CREATE] Success: {id: "...", name: "John", ...}
```

### 3. **No Double API Paths**
- [ ] **Verify**: API calls are `http://localhost:8000/api/v1/students/`
- [ ] **NOT**: `http://localhost:8000/api/v1/api/v1/students/`
- [ ] **Check**: No duplicate `/api/v1` in URLs

### 4. **CSP Headers Not Duplicated**
- [ ] **Check**: Only one `content-security-policy` header
- [ ] **Verify**: No duplicate CSP headers from proxy/middleware
- [ ] **Search**: `grep -R "Content-Security-Policy" .` (should only show next.config.mjs)

### 5. **Protocol Consistency**
- [ ] **Frontend**: Running on `http://localhost:3000`
- [ ] **Backend**: Running on `http://localhost:8000`
- [ ] **Verify**: Both use HTTP (not mixed https vs http)

### 6. **Environment Variables**
- [ ] **Check**: `.env.local` exists with correct API URL
- [ ] **Verify**: `NEXT_PUBLIC_API_URL=http://localhost:8000/api/v1`
- [ ] **Confirm**: No hardcoded URLs in code

## 🔍 **Troubleshooting Steps**

### If CSP Still Blocks API Calls:

#### 1. **Restart Dev Server**
```bash
# Stop current server (Ctrl+C)
npm run dev
```

#### 2. **Check CSP Headers in Browser**
- Open DevTools → Network → Document
- Look for `content-security-policy` header
- Verify it includes your backend URLs

#### 3. **Verify next.config.mjs**
- Check that CSP configuration is correct
- Ensure no syntax errors
- Verify file is saved

#### 4. **Check for Proxy Override**
- If using Nginx/Apache, check for CSP headers
- Ensure they don't override Next.js CSP
- Look for duplicate CSP headers

#### 5. **Temporary Meta CSP (DEV ONLY)**
If headers aren't working, add this to `app/layout.tsx` temporarily:

```tsx
<meta
  httpEquiv="Content-Security-Policy"
  content="default-src 'self'; connect-src 'self' http://localhost:8000 http://127.0.0.1:8000; img-src 'self' data: blob:; style-src 'self' 'unsafe-inline'; script-src 'self' 'unsafe-eval';"
/>
```

**Remove once header-based CSP works!**

## ✅ **Success Indicators**

- [ ] **Console Logs**: `[API ▶]` and `[API ✓]` appear
- [ ] **Network Requests**: API calls succeed with 200/201 status
- [ ] **Toast Messages**: "Student created successfully!" appears
- [ ] **Data Persistence**: Students appear in the list
- **No CSP Violations**: Browser console shows no CSP errors

## 🚨 **Common Issues & Solutions**

### Issue: "Refused to connect" Error
**Solution**: CSP headers not applied - restart dev server

### Issue: Double `/api/v1` in URLs
**Solution**: Check baseURL configuration in apiClient.ts

### Issue: Mixed Protocol Errors
**Solution**: Ensure both frontend and backend use HTTP in development

### Issue: CORS Errors
**Solution**: Check backend CORS configuration allows localhost:3000

### Issue: Network Timeout
**Solution**: Verify backend is running on port 8000

## 📋 **Final Verification**

After completing all steps:

1. **Create a student** → Should succeed with toast message
2. **Check console logs** → Should show detailed API communication
3. **Verify network tab** → Should show successful API calls
4. **No CSP violations** → Browser console should be clean

## 🎯 **Expected Result**

Your Students module should now work seamlessly with:
- ✅ **No CSP blocks** on API calls
- ✅ **Clear diagnostic logging** for debugging
- ✅ **Proper error handling** for network vs backend issues
- ✅ **Seamless backend integration** with FastAPI

**Remember**: Always restart your Next.js dev server after making CSP changes!
