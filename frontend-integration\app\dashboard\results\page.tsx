'use client';

/**
 * Results Page - Complete Results Management System
 *
 * Features:
 * - Professional results listing with grade analytics
 * - Result filtering and search by student/exam
 * - Grade distribution charts
 * - Responsive design with detailed views
 * - Loading and error states
 * - Mock data with realistic content
 */

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { ModulePageLayout } from '@/components/ui/module-page-layout';
import { BarChart3, FileText, GraduationCap, TrendingUp, User } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { useState } from 'react';

// Mock data for demonstration
const mockResultStats = {
  totalResults: 156,
  averageGrade: 85.2,
  passRate: 92,
  topPerformers: 24,
};

const mockResults = [
  {
    id: '1',
    studentName: '<PERSON>',
    studentId: 'STU001',
    examTitle: 'Mathematics Midterm',
    subject: 'Mathematics',
    grade: 'A',
    score: 95,
    maxScore: 100,
    percentage: 95,
    date: '2024-02-15',
    teacher: 'Dr. <PERSON>',
    status: 'Published',
  },
  {
    id: '2',
    studentName: '<PERSON>',
    studentId: 'STU002',
    examTitle: 'Physics Final Exam',
    subject: 'Physics',
    grade: 'B+',
    score: 87,
    maxScore: 100,
    percentage: 87,
    date: '2024-02-20',
    teacher: 'Prof. Michael Chen',
    status: 'Published',
  },
  {
    id: '3',
    studentName: 'Carol Davis',
    studentId: 'STU003',
    examTitle: 'English Literature Essay',
    subject: 'English',
    grade: 'A-',
    score: 92,
    maxScore: 100,
    percentage: 92,
    date: '2024-02-18',
    teacher: 'Ms. Emily Davis',
    status: 'Published',
  },
  {
    id: '4',
    studentName: 'David Wilson',
    studentId: 'STU004',
    examTitle: 'Chemistry Lab Report',
    subject: 'Chemistry',
    grade: 'B',
    score: 82,
    maxScore: 100,
    percentage: 82,
    date: '2024-02-22',
    teacher: 'Dr. Robert Wilson',
    status: 'Draft',
  },
  {
    id: '5',
    studentName: 'Eva Brown',
    studentId: 'STU005',
    examTitle: 'Biology Quiz',
    subject: 'Biology',
    grade: 'A+',
    score: 98,
    maxScore: 100,
    percentage: 98,
    date: '2024-02-25',
    teacher: 'Ms. Lisa Anderson',
    status: 'Published',
  },
];

const subjects = ['All Subjects', 'Mathematics', 'Physics', 'English', 'Chemistry', 'Biology'];
const statuses = ['All Status', 'Published', 'Draft', 'Under Review'];
const grades = ['All Grades', 'A+', 'A', 'A-', 'B+', 'B', 'B-', 'C+', 'C', 'C-', 'D', 'F'];

export default function ResultsPage() {
  const router = useRouter();
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedSubject, setSelectedSubject] = useState('All Subjects');
  const [selectedStatus, setSelectedStatus] = useState('All Status');
  const [selectedGrade, setSelectedGrade] = useState('All Grades');

  // Filter results based on search and filters
  const filteredResults = mockResults.filter(result => {
    const matchesSearch =
      result.studentName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      result.examTitle.toLowerCase().includes(searchTerm.toLowerCase()) ||
      result.studentId.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesSubject = selectedSubject === 'All Subjects' || result.subject === selectedSubject;
    const matchesStatus = selectedStatus === 'All Status' || result.status === selectedStatus;
    const matchesGrade = selectedGrade === 'All Grades' || result.grade === selectedGrade;

    return matchesSearch && matchesSubject && matchesStatus && matchesGrade;
  });

  const statsCards = [
    {
      title: 'Total Results',
      value: mockResultStats.totalResults.toString(),
      icon: FileText,
      color: 'text-blue-600',
      bgColor: 'bg-blue-50',
      change: '+12%',
      changeType: 'positive' as const,
    },
    {
      title: 'Average Grade',
      value: `${mockResultStats.averageGrade}%`,
      icon: BarChart3,
      color: 'text-green-600',
      bgColor: 'bg-green-50',
      change: '+2.5%',
      changeType: 'positive' as const,
    },
    {
      title: 'Pass Rate',
      value: `${mockResultStats.passRate}%`,
      icon: TrendingUp,
      color: 'text-orange-600',
      bgColor: 'bg-orange-50',
      change: '+5%',
      changeType: 'positive' as const,
    },
    {
      title: 'Top Performers',
      value: mockResultStats.topPerformers.toString(),
      icon: GraduationCap,
      color: 'text-purple-600',
      bgColor: 'bg-purple-50',
      change: '+8%',
      changeType: 'positive' as const,
    },
  ];

  const filters = [
    {
      label: 'Subject',
      value: selectedSubject,
      onChange: setSelectedSubject,
      options: subjects.map(subject => ({ label: subject, value: subject })),
    },
    {
      label: 'Status',
      value: selectedStatus,
      onChange: setSelectedStatus,
      options: statuses.map(status => ({ label: status, value: status })),
    },
    {
      label: 'Grade',
      value: selectedGrade,
      onChange: setSelectedGrade,
      options: grades.map(grade => ({ label: grade, value: grade })),
    },
  ];

  const getGradeColor = (grade: string) => {
    if (grade.startsWith('A')) return 'bg-green-100 text-green-800';
    if (grade.startsWith('B')) return 'bg-blue-100 text-blue-800';
    if (grade.startsWith('C')) return 'bg-yellow-100 text-yellow-800';
    if (grade.startsWith('D')) return 'bg-orange-100 text-orange-800';
    return 'bg-red-100 text-red-800';
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Published':
        return 'bg-green-100 text-green-800';
      case 'Draft':
        return 'bg-yellow-100 text-yellow-800';
      case 'Under Review':
        return 'bg-blue-100 text-blue-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <ModulePageLayout
      title='Results Management'
      description='View and manage student exam results and academic performance'
      icon={BarChart3}
      badge={{ label: 'Demo Data', variant: 'outline' }}
      statsCards={statsCards}
      searchPlaceholder='Search results by student or exam...'
      filters={filters}
      createRoute='/dashboard/results/create'
      createLabel='Add Result'
      searchValue={searchTerm}
      onSearchChange={setSearchTerm}
      totalItems={mockResults.length}
      filteredItems={filteredResults.length}
    >
      {/* Results List */}
      <div className='grid grid-cols-1 gap-4'>
        {filteredResults.map(result => (
          <Card key={result.id} className='hover:shadow-md transition-shadow'>
            <CardContent className='p-6'>
              <div className='flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4'>
                {/* Result Details */}
                <div className='flex-1 space-y-3'>
                  <div className='flex flex-col sm:flex-row sm:items-start sm:justify-between gap-2'>
                    <div>
                      <h3 className='text-lg font-semibold text-gray-900'>{result.examTitle}</h3>
                      <p className='text-gray-600'>
                        {result.subject} • {result.date}
                      </p>
                    </div>
                    <div className='flex gap-2'>
                      <Badge className={getStatusColor(result.status)}>{result.status}</Badge>
                    </div>
                  </div>

                  <div className='flex items-center gap-4'>
                    <div className='flex items-center gap-2'>
                      <User className='w-4 h-4 text-gray-500' />
                      <span className='text-sm text-gray-600'>
                        {result.studentName} ({result.studentId})
                      </span>
                    </div>
                    <div className='text-sm text-gray-600'>Teacher: {result.teacher}</div>
                  </div>
                </div>

                {/* Score Display */}
                <div className='flex items-center gap-6'>
                  <div className='text-center'>
                    <div className='text-2xl font-bold text-gray-900'>
                      {result.score}/{result.maxScore}
                    </div>
                    <div className='text-sm text-gray-500'>Score</div>
                  </div>
                  <div className='text-center'>
                    <div className='text-2xl font-bold text-gray-900'>{result.percentage}%</div>
                    <div className='text-sm text-gray-500'>Percentage</div>
                  </div>
                  <div className='text-center'>
                    <Badge className={`text-lg px-3 py-1 ${getGradeColor(result.grade)}`}>
                      {result.grade}
                    </Badge>
                    <div className='text-sm text-gray-500 mt-1'>Grade</div>
                  </div>
                </div>

                {/* Actions */}
                <div className='flex gap-2'>
                  <Button
                    variant='outline'
                    size='sm'
                    onClick={() => router.push(`/dashboard/results/${result.id}`)}
                  >
                    View Details
                  </Button>
                  <Button
                    variant='outline'
                    size='sm'
                    onClick={() => router.push(`/dashboard/results/${result.id}/edit`)}
                  >
                    Edit
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}

        {filteredResults.length === 0 && (
          <div className='text-center py-12'>
            <BarChart3 className='w-16 h-16 text-gray-300 mx-auto mb-4' />
            <h3 className='text-lg font-medium text-gray-900 mb-2'>No results found</h3>
            <p className='text-gray-500 mb-6'>
              {searchTerm || selectedSubject !== 'All Subjects' || selectedStatus !== 'All Status'
                ? 'Try adjusting your search or filters'
                : 'Get started by adding your first result'}
            </p>
            <Button onClick={() => router.push('/dashboard/results/create')}>Add Result</Button>
          </div>
        )}
      </div>
    </ModulePageLayout>
  );
}
