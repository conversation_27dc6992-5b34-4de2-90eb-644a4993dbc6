'use client';

import React, { useState } from 'react';
import { DragDrop<PERSON>ontext, Droppable, Draggable } from '@hello-pangea/dnd';
import { Plus, GripVertical, Edit2, Trash2, AlertTriangle } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle } from '@/components/ui/alert-dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { useToast } from '@/hooks/use-toast';

interface Column {
  key: string;
  label: string;
  render: (item: any) => React.ReactNode;
}

interface FormField {
  name: string;
  label: string;
  type: 'text' | 'number' | 'date' | 'select' | 'checkbox' | 'textarea';
  required?: boolean;
  options?: { value: string; label: string }[];
}

interface AcademicPanelProps {
  title: string;
  data: any[];
  columns: Column[];
  formFields: FormField[];
  onDataChange?: () => void;
}

/**
 * AcademicPanel Component
 * 
 * Reusable panel for academic entities with:
 * - Small table with drag & drop reordering
 * - Add/Edit dialog
 * - Guarded delete (checks if in use)
 * - Form validation
 */
export function AcademicPanel({ title, data, columns, formFields, onDataChange }: AcademicPanelProps) {
  const { toast } = useToast();
  const [items, setItems] = useState(data);
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [editingItem, setEditingItem] = useState<any>(null);
  const [deletingItem, setDeletingItem] = useState<any>(null);
  const [formData, setFormData] = useState<any>({});

  // Handle drag and drop reordering
  const handleDragEnd = (result: any) => {
    if (!result.destination) return;

    const reorderedItems = Array.from(items);
    const [movedItem] = reorderedItems.splice(result.source.index, 1);
    reorderedItems.splice(result.destination.index, 0, movedItem);

    // Update sort orders
    const updatedItems = reorderedItems.map((item, index) => ({
      ...item,
      sortOrder: index,
    }));

    setItems(updatedItems);
    onDataChange?.();
    
    toast({
      title: 'Order updated',
      description: `${title} have been reordered successfully.`,
    });
  };

  // Handle form field change
  const handleFieldChange = (fieldName: string, value: any) => {
    if (fieldName.includes('.')) {
      // Handle nested fields like 'ageRange.min'
      const [parent, child] = fieldName.split('.');
      setFormData((prev: any) => ({
        ...prev,
        [parent]: {
          ...prev[parent],
          [child]: value,
        },
      }));
    } else {
      setFormData((prev: any) => ({
        ...prev,
        [fieldName]: value,
      }));
    }
  };

  // Handle add item
  const handleAdd = () => {
    // Basic validation
    const requiredFields = formFields.filter(field => field.required);
    const missingFields = requiredFields.filter(field => {
      if (field.name.includes('.')) {
        const [parent, child] = field.name.split('.');
        return !formData[parent]?.[child];
      }
      return !formData[field.name];
    });

    if (missingFields.length > 0) {
      toast({
        title: 'Validation Error',
        description: `Please fill in all required fields: ${missingFields.map(f => f.label).join(', ')}`,
        variant: 'destructive',
      });
      return;
    }

    const newItem = {
      ...formData,
      id: Date.now().toString(),
      sortOrder: items.length,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };

    setItems([...items, newItem]);
    setIsAddDialogOpen(false);
    setFormData({});
    onDataChange?.();

    toast({
      title: `${title.slice(0, -1)} added`,
      description: `New ${title.toLowerCase().slice(0, -1)} has been added successfully.`,
    });
  };

  // Handle edit item
  const handleEdit = () => {
    if (!editingItem) return;

    const updatedItems = items.map(item =>
      item.id === editingItem.id
        ? { ...item, ...formData, updatedAt: new Date().toISOString() }
        : item
    );

    setItems(updatedItems);
    setEditingItem(null);
    setFormData({});
    onDataChange?.();

    toast({
      title: `${title.slice(0, -1)} updated`,
      description: `${title.slice(0, -1)} has been updated successfully.`,
    });
  };

  // Handle delete item
  const handleDelete = () => {
    if (!deletingItem) return;

    // Check if item is in use (simplified check)
    const isInUse = deletingItem.isActive && items.filter((item: any) => item.isActive).length === 1;
    
    if (isInUse && title === 'Sessions') {
      toast({
        title: 'Cannot delete',
        description: 'Cannot delete the only active session.',
        variant: 'destructive',
      });
      setDeletingItem(null);
      return;
    }

    setItems(items.filter(item => item.id !== deletingItem.id));
    setDeletingItem(null);
    onDataChange?.();

    toast({
      title: `${title.slice(0, -1)} deleted`,
      description: `${title.slice(0, -1)} has been deleted successfully.`,
    });
  };

  // Open edit dialog
  const openEditDialog = (item: any) => {
    setEditingItem(item);
    setFormData(item);
  };

  // Render form field
  const renderFormField = (field: FormField) => {
    const value = field.name.includes('.') 
      ? formData[field.name.split('.')[0]]?.[field.name.split('.')[1]] || ''
      : formData[field.name] || '';

    switch (field.type) {
      case 'select':
        return (
          <Select
            value={value}
            onValueChange={(val) => handleFieldChange(field.name, val)}
          >
            <SelectTrigger>
              <SelectValue placeholder={`Select ${field.label.toLowerCase()}`} />
            </SelectTrigger>
            <SelectContent>
              {field.options?.map(option => (
                <SelectItem key={option.value} value={option.value}>
                  {option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        );
      
      case 'checkbox':
        return (
          <Switch
            checked={value || false}
            onCheckedChange={(checked) => handleFieldChange(field.name, checked)}
          />
        );
      
      case 'textarea':
        return (
          <Textarea
            value={value}
            onChange={(e) => handleFieldChange(field.name, e.target.value)}
            placeholder={`Enter ${field.label.toLowerCase()}`}
            rows={3}
          />
        );
      
      default:
        return (
          <Input
            type={field.type}
            value={value}
            onChange={(e) => handleFieldChange(field.name, field.type === 'number' ? Number(e.target.value) : e.target.value)}
            placeholder={`Enter ${field.label.toLowerCase()}`}
          />
        );
    }
  };

  return (
    <div className="p-6">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div>
          <h3 className="text-lg font-semibold text-slate-900">{title}</h3>
          <p className="text-sm text-slate-600">{items.length} {title.toLowerCase()}</p>
        </div>
        
        <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
          <DialogTrigger asChild>
            <Button size="sm" className="bg-gradient-to-r from-sky-600 to-violet-600">
              <Plus className="h-4 w-4 mr-2" />
              Add {title.slice(0, -1)}
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-md">
            <DialogHeader>
              <DialogTitle>Add New {title.slice(0, -1)}</DialogTitle>
            </DialogHeader>
            <div className="space-y-4">
              {formFields.map((field) => (
                <div key={field.name} className="space-y-2">
                  <Label htmlFor={field.name}>
                    {field.label} {field.required && <span className="text-red-500">*</span>}
                  </Label>
                  {renderFormField(field)}
                </div>
              ))}
              <div className="flex justify-end gap-2 pt-4">
                <Button variant="outline" onClick={() => setIsAddDialogOpen(false)}>
                  Cancel
                </Button>
                <Button onClick={handleAdd}>
                  Add {title.slice(0, -1)}
                </Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      </div>

      {/* Table */}
      {items.length === 0 ? (
        <div className="text-center py-12 text-slate-500">
          <div className="text-4xl mb-4">📚</div>
          <p>No {title.toLowerCase()} configured</p>
          <p className="text-sm">Add your first {title.toLowerCase().slice(0, -1)} to get started</p>
        </div>
      ) : (
        <DragDropContext onDragEnd={handleDragEnd}>
          <Droppable droppableId={`${title}-table`}>
            {(provided) => (
              <div {...provided.droppableProps} ref={provided.innerRef}>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead className="w-12"></TableHead>
                      {columns.map((column) => (
                        <TableHead key={column.key}>{column.label}</TableHead>
                      ))}
                      <TableHead className="w-24">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {items.map((item, index) => (
                      <Draggable key={item.id} draggableId={item.id} index={index}>
                        {(provided, snapshot) => (
                          <TableRow
                            ref={provided.innerRef}
                            {...provided.draggableProps}
                            className={snapshot.isDragging ? 'bg-slate-50' : ''}
                          >
                            <TableCell {...provided.dragHandleProps}>
                              <GripVertical className="h-4 w-4 text-slate-400 cursor-grab" />
                            </TableCell>
                            {columns.map((column) => (
                              <TableCell key={column.key}>
                                {column.render(item)}
                              </TableCell>
                            ))}
                            <TableCell>
                              <div className="flex items-center gap-1">
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => openEditDialog(item)}
                                >
                                  <Edit2 className="h-4 w-4" />
                                </Button>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => setDeletingItem(item)}
                                >
                                  <Trash2 className="h-4 w-4" />
                                </Button>
                              </div>
                            </TableCell>
                          </TableRow>
                        )}
                      </Draggable>
                    ))}
                    {provided.placeholder}
                  </TableBody>
                </Table>
              </div>
            )}
          </Droppable>
        </DragDropContext>
      )}

      {/* Edit Dialog */}
      <Dialog open={!!editingItem} onOpenChange={() => setEditingItem(null)}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>Edit {title.slice(0, -1)}</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            {formFields.map((field) => (
              <div key={field.name} className="space-y-2">
                <Label htmlFor={field.name}>
                  {field.label} {field.required && <span className="text-red-500">*</span>}
                </Label>
                {renderFormField(field)}
              </div>
            ))}
            <div className="flex justify-end gap-2 pt-4">
              <Button variant="outline" onClick={() => setEditingItem(null)}>
                Cancel
              </Button>
              <Button onClick={handleEdit}>
                Save Changes
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={!!deletingItem} onOpenChange={() => setDeletingItem(null)}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <div className="flex items-center gap-3">
              <div className="p-2 bg-red-50 rounded-lg">
                <AlertTriangle className="h-5 w-5 text-red-600" />
              </div>
              <div>
                <AlertDialogTitle>Delete {title.slice(0, -1)}</AlertDialogTitle>
              </div>
            </div>
            <AlertDialogDescription>
              Are you sure you want to delete this {title.toLowerCase().slice(0, -1)}? 
              This action cannot be undone and may affect related data.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDelete}
              className="bg-red-600 hover:bg-red-700"
            >
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}
