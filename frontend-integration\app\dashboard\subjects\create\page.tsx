'use client';

import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Progress } from '@/components/ui/progress';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import {
  ArrowLeft,
  ArrowRight,
  BookOpen,
  Check,
  Clock,
  GraduationCap,
  Plus,
  Users,
} from 'lucide-react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { useState } from 'react';

interface SubjectFormData {
  // Basic Information
  name: string;
  code: string;
  department: string;
  description: string;
  
  // Academic Information
  credits: string;
  duration: string;
  level: string;
  prerequisites: string;
  
  // Assignment Information
  teacherId: string;
  maxStudents: string;
  schedule: string;
  classroom: string;
  
  // Additional Information
  objectives: string;
  materials: string;
  assessment: string;
}

const initialFormData: SubjectFormData = {
  name: '',
  code: '',
  department: '',
  description: '',
  credits: '',
  duration: '',
  level: '',
  prerequisites: '',
  teacherId: '',
  maxStudents: '',
  schedule: '',
  classroom: '',
  objectives: '',
  materials: '',
  assessment: '',
};

const departments = [
  'Mathematics',
  'Science',
  'English',
  'History',
  'Geography',
  'Physics',
  'Chemistry',
  'Biology',
  'Computer Science',
  'Physical Education',
  'Art',
  'Music',
];

const teachers = [
  { id: '1', name: 'Dr. John Smith' },
  { id: '2', name: 'Prof. Sarah Johnson' },
  { id: '3', name: 'Dr. Emily Davis' },
  { id: '4', name: 'Mr. Michael Brown' },
];

export default function CreateSubjectPage() {
  const router = useRouter();
  const [currentStep, setCurrentStep] = useState(1);
  const [formData, setFormData] = useState<SubjectFormData>(initialFormData);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const totalSteps = 3;
  const progress = (currentStep / totalSteps) * 100;

  const handleInputChange = (field: keyof SubjectFormData, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleNext = () => {
    if (currentStep < totalSteps) {
      setCurrentStep(prev => prev + 1);
    }
  };

  const handlePrevious = () => {
    if (currentStep > 1) {
      setCurrentStep(prev => prev - 1);
    }
  };

  const handleSubmit = async () => {
    setIsSubmitting(true);
    
    try {
      // TODO: Replace with actual API call
      console.log('Submitting subject data:', formData);
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // Success - redirect to subjects list
      router.push('/dashboard/subjects');
    } catch (error) {
      console.error('Error creating subject:', error);
      // TODO: Show error toast
    } finally {
      setIsSubmitting(false);
    }
  };

  const renderStepContent = () => {
    switch (currentStep) {
      case 1:
        return (
          <div className="space-y-6">
            <div>
              <h3 className="text-lg font-semibold mb-4 flex items-center">
                <BookOpen className="w-5 h-5 mr-2 text-blue-600" />
                Basic Information
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="name">Subject Name *</Label>
                  <Input
                    id="name"
                    value={formData.name}
                    onChange={(e) => handleInputChange('name', e.target.value)}
                    placeholder="e.g., Advanced Mathematics"
                    required
                  />
                </div>
                <div>
                  <Label htmlFor="code">Subject Code *</Label>
                  <Input
                    id="code"
                    value={formData.code}
                    onChange={(e) => handleInputChange('code', e.target.value)}
                    placeholder="e.g., MATH301"
                    required
                  />
                </div>
                <div>
                  <Label htmlFor="department">Department *</Label>
                  <Select value={formData.department} onValueChange={(value) => handleInputChange('department', value)}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select department" />
                    </SelectTrigger>
                    <SelectContent>
                      {departments.map((dept) => (
                        <SelectItem key={dept} value={dept}>
                          {dept}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label htmlFor="level">Level</Label>
                  <Select value={formData.level} onValueChange={(value) => handleInputChange('level', value)}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select level" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="beginner">Beginner</SelectItem>
                      <SelectItem value="intermediate">Intermediate</SelectItem>
                      <SelectItem value="advanced">Advanced</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
              <div className="mt-4">
                <Label htmlFor="description">Description</Label>
                <Textarea
                  id="description"
                  value={formData.description}
                  onChange={(e) => handleInputChange('description', e.target.value)}
                  placeholder="Brief description of the subject..."
                  rows={3}
                />
              </div>
            </div>
          </div>
        );

      case 2:
        return (
          <div className="space-y-6">
            <div>
              <h3 className="text-lg font-semibold mb-4 flex items-center">
                <GraduationCap className="w-5 h-5 mr-2 text-green-600" />
                Academic Details
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="credits">Credits</Label>
                  <Input
                    id="credits"
                    type="number"
                    value={formData.credits}
                    onChange={(e) => handleInputChange('credits', e.target.value)}
                    placeholder="3"
                  />
                </div>
                <div>
                  <Label htmlFor="duration">Duration (weeks)</Label>
                  <Input
                    id="duration"
                    type="number"
                    value={formData.duration}
                    onChange={(e) => handleInputChange('duration', e.target.value)}
                    placeholder="16"
                  />
                </div>
                <div>
                  <Label htmlFor="maxStudents">Max Students</Label>
                  <Input
                    id="maxStudents"
                    type="number"
                    value={formData.maxStudents}
                    onChange={(e) => handleInputChange('maxStudents', e.target.value)}
                    placeholder="30"
                  />
                </div>
                <div>
                  <Label htmlFor="classroom">Classroom</Label>
                  <Input
                    id="classroom"
                    value={formData.classroom}
                    onChange={(e) => handleInputChange('classroom', e.target.value)}
                    placeholder="Room 101"
                  />
                </div>
              </div>
              <div className="mt-4">
                <Label htmlFor="prerequisites">Prerequisites</Label>
                <Textarea
                  id="prerequisites"
                  value={formData.prerequisites}
                  onChange={(e) => handleInputChange('prerequisites', e.target.value)}
                  placeholder="List any prerequisite subjects or requirements..."
                  rows={2}
                />
              </div>
              <div className="mt-4">
                <Label htmlFor="schedule">Schedule</Label>
                <Input
                  id="schedule"
                  value={formData.schedule}
                  onChange={(e) => handleInputChange('schedule', e.target.value)}
                  placeholder="Mon, Wed, Fri - 9:00 AM"
                />
              </div>
            </div>
          </div>
        );

      case 3:
        return (
          <div className="space-y-6">
            <div>
              <h3 className="text-lg font-semibold mb-4 flex items-center">
                <Users className="w-5 h-5 mr-2 text-purple-600" />
                Assignment & Additional Details
              </h3>
              <div className="grid grid-cols-1 gap-4">
                <div>
                  <Label htmlFor="teacherId">Assigned Teacher</Label>
                  <Select value={formData.teacherId} onValueChange={(value) => handleInputChange('teacherId', value)}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select teacher" />
                    </SelectTrigger>
                    <SelectContent>
                      {teachers.map((teacher) => (
                        <SelectItem key={teacher.id} value={teacher.id}>
                          {teacher.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label htmlFor="objectives">Learning Objectives</Label>
                  <Textarea
                    id="objectives"
                    value={formData.objectives}
                    onChange={(e) => handleInputChange('objectives', e.target.value)}
                    placeholder="What students will learn in this subject..."
                    rows={3}
                  />
                </div>
                <div>
                  <Label htmlFor="materials">Required Materials</Label>
                  <Textarea
                    id="materials"
                    value={formData.materials}
                    onChange={(e) => handleInputChange('materials', e.target.value)}
                    placeholder="Textbooks, supplies, equipment needed..."
                    rows={2}
                  />
                </div>
                <div>
                  <Label htmlFor="assessment">Assessment Methods</Label>
                  <Textarea
                    id="assessment"
                    value={formData.assessment}
                    onChange={(e) => handleInputChange('assessment', e.target.value)}
                    placeholder="How students will be evaluated..."
                    rows={2}
                  />
                </div>
              </div>
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <div className="container mx-auto p-4 sm:p-6 lg:p-8 max-w-4xl">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-4">
          <Link href="/dashboard/subjects">
            <Button variant="ghost" size="sm">
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to Subjects
            </Button>
          </Link>
          <div>
            <h1 className="text-2xl sm:text-3xl font-bold tracking-tight flex items-center">
              <Plus className="w-8 h-8 mr-3 text-blue-600" />
              Add New Subject
            </h1>
            <p className="text-muted-foreground mt-1">
              Create a comprehensive subject profile
              <Badge variant="outline" className="ml-2">
                Step {currentStep} of {totalSteps}
              </Badge>
            </p>
          </div>
        </div>
      </div>

      {/* Progress Bar */}
      <div className="mb-8">
        <div className="flex justify-between text-sm text-muted-foreground mb-2">
          <span>Progress</span>
          <span>{Math.round(progress)}% Complete</span>
        </div>
        <Progress value={progress} className="h-2" />
      </div>

      {/* Form Card */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            {currentStep === 1 && <BookOpen className="w-5 h-5 mr-2 text-blue-600" />}
            {currentStep === 2 && <GraduationCap className="w-5 h-5 mr-2 text-green-600" />}
            {currentStep === 3 && <Users className="w-5 h-5 mr-2 text-purple-600" />}
            {currentStep === 1 && 'Basic Information'}
            {currentStep === 2 && 'Academic Details'}
            {currentStep === 3 && 'Assignment & Additional Details'}
          </CardTitle>
          <CardDescription>
            {currentStep === 1 && 'Enter the subject\'s basic information and description'}
            {currentStep === 2 && 'Provide academic details and scheduling information'}
            {currentStep === 3 && 'Assign teacher and add additional subject details'}
          </CardDescription>
        </CardHeader>
        <CardContent>
          {renderStepContent()}
        </CardContent>
      </Card>

      {/* Navigation Buttons */}
      <div className="flex justify-between mt-6">
        <Button
          variant="outline"
          onClick={handlePrevious}
          disabled={currentStep === 1}
        >
          <ArrowLeft className="w-4 h-4 mr-2" />
          Previous
        </Button>

        <div className="flex space-x-2">
          {currentStep < totalSteps ? (
            <Button onClick={handleNext}>
              Next
              <ArrowRight className="w-4 h-4 ml-2" />
            </Button>
          ) : (
            <Button
              onClick={handleSubmit}
              disabled={isSubmitting}
              className="bg-green-600 hover:bg-green-700"
            >
              {isSubmitting ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Creating Subject...
                </>
              ) : (
                <>
                  <Check className="w-4 h-4 mr-2" />
                  Create Subject
                </>
              )}
            </Button>
          )}
        </div>
      </div>
    </div>
  );
}
