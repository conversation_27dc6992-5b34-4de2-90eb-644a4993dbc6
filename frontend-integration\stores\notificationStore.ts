/**
 * Notification Store - Real-time Notification Management
 *
 * Manages application notifications with best practices:
 * - Toast notifications with auto-dismiss
 * - In-app notifications with persistence
 * - Real-time updates via WebSocket/SSE
 * - Notification preferences and settings
 * - Bulk operations and filtering
 * - Sound and visual notification support
 */

import { create } from 'zustand';
import { createJSONStorage, devtools, persist, subscribeWithSelector } from 'zustand/middleware';

// Notification types
export type NotificationType = 'info' | 'success' | 'warning' | 'error';
export type NotificationPriority = 'low' | 'medium' | 'high' | 'urgent';
export type NotificationCategory = 'system' | 'academic' | 'social' | 'administrative' | 'security';

export interface Notification {
  id: string;
  title: string;
  message: string;
  type: NotificationType;
  priority: NotificationPriority;
  category: NotificationCategory;

  // Metadata
  userId?: string;
  targetRole?: string[];
  targetUsers?: string[];

  // Timestamps
  createdAt: string;
  readAt?: string | undefined;
  dismissedAt?: string | undefined;
  expiresAt?: string | undefined;

  // Behavior
  persistent: boolean;
  actionable: boolean;
  autoClose: boolean;
  closeDelay?: number; // milliseconds

  // Actions
  actions?: NotificationAction[];

  // Rich content
  icon?: string;
  image?: string;
  link?: string;
  data?: Record<string, any>;
}

export interface NotificationAction {
  id: string;
  label: string;
  action: string;
  style?: 'primary' | 'secondary' | 'danger';
  data?: Record<string, any>;
}

export interface NotificationPreferences {
  enabled: boolean;
  categories: Record<NotificationCategory, boolean>;
  types: Record<NotificationType, boolean>;
  sound: boolean;
  desktop: boolean;
  email: boolean;
  push: boolean;
  quietHours: {
    enabled: boolean;
    start: string; // HH:mm format
    end: string; // HH:mm format
  };
}

export interface NotificationFilters {
  type?: NotificationType;
  category?: NotificationCategory;
  priority?: NotificationPriority;
  read?: boolean;
  dismissed?: boolean;
  dateFrom?: string;
  dateTo?: string;
}

export interface NotificationState {
  // Data State
  notifications: Record<string, Notification>;
  notificationIds: string[]; // Ordered by creation time (newest first)

  // UI State
  isLoading: boolean;
  error: string | null;

  // Real-time Connection
  isConnected: boolean;
  connectionError: string | null;
  lastSync: number | null;

  // Filtering & Display
  filters: NotificationFilters;
  unreadCount: number;

  // User Preferences
  preferences: NotificationPreferences;

  // Toast Management
  activeToasts: string[]; // Notification IDs currently shown as toasts
  maxToasts: number;

  // Actions - Data Management
  addNotification: (notification: Notification) => void;
  updateNotification: (id: string, updates: Partial<Notification>) => void;
  removeNotification: (id: string) => void;
  clearNotifications: () => void;

  // Actions - CRUD Operations
  fetchNotifications: () => Promise<void>;
  markAsRead: (id: string) => Promise<void>;
  markAllAsRead: () => Promise<void>;
  dismissNotification: (id: string) => Promise<void>;
  dismissAll: () => Promise<void>;

  // Actions - Bulk Operations
  bulkMarkAsRead: (ids: string[]) => Promise<void>;
  bulkDismiss: (ids: string[]) => Promise<void>;
  bulkDelete: (ids: string[]) => Promise<void>;

  // Actions - Toast Management
  showToast: (notification: Notification) => void;
  hideToast: (id: string) => void;
  clearToasts: () => void;

  // Actions - Real-time Connection
  connect: () => void;
  disconnect: () => void;
  reconnect: () => void;

  // Actions - Preferences
  updatePreferences: (preferences: Partial<NotificationPreferences>) => void;
  toggleCategory: (category: NotificationCategory) => void;
  toggleType: (type: NotificationType) => void;

  // Actions - Filtering
  setFilters: (filters: Partial<NotificationFilters>) => void;
  clearFilters: () => void;

  // Actions - Error Handling
  setError: (error: string | null) => void;
  clearError: () => void;

  // Getters
  getNotification: (id: string) => Notification | undefined;
  getUnreadNotifications: () => Notification[];
  getFilteredNotifications: () => Notification[];
  getNotificationsByCategory: (category: NotificationCategory) => Notification[];
  getNotificationsByType: (type: NotificationType) => Notification[];
  shouldShowNotification: (notification: Notification) => boolean;
  isInQuietHours: () => boolean;
}

// Default preferences
const defaultPreferences: NotificationPreferences = {
  enabled: true,
  categories: {
    system: true,
    academic: true,
    social: true,
    administrative: true,
    security: true,
  },
  types: {
    info: true,
    success: true,
    warning: true,
    error: true,
  },
  sound: true,
  desktop: true,
  email: false,
  push: true,
  quietHours: {
    enabled: false,
    start: '22:00',
    end: '08:00',
  },
};

export const useNotificationStore = create<NotificationState>()(
  subscribeWithSelector(
    devtools(
      persist(
        (set, get) => ({
          // Initial State
          notifications: {},
          notificationIds: [],

          // UI State
          isLoading: false,
          error: null,

          // Real-time Connection
          isConnected: false,
          connectionError: null,
          lastSync: null,

          // Filtering & Display
          filters: {},
          unreadCount: 0,

          // User Preferences
          preferences: defaultPreferences,

          // Toast Management
          activeToasts: [],
          maxToasts: 5,

          // Data Management Actions
          addNotification: (notification: Notification) => {
            set(state => {
              const newNotifications = {
                ...state.notifications,
                [notification.id]: notification,
              };

              const newNotificationIds = [notification.id, ...state.notificationIds];
              const newUnreadCount = notification.readAt
                ? state.unreadCount
                : state.unreadCount + 1;

              return {
                notifications: newNotifications,
                notificationIds: newNotificationIds,
                unreadCount: newUnreadCount,
              };
            });

            // Show as toast if appropriate
            const notification_obj = get().notifications[notification.id];
            if (notification_obj && get().shouldShowNotification(notification_obj)) {
              get().showToast(notification_obj);
            }
          },

          updateNotification: (id: string, updates: Partial<Notification>) => {
            set(state => {
              const existingNotification = state.notifications[id];
              if (!existingNotification) return state;

              const updatedNotification = { ...existingNotification, ...updates };
              const wasUnread = !existingNotification.readAt;
              const isNowRead = !!updatedNotification.readAt;

              let unreadCountChange = 0;
              if (wasUnread && isNowRead) {
                unreadCountChange = -1;
              } else if (!wasUnread && !isNowRead) {
                unreadCountChange = 1;
              }

              return {
                notifications: {
                  ...state.notifications,
                  [id]: updatedNotification,
                },
                unreadCount: Math.max(0, state.unreadCount + unreadCountChange),
              };
            });
          },

          removeNotification: (id: string) => {
            set(state => {
              const notification = state.notifications[id];
              const unreadCountChange = notification && !notification.readAt ? -1 : 0;

              const newNotifications = { ...state.notifications };
              delete newNotifications[id];

              return {
                notifications: newNotifications,
                notificationIds: state.notificationIds.filter(nId => nId !== id),
                activeToasts: state.activeToasts.filter(tId => tId !== id),
                unreadCount: Math.max(0, state.unreadCount + unreadCountChange),
              };
            });
          },

          clearNotifications: () => {
            set({
              notifications: {},
              notificationIds: [],
              activeToasts: [],
              unreadCount: 0,
            });
          },

          // CRUD Operations
          fetchNotifications: async () => {
            set({ isLoading: true, error: null });

            try {
              // TODO: Replace with actual API call
              // const response = await apiClient.get('/notifications');
              // const notifications = response.data;

              // Mock API call
              await new Promise(resolve => setTimeout(resolve, 1000));
              const mockNotifications: Notification[] = [
                {
                  id: '1',
                  title: 'Welcome to School Management System',
                  message: 'Your account has been successfully created.',
                  type: 'success',
                  priority: 'medium',
                  category: 'system',
                  createdAt: new Date().toISOString(),
                  persistent: true,
                  actionable: false,
                  autoClose: false,
                },
                {
                  id: '2',
                  title: 'New Assignment Posted',
                  message: 'Mathematics homework has been posted for Grade 10.',
                  type: 'info',
                  priority: 'medium',
                  category: 'academic',
                  createdAt: new Date(Date.now() - 3600000).toISOString(), // 1 hour ago
                  persistent: true,
                  actionable: true,
                  autoClose: false,
                  actions: [
                    {
                      id: 'view',
                      label: 'View Assignment',
                      action: 'navigate',
                      style: 'primary',
                      data: { url: '/assignments/123' },
                    },
                  ],
                },
              ];

              // Process notifications
              const notificationsById = mockNotifications.reduce((acc, notification) => {
                acc[notification.id] = notification;
                return acc;
              }, {} as Record<string, Notification>);

              const unreadCount = mockNotifications.filter(n => !n.readAt).length;

              set({
                notifications: notificationsById,
                notificationIds: mockNotifications.map(n => n.id),
                unreadCount,
                isLoading: false,
                lastSync: Date.now(),
              });
            } catch (error) {
              const errorMessage =
                error instanceof Error ? error.message : 'Failed to fetch notifications';
              set({ error: errorMessage, isLoading: false });
              throw error;
            }
          },

          markAsRead: async (id: string) => {
            const notification = get().notifications[id];
            if (!notification || notification.readAt) return;

            // Optimistic update
            get().updateNotification(id, { readAt: new Date().toISOString() });

            try {
              // TODO: Replace with actual API call
              // await apiClient.patch(`/notifications/${id}/read`);

              // Mock API call
              await new Promise(resolve => setTimeout(resolve, 200));
            } catch (error) {
              // Rollback optimistic update
              const notification = get().notifications[id];
              if (notification) {
                get().updateNotification(id, { readAt: notification.readAt });
              }
              throw error;
            }
          },

          markAllAsRead: async () => {
            const unreadNotifications = get().getUnreadNotifications();
            const now = new Date().toISOString();

            // Optimistic updates
            unreadNotifications.forEach(notification => {
              get().updateNotification(notification.id, { readAt: now });
            });

            try {
              // TODO: Replace with actual API call
              // await apiClient.patch('/notifications/read-all');

              // Mock API call
              await new Promise(resolve => setTimeout(resolve, 500));
            } catch (error) {
              // Rollback optimistic updates
              unreadNotifications.forEach(notification => {
                get().updateNotification(notification.id, { readAt: notification.readAt });
              });
              throw error;
            }
          },

          dismissNotification: async (id: string) => {
            const notification = get().notifications[id];
            if (!notification) return;

            // Optimistic update
            get().updateNotification(id, { dismissedAt: new Date().toISOString() });
            get().hideToast(id);

            try {
              // TODO: Replace with actual API call
              // await apiClient.patch(`/notifications/${id}/dismiss`);

              // Mock API call
              await new Promise(resolve => setTimeout(resolve, 200));
            } catch (error) {
              // Rollback optimistic update
              const notification = get().notifications[id];
              if (notification) {
                get().updateNotification(id, { dismissedAt: notification.dismissedAt });
              }
              throw error;
            }
          },

          dismissAll: async () => {
            const notifications = Object.values(get().notifications);
            const now = new Date().toISOString();

            // Optimistic updates
            notifications.forEach(notification => {
              if (!notification.dismissedAt) {
                get().updateNotification(notification.id, { dismissedAt: now });
              }
            });

            get().clearToasts();

            try {
              // TODO: Replace with actual API call
              // await apiClient.patch('/notifications/dismiss-all');

              // Mock API call
              await new Promise(resolve => setTimeout(resolve, 500));
            } catch (error) {
              // Rollback optimistic updates
              notifications.forEach(notification => {
                if (!notification.dismissedAt) {
                  get().updateNotification(notification.id, {
                    dismissedAt: notification.dismissedAt,
                  });
                }
              });
              throw error;
            }
          },

          // Bulk Operations
          bulkMarkAsRead: async (ids: string[]) => {
            const now = new Date().toISOString();
            const originalStates = ids.map(id => ({
              id,
              readAt: get().notifications[id]?.readAt || null,
            }));

            // Optimistic updates
            ids.forEach(id => {
              get().updateNotification(id, { readAt: now });
            });

            try {
              // TODO: Replace with actual API call
              // await apiClient.patch('/notifications/bulk-read', { ids });

              // Mock API call
              await new Promise(resolve => setTimeout(resolve, 500));
            } catch (error) {
              // Rollback optimistic updates
              originalStates.forEach(({ id, readAt }) => {
                get().updateNotification(id, { readAt: readAt || undefined });
              });
              throw error;
            }
          },

          bulkDismiss: async (ids: string[]) => {
            const now = new Date().toISOString();
            const originalStates = ids.map(id => ({
              id,
              dismissedAt: get().notifications[id]?.dismissedAt || null,
            }));

            // Optimistic updates
            ids.forEach(id => {
              get().updateNotification(id, { dismissedAt: now });
              get().hideToast(id);
            });

            try {
              // TODO: Replace with actual API call
              // await apiClient.patch('/notifications/bulk-dismiss', { ids });

              // Mock API call
              await new Promise(resolve => setTimeout(resolve, 500));
            } catch (error) {
              // Rollback optimistic updates
              originalStates.forEach(({ id, dismissedAt }) => {
                get().updateNotification(id, { dismissedAt: dismissedAt || undefined });
              });
              throw error;
            }
          },

          bulkDelete: async (ids: string[]) => {
            const originalNotifications = ids
              .map(id => get().notifications[id])
              .filter((notification): notification is Notification => notification != null);

            // Optimistic deletes
            ids.forEach(id => {
              get().removeNotification(id);
            });

            try {
              // TODO: Replace with actual API call
              // await apiClient.delete('/notifications/bulk', { data: { ids } });

              // Mock API call
              await new Promise(resolve => setTimeout(resolve, 500));
            } catch (error) {
              // Rollback optimistic deletes
              originalNotifications.forEach(notification => {
                get().addNotification(notification);
              });
              throw error;
            }
          },

          // Toast Management
          showToast: (notification: Notification) => {
            set(state => {
              // Don't show if already showing or if at max capacity
              if (state.activeToasts.includes(notification.id)) {
                return state;
              }

              let newActiveToasts = [...state.activeToasts, notification.id];

              // Remove oldest toasts if at capacity
              if (newActiveToasts.length > state.maxToasts) {
                const toRemove = newActiveToasts.slice(0, newActiveToasts.length - state.maxToasts);
                newActiveToasts = newActiveToasts.slice(-state.maxToasts);

                // Auto-hide removed toasts
                toRemove.forEach(id => {
                  setTimeout(() => get().hideToast(id), 100);
                });
              }

              return { activeToasts: newActiveToasts };
            });

            // Auto-hide toast if configured
            if (notification.autoClose && notification.closeDelay) {
              setTimeout(() => {
                get().hideToast(notification.id);
              }, notification.closeDelay);
            }
          },

          hideToast: (id: string) => {
            set(state => ({
              activeToasts: state.activeToasts.filter(toastId => toastId !== id),
            }));
          },

          clearToasts: () => {
            set({ activeToasts: [] });
          },

          // Real-time Connection (placeholder for WebSocket/SSE)
          connect: () => {
            // TODO: Implement WebSocket/SSE connection
            set({ isConnected: true, connectionError: null });
            console.log('Notification real-time connection established');
          },

          disconnect: () => {
            // TODO: Close WebSocket/SSE connection
            set({ isConnected: false });
            console.log('Notification real-time connection closed');
          },

          reconnect: () => {
            get().disconnect();
            setTimeout(() => {
              get().connect();
            }, 1000);
          },

          // Preferences Management
          updatePreferences: (preferences: Partial<NotificationPreferences>) => {
            set(state => ({
              preferences: { ...state.preferences, ...preferences },
            }));
          },

          toggleCategory: (category: NotificationCategory) => {
            set(state => ({
              preferences: {
                ...state.preferences,
                categories: {
                  ...state.preferences.categories,
                  [category]: !state.preferences.categories[category],
                },
              },
            }));
          },

          toggleType: (type: NotificationType) => {
            set(state => ({
              preferences: {
                ...state.preferences,
                types: {
                  ...state.preferences.types,
                  [type]: !state.preferences.types[type],
                },
              },
            }));
          },

          // Filtering
          setFilters: (filters: Partial<NotificationFilters>) => {
            set(state => ({
              filters: { ...state.filters, ...filters },
            }));
          },

          clearFilters: () => {
            set({ filters: {} });
          },

          // Error Handling
          setError: (error: string | null) => {
            set({ error });
          },

          clearError: () => {
            set({ error: null });
          },

          // Getters
          getNotification: (id: string) => {
            return get().notifications[id];
          },

          getUnreadNotifications: () => {
            const { notifications, notificationIds } = get();
            return notificationIds
              .map(id => notifications[id])
              .filter(
                (notification): notification is Notification =>
                  notification != null && !notification.readAt
              );
          },

          getFilteredNotifications: () => {
            const { notifications, notificationIds, filters } = get();

            return notificationIds
              .map(id => notifications[id])
              .filter((notification): notification is Notification => {
                if (!notification) return false;

                if (filters.type && notification.type !== filters.type) return false;
                if (filters.category && notification.category !== filters.category) return false;
                if (filters.priority && notification.priority !== filters.priority) return false;

                if (filters.read !== undefined) {
                  const isRead = !!notification.readAt;
                  if (filters.read !== isRead) return false;
                }

                if (filters.dismissed !== undefined) {
                  const isDismissed = !!notification.dismissedAt;
                  if (filters.dismissed !== isDismissed) return false;
                }

                if (filters.dateFrom && notification.createdAt < filters.dateFrom) return false;
                if (filters.dateTo && notification.createdAt > filters.dateTo) return false;

                return true;
              });
          },

          getNotificationsByCategory: (category: NotificationCategory) => {
            const { notifications, notificationIds } = get();
            return notificationIds
              .map(id => notifications[id])
              .filter(
                (notification): notification is Notification =>
                  notification != null && notification.category === category
              );
          },

          getNotificationsByType: (type: NotificationType) => {
            const { notifications, notificationIds } = get();
            return notificationIds
              .map(id => notifications[id])
              .filter(
                (notification): notification is Notification =>
                  notification != null && notification.type === type
              );
          },

          shouldShowNotification: (notification: Notification) => {
            const { preferences } = get();

            // Check if notifications are enabled
            if (!preferences.enabled) return false;

            // Check category preferences
            if (!preferences.categories[notification.category]) return false;

            // Check type preferences
            if (!preferences.types[notification.type]) return false;

            // Check quiet hours
            if (get().isInQuietHours()) {
              // Only show urgent notifications during quiet hours
              return notification.priority === 'urgent';
            }

            return true;
          },

          isInQuietHours: () => {
            const { preferences } = get();

            if (!preferences.quietHours.enabled) return false;

            const now = new Date();
            const currentTime = now.getHours() * 60 + now.getMinutes();

            const [startHour = 0, startMin = 0] = preferences.quietHours.start
              .split(':')
              .map(Number);
            const [endHour = 0, endMin = 0] = preferences.quietHours.end.split(':').map(Number);

            const startTime = startHour * 60 + startMin;
            const endTime = endHour * 60 + endMin;

            // Handle overnight quiet hours (e.g., 22:00 to 08:00)
            if (startTime > endTime) {
              return currentTime >= startTime || currentTime <= endTime;
            } else {
              return currentTime >= startTime && currentTime <= endTime;
            }
          },
        }),
        {
          name: 'notification-storage',
          storage: createJSONStorage(() => localStorage),
          partialize: state => ({
            preferences: state.preferences,
            // Don't persist actual notifications for privacy and freshness
          }),
        }
      ),
      {
        name: 'notification-store',
        enabled: process.env.NODE_ENV === 'development',
      }
    )
  )
);

// Selectors for performance optimization
export const useNotificationSelectors = {
  // Basic selectors
  notifications: () => useNotificationStore(state => state.notifications),
  notificationIds: () => useNotificationStore(state => state.notificationIds),
  unreadCount: () => useNotificationStore(state => state.unreadCount),
  activeToasts: () => useNotificationStore(state => state.activeToasts),

  // UI state selectors
  isLoading: () => useNotificationStore(state => state.isLoading),
  error: () => useNotificationStore(state => state.error),
  isConnected: () => useNotificationStore(state => state.isConnected),

  // Computed selectors
  unreadNotifications: () => useNotificationStore(state => state.getUnreadNotifications()),
  filteredNotifications: () => useNotificationStore(state => state.getFilteredNotifications()),

  // Preferences
  preferences: () => useNotificationStore(state => state.preferences),

  // Specific notification selector
  notification: (id: string) => useNotificationStore(state => state.getNotification(id)),
};

// Action selectors
export const useNotificationActions = {
  // Data actions
  addNotification: () => useNotificationStore(state => state.addNotification),
  updateNotification: () => useNotificationStore(state => state.updateNotification),
  removeNotification: () => useNotificationStore(state => state.removeNotification),
  clearNotifications: () => useNotificationStore(state => state.clearNotifications),

  // CRUD actions
  fetchNotifications: () => useNotificationStore(state => state.fetchNotifications),
  markAsRead: () => useNotificationStore(state => state.markAsRead),
  markAllAsRead: () => useNotificationStore(state => state.markAllAsRead),
  dismissNotification: () => useNotificationStore(state => state.dismissNotification),
  dismissAll: () => useNotificationStore(state => state.dismissAll),

  // Bulk actions
  bulkMarkAsRead: () => useNotificationStore(state => state.bulkMarkAsRead),
  bulkDismiss: () => useNotificationStore(state => state.bulkDismiss),
  bulkDelete: () => useNotificationStore(state => state.bulkDelete),

  // Toast actions
  showToast: () => useNotificationStore(state => state.showToast),
  hideToast: () => useNotificationStore(state => state.hideToast),
  clearToasts: () => useNotificationStore(state => state.clearToasts),

  // Connection actions
  connect: () => useNotificationStore(state => state.connect),
  disconnect: () => useNotificationStore(state => state.disconnect),
  reconnect: () => useNotificationStore(state => state.reconnect),

  // Preference actions
  updatePreferences: () => useNotificationStore(state => state.updatePreferences),
  toggleCategory: () => useNotificationStore(state => state.toggleCategory),
  toggleType: () => useNotificationStore(state => state.toggleType),

  // Filter actions
  setFilters: () => useNotificationStore(state => state.setFilters),
  clearFilters: () => useNotificationStore(state => state.clearFilters),

  // Error actions
  setError: () => useNotificationStore(state => state.setError),
  clearError: () => useNotificationStore(state => state.clearError),
};
