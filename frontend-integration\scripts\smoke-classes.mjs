// scripts/smoke-classes.mjs - Smoke test for classes API
// Usage: set API_TOKEN=eyJ... && node scripts/smoke-classes.mjs

const base = (process.env.NEXT_PUBLIC_API_URL || 'http://127.0.0.1:8000/api/v1').replace(/\/$/, '');
const token = process.env.API_TOKEN; // paste a valid JWT or export it before running

console.log('🔍 Classes API Smoke Test');
console.log('Base URL:', base);
console.log('Token:', token ? `${token.substring(0, 20)}...` : '❌ NOT SET');

(async () => {
  if (!token) {
    console.error('❌ No API_TOKEN set. Example:');
    console.error('Windows PowerShell: $env:API_TOKEN="eyJ..."; node scripts/smoke-classes.mjs');
    console.error('Windows CMD: set API_TOKEN=eyJ... && node scripts/smoke-classes.mjs');
    console.error('Linux/Mac: API_TOKEN=eyJ... node scripts/smoke-classes.mjs');
    process.exit(1);
  }

  // Test 1: GET /api/v1/classes/
  const classesUrl = `${base}/classes/`;
  console.log('\n🚀 Testing Classes List:', classesUrl);

  try {
    const res = await fetch(classesUrl, {
      headers: {
        Authorization: `Bearer ${token}`,
        'Content-Type': 'application/json',
      },
      redirect: 'manual', // Handle redirects manually to detect 307s
    });

    console.log('📊 Response:', res.status, res.statusText);

    // Check for redirect issues
    if (res.status === 307) {
      console.warn('⚠️  Got 307 redirect - this indicates missing trailing slash issue');
      console.warn('🔗 Redirect Location:', res.headers.get('location'));
    }

    const text = await res.text();
    let data;

    try {
      data = JSON.parse(text);
    } catch (e) {
      console.log('📄 Raw Response:', text);
      return;
    }

    if (res.status === 200) {
      if (Array.isArray(data)) {
        console.log('✅ Success: Got classes array');
        console.log('📈 Count:', data.length);
        if (data.length > 0) {
          console.log('🏫 Sample class:', {
            id: data[0].id,
            name: data[0].name,
            grade: data[0].grade,
            section: data[0].section,
            capacity: data[0].capacity,
            teacher_name: data[0].teacher_name,
          });
        } else {
          console.log("📝 Result: Empty list (this is OK - frontend will show 'No classes found')");
        }
      } else {
        console.log('⚠️ Unexpected format (not array):', data);
      }
    } else if (res.status === 404) {
      console.log('📝 Result: 404 (backend returns 404 for empty list - frontend handles this)');
    } else if (res.status === 401) {
      console.log('🔐 Result: 401 Unauthorized - check your token');
    } else if (res.status === 403) {
      console.log('🚫 Result: 403 Forbidden - insufficient permissions');
    } else {
      console.log('❌ Unexpected status:', res.status);
      console.log('📄 Response:', data);
    }
  } catch (error) {
    console.error('💥 Request failed:', error.message);

    if (error.code === 'ECONNREFUSED') {
      console.error('🔌 Connection refused - is the backend server running?');
      console.error('💡 Try: Start your FastAPI server on http://127.0.0.1:8000');
    }
  }

  // Test 2: GET /api/v1/classes/stats/ (with trailing slash to avoid 307 redirects)
  const statsUrl = `${base}/classes/stats`;
  console.log('\n🚀 Testing Classes Stats:', statsUrl);

  try {
    const res = await fetch(statsUrl, {
      headers: {
        Authorization: `Bearer ${token}`,
        'Content-Type': 'application/json',
      },
      redirect: 'manual', // Handle redirects manually to detect 307s
    });

    console.log('📊 Stats Response:', res.status, res.statusText);

    // Check for redirect issues
    if (res.status === 307) {
      console.warn('⚠️  Got 307 redirect on stats - this indicates missing trailing slash issue');
      console.warn('🔗 Redirect Location:', res.headers.get('location'));
    }

    const text = await res.text();
    let data;

    try {
      data = JSON.parse(text);
    } catch (e) {
      console.log('📄 Raw Stats Response:', text);
      return;
    }

    if (res.status === 200) {
      if (typeof data === 'object' && data !== null) {
        console.log('✅ Success: Got stats object');
        console.log('📊 Stats:', {
          total: data.total,
          active: data.active,
          inactive: data.inactive,
          totalStudents: data.totalStudents,
          averageCapacity: data.averageCapacity,
        });
      } else {
        console.log('⚠️ Unexpected stats format:', data);
      }
    } else if (res.status === 404) {
      console.log('📝 Stats Result: 404 (stats endpoint not implemented yet)');
    } else if (res.status === 401) {
      console.log('🔐 Stats Result: 401 Unauthorized - check your token');
    } else if (res.status === 403) {
      console.log('🚫 Stats Result: 403 Forbidden - insufficient permissions');
    } else {
      console.log('❌ Unexpected stats status:', res.status);
      console.log('📄 Stats Response:', data);
    }
  } catch (error) {
    console.error('💥 Stats request failed:', error.message);

    if (error.code === 'ECONNREFUSED') {
      console.error('🔌 Connection refused - is the backend server running?');
      console.error('💡 Try: Start your FastAPI server on http://127.0.0.1:8000');
    }
  }

  // Test 3: POST /api/v1/classes/ (Create Class)
  console.log('\n🚀 Testing Create Class (POST)');

  const testClassData = {
    name: 'Test Class 10A',
    grade: '10',
    section: 'A',
    capacity: 30,
    teacher_id: 'test-teacher-id',
    academic_year: '2024-2025',
    status: 'ACTIVE',
    room: 'Room 101',
    schedule: 'Mon-Fri 9:00-10:00',
  };

  try {
    const createRes = await fetch(`${base}/classes/`, {
      method: 'POST',
      headers: {
        Authorization: `Bearer ${token}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(testClassData),
      redirect: 'manual',
    });

    console.log('📊 Create Response:', createRes.status, createRes.statusText);

    if (createRes.status === 307) {
      console.warn('⚠️  Got 307 redirect on create - this indicates missing trailing slash issue');
      console.warn('🔗 Redirect Location:', createRes.headers.get('location'));
    }

    const createText = await createRes.text();
    let createData;

    try {
      createData = JSON.parse(createText);
    } catch (e) {
      console.log('📄 Raw Create Response:', createText);
    }

    if (createRes.status === 201 || createRes.status === 200) {
      console.log('✅ Create Success: Class created');
      console.log('🆔 Created Class ID:', createData?.id);
    } else if (createRes.status === 401) {
      console.log('🔐 Create Result: 401 Unauthorized - check your token');
    } else if (createRes.status === 403) {
      console.log('🚫 Create Result: 403 Forbidden - insufficient permissions');
    } else if (createRes.status === 422) {
      console.log('📝 Create Result: 422 Validation Error');
      console.log('📄 Validation Details:', createData);
    } else {
      console.log('❌ Unexpected create status:', createRes.status);
      console.log('📄 Create Response:', createData);
    }
  } catch (error) {
    console.error('💥 Create request failed:', error.message);
  }

  console.log('\n🎯 Expected Results:');
  console.log('✅ GET /classes/ → 200 + [] or 200 + [classes]');
  console.log('✅ GET /classes/stats/ → 200 + {stats}');
  console.log('✅ POST /classes/ → 201 + {created_class}');
  console.log('❌ 307 redirects → Missing trailing slash');
  console.log('🔐 401/403 → Authentication/Authorization issues');
})();
