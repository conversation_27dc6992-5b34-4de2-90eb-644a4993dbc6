'use client';

import { <PERSON><PERSON><PERSON><PERSON>, Edit, Trash } from 'lucide-react';
import { useParams, useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

// Temporary mock data — replace with API call later
const mockFeeRecords = [
  {
    id: 1,
    studentName: '<PERSON>',
    studentId: 'STU001',
    class: 'Grade 10',
    feeType: 'Tuition Fee',
    amount: 1500,
    dueDate: '2024-01-15',
    status: 'Paid',
    paidDate: '2024-01-10',
  },
  {
    id: 2,
    studentName: '<PERSON>',
    studentId: 'STU002',
    class: 'Grade 9',
    feeType: 'Tuition Fee',
    amount: 1500,
    dueDate: '2024-01-15',
    status: 'Pending',
    paidDate: null,
  },
  {
    id: 3,
    studentName: '<PERSON>',
    studentId: 'STU003',
    class: 'Grade 11',
    feeType: 'Lab Fee',
    amount: 300,
    dueDate: '2024-01-20',
    status: 'Overdue',
    paidDate: null,
  },
];

export default function FeeDetailsPage() {
  const { id } = useParams();
  const router = useRouter();
  const [record, setRecord] = useState<any>(null);

  useEffect(() => {
    const found = mockFeeRecords.find(r => r.id === Number(id));
    setRecord(found || null);
  }, [id]);

  const handleDelete = () => {
    if (confirm('Are you sure you want to delete this fee record?')) {
      console.log(`Deleted fee record with ID: ${id}`);
      router.push('/dashboard/students-fee');
    }
  };

  if (!record) {
    return (
      <div className='container mx-auto p-6'>
        <p className='text-red-500'>Fee record not found.</p>
        <Button className='mt-4' onClick={() => router.push('/dashboard/students-fee')}>
          <ArrowLeft className='w-4 h-4 mr-2' /> Back to Fee List
        </Button>
      </div>
    );
  }

  return (
    <div className='container mx-auto p-6 max-w-3xl space-y-6'>
      {/* Header with Actions */}
      <div className='flex items-center justify-between'>
        <Button variant='outline' onClick={() => router.push('/dashboard/students-fee')}>
          <ArrowLeft className='w-4 h-4 mr-2' /> Back
        </Button>
        <div className='flex gap-2'>
          <Button
            variant='outline'
            onClick={() => router.push(`/dashboard/student-fee/${record.id}/edit`)}
          >
            <Edit className='w-4 h-4 mr-2' /> Edit
          </Button>
          <Button variant='destructive' onClick={handleDelete}>
            <Trash className='w-4 h-4 mr-2' /> Delete
          </Button>
        </div>
      </div>

      {/* Details Card */}
      <Card>
        <CardHeader>
          <CardTitle>Fee Details</CardTitle>
        </CardHeader>
        <CardContent className='space-y-4'>
          <div className='grid grid-cols-1 sm:grid-cols-2 gap-4'>
            <DetailItem label='Student Name' value={record.studentName} />
            <DetailItem label='Student ID' value={record.studentId} />
            <DetailItem label='Class' value={record.class} />
            <DetailItem label='Fee Type' value={record.feeType} />
            <DetailItem label='Amount' value={`$${record.amount}`} />
            <DetailItem label='Due Date' value={record.dueDate} />
            <DetailItem
              label='Status'
              value={
                <Badge
                  variant={
                    record.status === 'Paid'
                      ? 'default'
                      : record.status === 'Pending'
                      ? 'secondary'
                      : 'destructive'
                  }
                >
                  {record.status}
                </Badge>
              }
            />
            <DetailItem label='Paid Date' value={record.paidDate || '—'} />
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

function DetailItem({ label, value }: { label: string; value: any }) {
  return (
    <div>
      <p className='text-sm text-muted-foreground'>{label}</p>
      <p className='font-medium'>{value}</p>
    </div>
  );
}
