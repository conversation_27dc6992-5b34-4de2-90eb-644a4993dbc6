/**
 * User Management Validation Schemas
 * 
 * Zod schemas for:
 * - User CRUD operations
 * - Role management
 * - Status updates
 */

import { z } from 'zod';

// User roles enum
export const UserRole = z.enum(['Admin', 'Teacher', 'Accountant', 'Clerk', 'Student']);
export type UserRoleType = z.infer<typeof UserRole>;

// User status enum
export const UserStatus = z.enum(['Active', 'Inactive', 'Pending']);
export type UserStatusType = z.infer<typeof UserStatus>;

// Base user schema
export const UserSchema = z.object({
  id: z.string().optional(),
  name: z.string().min(1, 'Name is required').max(100, 'Name must be less than 100 characters'),
  email: z.string().email('Invalid email address').max(255, 'Email must be less than 255 characters'),
  role: UserRole,
  status: UserStatus.default('Active'),
  phone: z.string().optional(),
  avatar: z.string().url().optional(),
  lastLogin: z.string().optional(),
  createdAt: z.string().optional(),
  updatedAt: z.string().optional(),
  createdBy: z.string().optional(),
});

// Create user schema (includes password)
export const CreateUserSchema = UserSchema.omit({ 
  id: true, 
  lastLogin: true, 
  createdAt: true, 
  updatedAt: true,
  createdBy: true 
}).extend({
  password: z.string()
    .min(8, 'Password must be at least 8 characters')
    .max(128, 'Password must be less than 128 characters')
    .regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/, 'Password must contain at least one lowercase letter, one uppercase letter, and one number'),
  confirmPassword: z.string(),
}).refine((data) => data.password === data.confirmPassword, {
  message: "Passwords don't match",
  path: ["confirmPassword"],
});

// Update user schema (password optional)
export const UpdateUserSchema = UserSchema.omit({ 
  id: true, 
  lastLogin: true, 
  createdAt: true, 
  updatedAt: true,
  createdBy: true 
}).extend({
  password: z.string()
    .min(8, 'Password must be at least 8 characters')
    .max(128, 'Password must be less than 128 characters')
    .regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/, 'Password must contain at least one lowercase letter, one uppercase letter, and one number')
    .optional(),
  confirmPassword: z.string().optional(),
}).refine((data) => {
  if (data.password && data.confirmPassword) {
    return data.password === data.confirmPassword;
  }
  return true;
}, {
  message: "Passwords don't match",
  path: ["confirmPassword"],
});

// Status update schema
export const UpdateUserStatusSchema = z.object({
  status: UserStatus,
});

// User list query schema
export const UserListQuerySchema = z.object({
  search: z.string().optional(),
  role: UserRole.optional(),
  status: UserStatus.optional(),
  page: z.number().int().min(1).default(1),
  limit: z.number().int().min(1).max(100).default(20),
  sortBy: z.enum(['name', 'email', 'role', 'status', 'createdAt', 'lastLogin']).default('name'),
  sortOrder: z.enum(['asc', 'desc']).default('asc'),
});

// User list response schema
export const UserListResponseSchema = z.object({
  users: z.array(UserSchema),
  pagination: z.object({
    page: z.number(),
    limit: z.number(),
    total: z.number(),
    totalPages: z.number(),
  }),
});

// Export types
export type User = z.infer<typeof UserSchema>;
export type CreateUser = z.infer<typeof CreateUserSchema>;
export type UpdateUser = z.infer<typeof UpdateUserSchema>;
export type UpdateUserStatus = z.infer<typeof UpdateUserStatusSchema>;
export type UserListQuery = z.infer<typeof UserListQuerySchema>;
export type UserListResponse = z.infer<typeof UserListResponseSchema>;

// Role permissions mapping
export const ROLE_PERMISSIONS = {
  Admin: ['all'],
  Teacher: ['students', 'classes', 'exams', 'attendance'],
  Accountant: ['fees', 'payments', 'reports'],
  Clerk: ['students', 'basic_reports'],
  Student: ['profile', 'assignments', 'results'],
} as const;

// Role hierarchy (for access control)
export const ROLE_HIERARCHY = {
  Admin: 5,
  Teacher: 4,
  Accountant: 3,
  Clerk: 2,
  Student: 1,
} as const;

// Helper functions
export const canManageUser = (currentUserRole: UserRoleType, targetUserRole: UserRoleType): boolean => {
  return ROLE_HIERARCHY[currentUserRole] > ROLE_HIERARCHY[targetUserRole];
};

export const canAccessUserManagement = (userRole: UserRoleType): boolean => {
  return userRole === 'Admin';
};

export const getRoleColor = (role: UserRoleType): string => {
  const colors = {
    Admin: 'bg-red-100 text-red-800',
    Teacher: 'bg-blue-100 text-blue-800',
    Accountant: 'bg-green-100 text-green-800',
    Clerk: 'bg-yellow-100 text-yellow-800',
    Student: 'bg-purple-100 text-purple-800',
  };
  return colors[role];
};

export const getStatusColor = (status: UserStatusType): string => {
  const colors = {
    Active: 'bg-green-100 text-green-800',
    Inactive: 'bg-gray-100 text-gray-800',
    Pending: 'bg-yellow-100 text-yellow-800',
  };
  return colors[status];
};
