# 📋 **Trailing Slash Policy - FastAPI + Next.js**

## 🎯 **Policy Overview**

This document defines the consistent trailing slash policy for our FastAPI backend and Next.js frontend integration.

---

## 📐 **URL Conventions**

### **✅ Collection Endpoints - WITH Trailing Slash**
Collection endpoints that return arrays of resources should include a trailing slash:

```
GET /api/v1/classes/     → Returns array of classes
GET /api/v1/teachers/    → Returns array of teachers  
GET /api/v1/students/    → Returns array of students
POST /api/v1/classes/    → Creates new class
POST /api/v1/teachers/   → Creates new teacher
```

### **✅ Individual Resources - NO Trailing Slash**
Individual resource endpoints should NOT include a trailing slash:

```
GET /api/v1/classes/123     → Returns specific class
GET /api/v1/teachers/456    → Returns specific teacher
PUT /api/v1/classes/123     → Updates specific class
DELETE /api/v1/teachers/456 → Deletes specific teacher
```

### **✅ Sub-Routes - NO Trailing Slash**
Sub-route endpoints (actions, stats, search) should NOT include a trailing slash:

```
GET /api/v1/classes/stats    → Returns class statistics
GET /api/v1/teachers/stats   → Returns teacher statistics
GET /api/v1/classes/search   → Search classes
POST /api/v1/classes/bulk    → Bulk operations
```

---

## 🔄 **Redirect Behavior**

### **Expected Redirects (307 Temporary Redirect)**
FastAPI should automatically redirect collection endpoints without trailing slash:

```
GET /api/v1/classes   → 307 Redirect → /api/v1/classes/
GET /api/v1/teachers  → 307 Redirect → /api/v1/teachers/
GET /api/v1/students  → 307 Redirect → /api/v1/students/
```

### **No Redirects Expected**
These endpoints should respond directly without redirects:

```
✅ GET /api/v1/classes/        → 200 OK (direct response)
✅ GET /api/v1/classes/stats   → 200 OK (direct response)  
✅ GET /api/v1/classes/123     → 200 OK (direct response)
```

---

## 🛠 **Implementation Guidelines**

### **Backend (FastAPI)**

```python
# Router configuration with dual endpoint handling
@router.get("/")
@router.get("")  # Handle both with and without trailing slash
async def get_collection():
    return [{"id": 1, "name": "Item"}]

@router.get("/stats")  # Sub-routes without trailing slash
async def get_stats():
    return {"total": 10, "active": 8}

@router.get("/{item_id}")  # Individual resources without trailing slash
async def get_item(item_id: str):
    return {"id": item_id, "name": "Item"}
```

### **Frontend (Next.js)**

```typescript
// API service endpoints
const ENDPOINTS = {
  // Collection endpoints - WITH trailing slash
  items: '/items/',
  
  // Individual resources - NO trailing slash  
  item: (id: string) => `/items/${id}`,
  
  // Sub-routes - NO trailing slash
  itemStats: '/items/stats',
  itemSearch: '/items/search',
  itemBulk: '/items/bulk',
} as const;
```

---

## 🧪 **Testing Standards**

### **Automated Tests Should Verify**

1. **Collection endpoints with trailing slash** → `200 OK` or `401 Unauthorized`
2. **Sub-routes without trailing slash** → `200 OK` or `401 Unauthorized`  
3. **Collection endpoints without trailing slash** → `307 Redirect`
4. **No unexpected redirects** on properly formatted URLs

### **Test Script Example**

```bash
# Test with authentication
$env:API_TOKEN="jwt_token"; node scripts/test-api-endpoints.mjs

# Expected results:
# ✅ /classes/ → 200 OK
# ✅ /classes/stats → 200 OK  
# ✅ /classes → 307 Redirect
```

---

## ❌ **Common Mistakes to Avoid**

### **Backend Mistakes**
- ❌ Defining only `/classes` without `/classes/` (causes redirects)
- ❌ Adding trailing slash to sub-routes: `/classes/stats/` 
- ❌ Inconsistent slash handling between similar endpoints

### **Frontend Mistakes**  
- ❌ Using `/classes` instead of `/classes/` for collections
- ❌ Adding trailing slash to sub-routes: `/classes/stats/`
- ❌ Inconsistent patterns between different services

### **Testing Mistakes**
- ❌ Not testing both with and without trailing slash
- ❌ Expecting 200 OK from collection endpoints without slash
- ❌ Not verifying redirect locations are correct

---

## 📊 **Current Status**

### **✅ Compliant Endpoints**
- Classes collection: `/api/v1/classes/` → `200 OK`
- Classes stats: `/api/v1/classes/stats` → `200 OK` (after backend fix)
- Classes redirect: `/api/v1/classes` → `307 Redirect`

### **🔧 Needs Backend Fix**
- Teachers collection: `/api/v1/teachers/` → Currently `307 Redirect` (should be `200 OK`)
- Students collection: `/api/v1/students/` → Currently `307 Redirect` (should be `200 OK`)
- Teachers stats: `/api/v1/teachers/stats` → Currently `404 Not Found` (should be `200 OK`)

### **✅ Frontend Already Compliant**
All frontend services (`classService.ts`, `teacherService.ts`, `studentService.ts`) already follow the correct trailing slash policy.

---

## 🎯 **Benefits of This Policy**

1. **Consistency** - Predictable URL patterns across all endpoints
2. **SEO Friendly** - Search engines prefer consistent trailing slash usage  
3. **Cache Efficiency** - Reduces duplicate cache entries for same resources
4. **Developer Experience** - Clear rules reduce confusion and bugs
5. **FastAPI Compatibility** - Aligns with FastAPI's automatic redirect behavior

---

## 📚 **References**

- [FastAPI Path Parameters](https://fastapi.tiangolo.com/tutorial/path-params/)
- [HTTP 307 Temporary Redirect](https://developer.mozilla.org/en-US/docs/Web/HTTP/Status/307)
- [REST API URL Design Best Practices](https://restfulapi.net/resource-naming/)

---

**Last Updated**: 2024-01-01  
**Status**: ✅ Policy Defined, 🔧 Backend Implementation Pending
