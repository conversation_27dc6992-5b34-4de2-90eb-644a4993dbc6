/**
 * Mock Classes Data for Development
 *
 * Provides realistic class data for testing and development
 */

import { Class } from '@/schemas/zodSchemas';
import type { PaginatedResponse } from '@/types';

// Mock classes data
export const mockClasses: Class[] = [
  {
    id: '1',
    name: 'Grade 10 - Mathematics A',
    grade: '10',
    section: 'A',
    capacity: 30,
    enrolled: 28,
    teacher_id: '1',
    teacher_name: 'Dr. <PERSON>',
    room: 'Room 101',
    schedule: 'Mon, Wed, Fri - 9:00 AM',
    academic_year: '2024-2025',
    status: 'ACTIVE',
    subjects: ['Mathematics', 'Algebra', 'Geometry'],
    class_monitor: '<PERSON>',
    created_at: '2024-01-15T08:00:00Z',
    updated_at: '2024-01-15T08:00:00Z',
  },
  {
    id: '2',
    name: 'Grade 9 - Science B',
    grade: '9',
    section: 'B',
    capacity: 25,
    enrolled: 23,
    teacher_id: '2',
    teacher_name: 'Prof<PERSON> <PERSON>',
    room: 'Lab 201',
    schedule: '<PERSON><PERSON>, Thu - 10:30 AM',
    academic_year: '2024-2025',
    status: 'ACTIVE',
    subjects: ['Physics', 'Chemistry', 'Biology'],
    class_monitor: '<PERSON>',
    created_at: '2024-01-15T08:00:00Z',
    updated_at: '2024-01-15T08:00:00Z',
  },
  {
    id: '3',
    name: 'Grade 11 - English Literature',
    grade: '11',
    section: 'A',
    capacity: 22,
    enrolled: 20,
    teacher_id: '3',
    teacher_name: 'Ms. <PERSON> <PERSON>',
    room: 'Room 305',
    schedule: 'Mon, Wed, Fri - 2:00 PM',
    academic_year: '2024-2025',
    status: 'ACTIVE',
    subjects: ['English Literature', 'Creative Writing'],
    class_monitor: 'Carol Brown',
    created_at: '2024-01-15T08:00:00Z',
    updated_at: '2024-01-15T08:00:00Z',
  },
  {
    id: '4',
    name: 'Grade 12 - Physics Advanced',
    grade: '12',
    section: 'A',
    capacity: 20,
    enrolled: 18,
    teacher_id: '4',
    teacher_name: 'Dr. Michael Brown',
    room: 'Lab 102',
    schedule: 'Tue, Thu - 1:00 PM',
    academic_year: '2024-2025',
    status: 'ACTIVE',
    subjects: ['Advanced Physics', 'Quantum Mechanics'],
    class_monitor: 'David Lee',
    created_at: '2024-01-15T08:00:00Z',
    updated_at: '2024-01-15T08:00:00Z',
  },
  {
    id: '5',
    name: 'Grade 8 - History',
    grade: '8',
    section: 'C',
    capacity: 28,
    enrolled: 25,
    teacher_id: '5',
    teacher_name: 'Mr. James Wilson',
    room: 'Room 203',
    schedule: 'Mon, Wed, Fri - 11:00 AM',
    academic_year: '2024-2025',
    status: 'ACTIVE',
    subjects: ['World History', 'Geography'],
    class_monitor: 'Eva Martinez',
    created_at: '2024-01-15T08:00:00Z',
    updated_at: '2024-01-15T08:00:00Z',
  },
  {
    id: '6',
    name: 'Grade 7 - Art & Design',
    grade: '7',
    section: 'B',
    capacity: 15,
    enrolled: 14,
    teacher_id: '6',
    teacher_name: 'Ms. Lisa Anderson',
    room: 'Art Studio',
    schedule: 'Tue, Thu - 3:00 PM',
    academic_year: '2024-2025',
    status: 'ACTIVE',
    subjects: ['Visual Arts', 'Design Principles'],
    class_monitor: 'Frank Garcia',
    created_at: '2024-01-15T08:00:00Z',
    updated_at: '2024-01-15T08:00:00Z',
  },
];

// Helper functions for mock data manipulation
export function getMockClassById(id: string): Class | undefined {
  return mockClasses.find(classItem => classItem.id === id);
}

export function createMockClass(classData: Omit<Class, 'id' | 'created_at' | 'updated_at'>): Class {
  const newClass: Class = {
    ...classData,
    id: (mockClasses.length + 1).toString(),
    enrolled: 0,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
  };

  mockClasses.push(newClass);
  return newClass;
}

export function updateMockClass(id: string, updates: Partial<Class>): Class | null {
  const index = mockClasses.findIndex(classItem => classItem.id === id);
  if (index === -1) return null;

  mockClasses[index] = {
    ...mockClasses[index],
    ...updates,
    updated_at: new Date().toISOString(),
  };

  return mockClasses[index];
}

export function deleteMockClass(id: string): boolean {
  const index = mockClasses.findIndex(classItem => classItem.id === id);
  if (index === -1) return false;

  mockClasses.splice(index, 1);
  return true;
}

export function filterMockClasses(filters: {
  search?: string;
  grade?: string;
  status?: string;
  teacher_id?: string;
  academic_year?: string;
}): Class[] {
  return mockClasses.filter(classItem => {
    // Search filter
    if (filters.search) {
      const searchTerm = filters.search.toLowerCase();
      const searchFields = [
        classItem.name,
        classItem.grade,
        classItem.section,
        classItem.teacher_name || '',
        classItem.room || '',
        ...(classItem.subjects || []),
      ].join(' ').toLowerCase();

      if (!searchFields.includes(searchTerm)) {
        return false;
      }
    }

    // Grade filter
    if (filters.grade && classItem.grade !== filters.grade) {
      return false;
    }

    // Status filter
    if (filters.status && classItem.status !== filters.status) {
      return false;
    }

    // Teacher filter
    if (filters.teacher_id && classItem.teacher_id !== filters.teacher_id) {
      return false;
    }

    // Academic year filter
    if (filters.academic_year && classItem.academic_year !== filters.academic_year) {
      return false;
    }

    return true;
  });
}

export function paginateMockClasses(
  classes: Class[],
  page: number,
  pageSize: number
): PaginatedResponse<Class> {
  const startIndex = (page - 1) * pageSize;
  const endIndex = startIndex + pageSize;
  const paginatedData = classes.slice(startIndex, endIndex);

  return {
    data: paginatedData,
    total: classes.length,
    page,
    pageSize,
    totalPages: Math.ceil(classes.length / pageSize),
  };
}