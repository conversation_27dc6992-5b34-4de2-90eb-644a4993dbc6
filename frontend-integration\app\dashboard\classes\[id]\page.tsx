'use client';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import {
  ArrowLeft,
  BookOpen,
  Calendar,
  Clock,
  Edit,
  GraduationCap,
  MapPin,
  Trash2,
  User,
  Users,
} from 'lucide-react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { useClass, useDeleteClass } from '@/hooks/useClasses';
import { useAuth } from '@/hooks/useAuth';
import { useToast } from '@/hooks/use-toast';
import { canEditClass, canDeleteClass, canViewClass } from '@/lib/permissions';

export default function ClassDetailPage({ params }: { params: { id: string } }) {
  const router = useRouter();
  const { user } = useAuth();
  const { toast } = useToast();
  const { data: classData, isLoading, error } = useClass(params.id);
  const deleteClassMutation = useDeleteClass();

  const handleEdit = () => {
    if (canEditClass(user?.role)) {
      router.push(`/dashboard/classes/${params.id}/edit`);
    }
  };

  const handleDelete = async () => {
    if (!canDeleteClass(user?.role)) {
      toast({
        title: 'Access Denied',
        description: 'Only administrators can delete classes',
        variant: 'destructive',
      });
      return;
    }

    if (confirm('Are you sure you want to delete this class?')) {
      try {
        await deleteClassMutation.mutateAsync(params.id);
        toast({
          title: 'Success',
          description: 'Class deleted successfully',
        });
        router.push('/dashboard/classes');
      } catch (error) {
        toast({
          title: 'Error',
          description: error instanceof Error ? error.message : 'Failed to delete class',
          variant: 'destructive',
        });
      }
    }
  };

  if (!canViewClass(user?.role)) {
    router.push('/dashboard/classes');
    return null;
  }

  if (isLoading) {
    return (
      <div className='container mx-auto p-4 sm:p-6 lg:p-8'>
        <div className='animate-pulse space-y-6'>
          <div className='h-8 bg-gray-200 rounded w-1/3'></div>
          <div className='h-64 bg-gray-200 rounded'></div>
          <div className='h-32 bg-gray-200 rounded'></div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className='container mx-auto p-4 sm:p-6 lg:p-8'>
        <div className='text-center py-12'>
          <p className='text-red-600 mb-4'>Failed to load class data</p>
          <Link href='/dashboard/classes'>
            <Button variant='outline'>Back to Classes</Button>
          </Link>
        </div>
      </div>
    );
  }

  if (!classData) {
    return (
      <div className='container mx-auto p-4 sm:p-6 lg:p-8'>
        <div className='text-center py-12'>
          <p className='text-gray-600 mb-4'>Class not found</p>
          <Link href='/dashboard/classes'>
            <Button variant='outline'>Back to Classes</Button>
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className='container mx-auto p-4 sm:p-6 lg:p-8'>
      {/* Header */}
      <div className='flex items-center justify-between mb-8'>
        <div className='flex items-center gap-4'>
          <Link href='/dashboard/classes'>
            <Button variant='outline' size='sm'>
              <ArrowLeft className='w-4 h-4 mr-2' />
              Back to Classes
            </Button>
          </Link>
          <div>
            <h1 className='text-3xl font-bold text-gray-900 flex items-center gap-2'>
              <BookOpen className='w-8 h-8 text-blue-600' />
              {classData.name}
            </h1>
            <p className='text-gray-600 mt-1'>{classData.code} • {classData.subject}</p>
          </div>
        </div>
        <div className='flex gap-2'>
          {canEditClass(user?.role) && (
            <Button onClick={handleEdit} variant='outline'>
              <Edit className='w-4 h-4 mr-2' />
              Edit
            </Button>
          )}
          {canDeleteClass(user?.role) && (
            <Button 
              onClick={handleDelete} 
              variant='destructive'
              disabled={deleteClassMutation.isPending}
            >
              <Trash2 className='w-4 h-4 mr-2' />
              {deleteClassMutation.isPending ? 'Deleting...' : 'Delete'}
            </Button>
          )}
        </div>
      </div>

      {/* Class Overview */}
      <div className='grid grid-cols-1 lg:grid-cols-3 gap-8 mb-8'>
        <div className='lg:col-span-2'>
          <Card>
            <CardHeader>
              <CardTitle>Class Information</CardTitle>
            </CardHeader>
            <CardContent className='space-y-4'>
              <div className='grid grid-cols-2 gap-4'>
                <div>
                  <p className='text-sm font-medium text-gray-500'>Grade Level</p>
                  <p className='text-lg'>Grade {classData.grade}</p>
                </div>
                <div>
                  <p className='text-sm font-medium text-gray-500'>Status</p>
                  <Badge variant={classData.status === 'ACTIVE' ? 'default' : 'secondary'}>
                    {classData.status}
                  </Badge>
                </div>
              </div>
              <Separator />
              <div className='grid grid-cols-2 gap-4'>
                <div className='flex items-center gap-2'>
                  <MapPin className='w-4 h-4 text-gray-500' />
                  <span>{classData.room}</span>
                </div>
                <div className='flex items-center gap-2'>
                  <Clock className='w-4 h-4 text-gray-500' />
                  <span>{classData.schedule}</span>
                </div>
              </div>
              <div className='flex items-center gap-2'>
                <Users className='w-4 h-4 text-gray-500' />
                <span>{classData.enrolled}/{classData.capacity} students enrolled</span>
              </div>
              {classData.description && (
                <>
                  <Separator />
                  <div>
                    <p className='text-sm font-medium text-gray-500 mb-2'>Description</p>
                    <p className='text-gray-700'>{classData.description}</p>
                  </div>
                </>
              )}
            </CardContent>
          </Card>
        </div>

        <div>
          <Card>
            <CardHeader>
              <CardTitle>Teacher Information</CardTitle>
            </CardHeader>
            <CardContent className='space-y-4'>
              <div className='flex items-center gap-3'>
                <div className='w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center'>
                  <User className='w-6 h-6 text-blue-600' />
                </div>
                <div>
                  <p className='font-medium'>{classData.teacher_name || 'Not assigned'}</p>
                  <p className='text-sm text-gray-500'>Class Teacher</p>
                </div>
              </div>
              <Separator />
              <div className='space-y-2'>
                <p className='text-sm'>
                  <span className='font-medium'>Academic Year:</span> {classData.academic_year}
                </p>
                <p className='text-sm'>
                  <span className='font-medium'>Section:</span> {classData.section}
                </p>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Detailed Tabs */}
      <Tabs defaultValue='students' className='space-y-6'>
        <TabsList>
          <TabsTrigger value='students'>Students ({classData.enrolled || 0})</TabsTrigger>
          <TabsTrigger value='schedule'>Schedule</TabsTrigger>
        </TabsList>

        <TabsContent value='students'>
          <Card>
            <CardHeader>
              <CardTitle>Enrolled Students</CardTitle>
            </CardHeader>
            <CardContent>
              <div className='text-center py-8'>
                <GraduationCap className='w-12 h-12 text-gray-400 mx-auto mb-4' />
                <p className='text-gray-500'>Student management coming soon</p>
                <p className='text-sm text-gray-400 mt-2'>
                  {classData.enrolled || 0} of {classData.capacity} students enrolled
                </p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value='schedule'>
          <Card>
            <CardHeader>
              <CardTitle>Class Schedule</CardTitle>
            </CardHeader>
            <CardContent>
              <div className='space-y-4'>
                <div className='flex items-center gap-2'>
                  <Calendar className='w-5 h-5 text-blue-600' />
                  <span className='font-medium'>Regular Schedule</span>
                </div>
                <p className='text-gray-700'>{classData.schedule}</p>
                <Separator />
                <div className='grid grid-cols-2 gap-4'>
                  <div>
                    <p className='text-sm font-medium text-gray-500'>Academic Year</p>
                    <p>{classData.academic_year}</p>
                  </div>
                  <div>
                    <p className='text-sm font-medium text-gray-500'>Room</p>
                    <p>{classData.room || 'Not assigned'}</p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
