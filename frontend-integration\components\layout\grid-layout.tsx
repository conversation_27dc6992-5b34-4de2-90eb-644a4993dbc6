/**
 * Grid Layout Component
 * 
 * Responsive grid layout with consistent spacing and breakpoints
 */

import { ReactNode } from 'react';
import { cn } from '@/lib/utils';

export interface GridLayoutProps {
  children: ReactNode;
  columns?: 1 | 2 | 3 | 4 | 5 | 6;
  gap?: 'sm' | 'md' | 'lg' | 'xl';
  className?: string;
}

const columnVariants = {
  1: 'grid-cols-1',
  2: 'grid-cols-1 md:grid-cols-2',
  3: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3',
  4: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4',
  5: 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-5',
  6: 'grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-6',
};

const gapVariants = {
  sm: 'gap-4',
  md: 'gap-6',
  lg: 'gap-8',
  xl: 'gap-12',
};

export function GridLayout({
  children,
  columns = 3,
  gap = 'md',
  className,
}: GridLayoutProps) {
  return (
    <div className={cn(
      'grid',
      columnVariants[columns],
      gapVariants[gap],
      className
    )}>
      {children}
    </div>
  );
}
