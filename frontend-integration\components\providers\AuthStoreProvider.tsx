"use client";

import { useEffect } from 'react';
import { useAuthStore } from '@/stores/authStore';

export function AuthStoreProvider({ children }: { children: React.ReactNode }) {
  useEffect(() => {
    // Attach Zustand store to globalThis for API client access
    (globalThis as any).__zustandStore__ = { auth: useAuthStore };
    
    console.log('🔗 Auth store attached to globalThis for API client');
  }, []);

  return <>{children}</>;
}
