/**
 * Permission utilities for role-based access control
 */

import type { Role } from '@/stores/authStore';

// Teacher permissions
export const canCreateTeacher = (role?: Role) => role === 'SUPER_ADMIN' || role === 'ADMIN';
export const canEditTeacher = (role?: Role) => role === 'SUPER_ADMIN' || role === 'ADMIN';
export const canDeleteTeacher = (role?: Role) => role === 'SUPER_ADMIN' || role === 'ADMIN';
export const canViewTeacher = (role?: Role) => !!role; // Any authenticated user

// Student permissions
export const canCreateStudent = (role?: Role) => role === 'SUPER_ADMIN' || role === 'ADMIN';
export const canEditStudent = (role?: Role) => role === 'SUPER_ADMIN' || role === 'ADMIN';
export const canDeleteStudent = (role?: Role) => role === 'SUPER_ADMIN' || role === 'ADMIN';
export const canViewStudent = (role?: Role) => !!role; // Any authenticated user

// Class permissions - SUPER_ADMIN only for CRUD operations
export const canCreateClass = (role?: Role) => role === 'SUPER_ADMIN';
export const canEditClass = (role?: Role) => role === 'SUPER_ADMIN';
export const canDeleteClass = (role?: Role) => role === 'SUPER_ADMIN';
export const canViewClass = (role?: Role) => !!role; // Any authenticated user
export const canManageClassStudents = (role?: Role) =>
  role === 'SUPER_ADMIN' || role === 'ADMIN' || role === 'TEACHER';
export const canBulkOperateClasses = (role?: Role) => role === 'SUPER_ADMIN';

// General admin permissions
export const isAdmin = (role?: Role) => role === 'SUPER_ADMIN' || role === 'ADMIN';
export const isSuperAdmin = (role?: Role) => role === 'SUPER_ADMIN';

// SUPER_ADMIN exclusive permissions
export const canManageUsers = (role?: Role) => role === 'SUPER_ADMIN';
export const canChangeUserRoles = (role?: Role) => role === 'SUPER_ADMIN';
export const canAccessSystemSettings = (role?: Role) => role === 'SUPER_ADMIN';
export const canViewAuditLogs = (role?: Role) => role === 'SUPER_ADMIN';
export const canBackupSystem = (role?: Role) => role === 'SUPER_ADMIN';
export const canManageAcademicYears = (role?: Role) => role === 'SUPER_ADMIN';
export const canManageSubjects = (role?: Role) => role === 'SUPER_ADMIN';
export const canManageGrades = (role?: Role) => role === 'SUPER_ADMIN';

// Dashboard access
export const canAccessDashboard = (role?: Role) => !!role; // Any authenticated user

// SUPER_ADMIN permission constants
export const SUPER_ADMIN_PERMISSIONS = {
  classes: ['CREATE', 'READ', 'UPDATE', 'DELETE', 'BULK_OPERATIONS'],
  users: ['CREATE', 'READ', 'UPDATE', 'DELETE', 'CHANGE_ROLE'],
  system: ['CONFIGURE', 'BACKUP', 'AUDIT_LOGS'],
  academic: ['MANAGE_YEARS', 'MANAGE_SUBJECTS', 'MANAGE_GRADES'],
} as const;
