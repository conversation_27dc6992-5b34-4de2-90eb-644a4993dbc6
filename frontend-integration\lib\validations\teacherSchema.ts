/**
 * Teacher Schema - Zod Validation with React Hook Form
 * 
 * Features:
 * - Comprehensive validation rules
 * - Type-safe form handling
 * - Custom validation messages
 * - Conditional validation
 * - File upload validation
 * - Async validation support
 * - Form state management
 */

import { z } from 'zod';
import { useForm, UseFormReturn } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';

// Base validation schemas
const emailSchema = z
  .string()
  .min(1, 'Email is required')
  .email('Please enter a valid email address')
  .max(255, 'Email must be less than 255 characters');

const phoneSchema = z
  .string()
  .min(1, 'Phone number is required')
  .regex(
    /^[\+]?[1-9][\d]{0,15}$/,
    'Please enter a valid phone number (e.g., +1234567890)'
  );

const nameSchema = z
  .string()
  .min(1, 'This field is required')
  .min(2, 'Must be at least 2 characters')
  .max(50, 'Must be less than 50 characters')
  .regex(
    /^[a-zA-Z\s\-'\.]+$/,
    'Only letters, spaces, hyphens, apostrophes, and periods are allowed'
  );

const salarySchema = z
  .number({
    required_error: 'Salary is required',
    invalid_type_error: 'Salary must be a number',
  })
  .min(0, 'Salary cannot be negative')
  .max(1000000, 'Salary cannot exceed $1,000,000');

// File validation schema
const fileSchema = z
  .instanceof(File, { message: 'Please select a file' })
  .refine((file) => file.size <= 5 * 1024 * 1024, 'File size must be less than 5MB')
  .refine(
    (file) => ['image/jpeg', 'image/png', 'image/webp'].includes(file.type),
    'Only JPEG, PNG, and WebP images are allowed'
  );

// Teacher creation schema
export const createTeacherSchema = z.object({
  // Personal Information
  first_name: nameSchema,
  last_name: nameSchema,
  email: emailSchema,
  phone: phoneSchema,
  date_of_birth: z
    .string()
    .min(1, 'Date of birth is required')
    .refine((date) => {
      const birthDate = new Date(date);
      const today = new Date();
      const age = today.getFullYear() - birthDate.getFullYear();
      return age >= 18 && age <= 80;
    }, 'Teacher must be between 18 and 80 years old'),
  
  gender: z.enum(['MALE', 'FEMALE', 'OTHER'], {
    required_error: 'Please select a gender',
  }),
  
  address: z.object({
    street: z.string().min(1, 'Street address is required').max(255),
    city: z.string().min(1, 'City is required').max(100),
    state: z.string().min(1, 'State is required').max(100),
    postal_code: z
      .string()
      .min(1, 'Postal code is required')
      .regex(/^\d{5}(-\d{4})?$/, 'Please enter a valid postal code'),
    country: z.string().min(1, 'Country is required').max(100),
  }),
  
  // Professional Information
  teacher_id: z
    .string()
    .min(1, 'Teacher ID is required')
    .regex(/^TCH\d{4,}$/, 'Teacher ID must start with "TCH" followed by at least 4 digits'),
  
  department: z.enum([
    'MATHEMATICS',
    'SCIENCE',
    'ENGLISH',
    'HISTORY',
    'PHYSICAL_EDUCATION',
    'ART',
    'MUSIC',
    'COMPUTER_SCIENCE',
    'FOREIGN_LANGUAGE',
    'OTHER'
  ], {
    required_error: 'Please select a department',
  }),
  
  subject_specialization: z
    .array(z.string().min(1, 'Subject cannot be empty'))
    .min(1, 'At least one subject specialization is required')
    .max(5, 'Maximum 5 subject specializations allowed'),
  
  qualification: z.enum([
    'HIGH_SCHOOL',
    'ASSOCIATE_DEGREE',
    'BACHELOR_DEGREE',
    'MASTER_DEGREE',
    'DOCTORATE',
    'PROFESSIONAL_CERTIFICATE'
  ], {
    required_error: 'Please select a qualification',
  }),
  
  experience_years: z
    .number({
      required_error: 'Experience is required',
      invalid_type_error: 'Experience must be a number',
    })
    .min(0, 'Experience cannot be negative')
    .max(50, 'Experience cannot exceed 50 years'),
  
  hire_date: z
    .string()
    .min(1, 'Hire date is required')
    .refine((date) => {
      const hireDate = new Date(date);
      const today = new Date();
      return hireDate <= today;
    }, 'Hire date cannot be in the future'),
  
  salary: salarySchema,
  
  status: z.enum(['ACTIVE', 'INACTIVE', 'ON_LEAVE'], {
    required_error: 'Please select a status',
  }),
  
  // Optional fields
  profile_picture: fileSchema.optional(),
  
  emergency_contact: z.object({
    name: nameSchema,
    relationship: z.string().min(1, 'Relationship is required').max(50),
    phone: phoneSchema,
    email: emailSchema.optional(),
  }).optional(),
  
  certifications: z
    .array(z.object({
      name: z.string().min(1, 'Certification name is required').max(100),
      issuer: z.string().min(1, 'Issuer is required').max(100),
      issue_date: z.string().min(1, 'Issue date is required'),
      expiry_date: z.string().optional(),
      certificate_id: z.string().optional(),
    }))
    .optional(),
  
  notes: z.string().max(1000, 'Notes must be less than 1000 characters').optional(),
});

// Teacher update schema (all fields optional except ID)
export const updateTeacherSchema = createTeacherSchema
  .partial()
  .extend({
    id: z.string().min(1, 'Teacher ID is required'),
  });

// Teacher search/filter schema
export const teacherFilterSchema = z.object({
  search: z.string().optional(),
  department: z.string().optional(),
  status: z.string().optional(),
  experience_min: z.number().min(0).optional(),
  experience_max: z.number().min(0).optional(),
  hire_date_from: z.string().optional(),
  hire_date_to: z.string().optional(),
  salary_min: z.number().min(0).optional(),
  salary_max: z.number().min(0).optional(),
  qualification: z.string().optional(),
  page: z.number().min(1).default(1),
  limit: z.number().min(1).max(100).default(10),
  sort_by: z.enum(['name', 'hire_date', 'salary', 'experience']).default('name'),
  sort_order: z.enum(['asc', 'desc']).default('asc'),
});

// Type inference
export type CreateTeacherInput = z.infer<typeof createTeacherSchema>;
export type UpdateTeacherInput = z.infer<typeof updateTeacherSchema>;
export type TeacherFilterInput = z.infer<typeof teacherFilterSchema>;

// Custom hook for teacher form
export function useTeacherForm(
  defaultValues?: Partial<CreateTeacherInput>,
  mode: 'create' | 'update' = 'create'
): UseFormReturn<CreateTeacherInput | UpdateTeacherInput> {
  const schema = mode === 'create' ? createTeacherSchema : updateTeacherSchema;
  
  return useForm({
    resolver: zodResolver(schema),
    defaultValues: {
      // Default values for create mode
      status: 'ACTIVE',
      department: 'MATHEMATICS',
      qualification: 'BACHELOR_DEGREE',
      experience_years: 0,
      subject_specialization: [],
      gender: 'MALE',
      address: {
        street: '',
        city: '',
        state: '',
        postal_code: '',
        country: 'United States',
      },
      ...defaultValues,
    } as any,
    mode: 'onChange',
    criteriaMode: 'all',
  });
}

// Custom validation functions
export const teacherValidations = {
  // Async email validation (check if email exists)
  validateEmailUnique: async (email: string, currentId?: string): Promise<boolean> => {
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 500));
      
      // Mock validation - in real app, call API
      const existingEmails = ['<EMAIL>', '<EMAIL>'];
      return !existingEmails.includes(email.toLowerCase());
    } catch {
      return true; // Allow on error
    }
  },
  
  // Validate teacher ID uniqueness
  validateTeacherIdUnique: async (teacherId: string, currentId?: string): Promise<boolean> => {
    try {
      await new Promise(resolve => setTimeout(resolve, 300));
      
      // Mock validation
      const existingIds = ['TCH0001', 'TCH0002', 'TCH0003'];
      return !existingIds.includes(teacherId);
    } catch {
      return true;
    }
  },
  
  // Validate file upload
  validateProfilePicture: (file: File): string | null => {
    if (file.size > 5 * 1024 * 1024) {
      return 'File size must be less than 5MB';
    }
    
    if (!['image/jpeg', 'image/png', 'image/webp'].includes(file.type)) {
      return 'Only JPEG, PNG, and WebP images are allowed';
    }
    
    return null;
  },
  
  // Validate salary range based on experience
  validateSalaryRange: (salary: number, experience: number): string | null => {
    const minSalary = 30000 + (experience * 2000);
    const maxSalary = 100000 + (experience * 5000);
    
    if (salary < minSalary) {
      return `Salary should be at least $${minSalary.toLocaleString()} for ${experience} years of experience`;
    }
    
    if (salary > maxSalary) {
      return `Salary seems high for ${experience} years of experience (max suggested: $${maxSalary.toLocaleString()})`;
    }
    
    return null;
  },
};

// Form field configurations
export const teacherFormFields = {
  personalInfo: [
    'first_name',
    'last_name',
    'email',
    'phone',
    'date_of_birth',
    'gender',
    'address',
  ],
  professionalInfo: [
    'teacher_id',
    'department',
    'subject_specialization',
    'qualification',
    'experience_years',
    'hire_date',
    'salary',
    'status',
  ],
  additionalInfo: [
    'profile_picture',
    'emergency_contact',
    'certifications',
    'notes',
  ],
};

// Error message helpers
export const getFieldError = (
  errors: any,
  fieldName: string
): string | undefined => {
  const fieldPath = fieldName.split('.');
  let error = errors;
  
  for (const path of fieldPath) {
    error = error?.[path];
  }
  
  return error?.message;
};

// Form validation helpers
export const validateFormSection = (
  data: any,
  section: keyof typeof teacherFormFields
): boolean => {
  const fields = teacherFormFields[section];
  return fields.every(field => {
    const value = field.split('.').reduce((obj, key) => obj?.[key], data);
    return value !== undefined && value !== null && value !== '';
  });
};

export default createTeacherSchema;
