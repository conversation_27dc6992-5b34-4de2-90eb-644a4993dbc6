'use client';

import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import {
  ArrowLeft,
  Award,
  Check,
  FileText,
  Plus,
  TrendingUp,
  User,
} from 'lucide-react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { useState } from 'react';

interface GradeFormData {
  studentId: string;
  studentName: string;
  class: string;
  subject: string;
  examType: string;
  examDate: string;
  totalMarks: string;
  obtainedMarks: string;
  grade: string;
  percentage: string;
  remarks: string;
  teacherId: string;
}

const initialFormData: GradeFormData = {
  studentId: '',
  studentName: '',
  class: '',
  subject: '',
  examType: '',
  examDate: '',
  totalMarks: '',
  obtainedMarks: '',
  grade: '',
  percentage: '',
  remarks: '',
  teacherId: '',
};

const students = [
  { id: 'STU001', name: 'John Doe', class: 'Grade 10' },
  { id: 'STU002', name: 'Jane Smith', class: 'Grade 9' },
  { id: 'STU003', name: 'Mike Johnson', class: 'Grade 11' },
];

const subjects = ['Mathematics', 'Science', 'English', 'History', 'Physics', 'Chemistry'];
const examTypes = ['Mid-term', 'Final', 'Quiz', 'Assignment', 'Project', 'Practical'];
const teachers = [
  { id: 'T001', name: 'Dr. John Smith' },
  { id: 'T002', name: 'Prof. Sarah Johnson' },
  { id: 'T003', name: 'Dr. Emily Davis' },
];

export default function CreateGradePage() {
  const router = useRouter();
  const [formData, setFormData] = useState<GradeFormData>(initialFormData);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleInputChange = (field: keyof GradeFormData, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleStudentSelect = (studentId: string) => {
    const student = students.find(s => s.id === studentId);
    if (student) {
      setFormData(prev => ({
        ...prev,
        studentId: student.id,
        studentName: student.name,
        class: student.class,
      }));
    }
  };

  const calculateGrade = (obtained: number, total: number) => {
    const percentage = (obtained / total) * 100;
    if (percentage >= 90) return 'A+';
    if (percentage >= 80) return 'A';
    if (percentage >= 70) return 'B+';
    if (percentage >= 60) return 'B';
    if (percentage >= 50) return 'C+';
    if (percentage >= 40) return 'C';
    if (percentage >= 30) return 'D';
    return 'F';
  };

  const handleMarksChange = (field: 'totalMarks' | 'obtainedMarks', value: string) => {
    const newFormData = { ...formData, [field]: value };
    
    if (newFormData.totalMarks && newFormData.obtainedMarks) {
      const total = parseFloat(newFormData.totalMarks);
      const obtained = parseFloat(newFormData.obtainedMarks);
      
      if (!isNaN(total) && !isNaN(obtained) && total > 0) {
        const percentage = ((obtained / total) * 100).toFixed(2);
        const grade = calculateGrade(obtained, total);
        
        newFormData.percentage = percentage;
        newFormData.grade = grade;
      }
    }
    
    setFormData(newFormData);
  };

  const handleSubmit = async () => {
    setIsSubmitting(true);
    
    try {
      // TODO: Replace with actual API call
      console.log('Submitting grade data:', formData);
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // Success - redirect to grades list
      router.push('/dashboard/grade');
    } catch (error) {
      console.error('Error creating grade record:', error);
      // TODO: Show error toast
    } finally {
      setIsSubmitting(false);
    }
  };

  const getGradeColor = (grade: string) => {
    switch (grade) {
      case 'A+':
      case 'A': return 'text-green-600 bg-green-50';
      case 'B+':
      case 'B': return 'text-blue-600 bg-blue-50';
      case 'C+':
      case 'C': return 'text-yellow-600 bg-yellow-50';
      case 'D': return 'text-orange-600 bg-orange-50';
      case 'F': return 'text-red-600 bg-red-50';
      default: return 'text-gray-600 bg-gray-50';
    }
  };

  return (
    <div className="container mx-auto p-4 sm:p-6 lg:p-8 max-w-4xl">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-4">
          <Link href="/dashboard/grade">
            <Button variant="ghost" size="sm">
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to Grades
            </Button>
          </Link>
          <div>
            <h1 className="text-2xl sm:text-3xl font-bold tracking-tight flex items-center">
              <Plus className="w-8 h-8 mr-3 text-purple-600" />
              Add Grade Record
            </h1>
            <p className="text-muted-foreground mt-1">
              Create a new student grade record
            </p>
          </div>
        </div>
      </div>

      {/* Student Information Card */}
      <Card className="mb-6">
        <CardHeader>
          <CardTitle className="flex items-center">
            <User className="w-5 h-5 mr-2 text-blue-600" />
            Student Information
          </CardTitle>
          <CardDescription>
            Select the student for grade entry
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <Label htmlFor="studentId">Select Student *</Label>
              <Select value={formData.studentId} onValueChange={handleStudentSelect}>
                <SelectTrigger>
                  <SelectValue placeholder="Select student" />
                </SelectTrigger>
                <SelectContent>
                  {students.map((student) => (
                    <SelectItem key={student.id} value={student.id}>
                      {student.name} ({student.id})
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div>
              <Label htmlFor="class">Class</Label>
              <Input
                id="class"
                value={formData.class}
                onChange={(e) => handleInputChange('class', e.target.value)}
                placeholder="Grade/Class"
                disabled
              />
            </div>
            <div>
              <Label htmlFor="teacherId">Teacher</Label>
              <Select value={formData.teacherId} onValueChange={(value) => handleInputChange('teacherId', value)}>
                <SelectTrigger>
                  <SelectValue placeholder="Select teacher" />
                </SelectTrigger>
                <SelectContent>
                  {teachers.map((teacher) => (
                    <SelectItem key={teacher.id} value={teacher.id}>
                      {teacher.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Exam Information Card */}
      <Card className="mb-6">
        <CardHeader>
          <CardTitle className="flex items-center">
            <FileText className="w-5 h-5 mr-2 text-green-600" />
            Exam Information
          </CardTitle>
          <CardDescription>
            Enter exam details and subject information
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div>
              <Label htmlFor="subject">Subject *</Label>
              <Select value={formData.subject} onValueChange={(value) => handleInputChange('subject', value)}>
                <SelectTrigger>
                  <SelectValue placeholder="Select subject" />
                </SelectTrigger>
                <SelectContent>
                  {subjects.map((subject) => (
                    <SelectItem key={subject} value={subject}>
                      {subject}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div>
              <Label htmlFor="examType">Exam Type *</Label>
              <Select value={formData.examType} onValueChange={(value) => handleInputChange('examType', value)}>
                <SelectTrigger>
                  <SelectValue placeholder="Select exam type" />
                </SelectTrigger>
                <SelectContent>
                  {examTypes.map((type) => (
                    <SelectItem key={type} value={type}>
                      {type}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div>
              <Label htmlFor="examDate">Exam Date</Label>
              <Input
                id="examDate"
                type="date"
                value={formData.examDate}
                onChange={(e) => handleInputChange('examDate', e.target.value)}
              />
            </div>
            <div>
              <Label htmlFor="totalMarks">Total Marks *</Label>
              <Input
                id="totalMarks"
                type="number"
                value={formData.totalMarks}
                onChange={(e) => handleMarksChange('totalMarks', e.target.value)}
                placeholder="100"
                required
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Grade Information Card */}
      <Card className="mb-6">
        <CardHeader>
          <CardTitle className="flex items-center">
            <Award className="w-5 h-5 mr-2 text-purple-600" />
            Grade Information
          </CardTitle>
          <CardDescription>
            Enter marks and view calculated grade
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div>
              <Label htmlFor="obtainedMarks">Obtained Marks *</Label>
              <Input
                id="obtainedMarks"
                type="number"
                value={formData.obtainedMarks}
                onChange={(e) => handleMarksChange('obtainedMarks', e.target.value)}
                placeholder="85"
                required
              />
            </div>
            <div>
              <Label htmlFor="percentage">Percentage</Label>
              <Input
                id="percentage"
                value={formData.percentage ? `${formData.percentage}%` : ''}
                disabled
                placeholder="Calculated automatically"
              />
            </div>
            <div>
              <Label htmlFor="grade">Grade</Label>
              <div className="flex items-center h-10">
                {formData.grade ? (
                  <Badge variant="outline" className={getGradeColor(formData.grade)}>
                    {formData.grade}
                  </Badge>
                ) : (
                  <span className="text-muted-foreground text-sm">Calculated automatically</span>
                )}
              </div>
            </div>
            <div className="flex items-center justify-center">
              {formData.percentage && (
                <div className="text-center">
                  <TrendingUp className="w-8 h-8 mx-auto mb-2 text-green-600" />
                  <p className="text-sm text-muted-foreground">Performance</p>
                </div>
              )}
            </div>
          </div>
          <div className="mt-4">
            <Label htmlFor="remarks">Remarks</Label>
            <Textarea
              id="remarks"
              value={formData.remarks}
              onChange={(e) => handleInputChange('remarks', e.target.value)}
              placeholder="Teacher's remarks or comments..."
              rows={3}
            />
          </div>
        </CardContent>
      </Card>

      {/* Submit Button */}
      <div className="flex justify-end">
        <Button
          onClick={handleSubmit}
          disabled={isSubmitting || !formData.studentId || !formData.subject || !formData.totalMarks || !formData.obtainedMarks}
          className="bg-purple-600 hover:bg-purple-700"
        >
          {isSubmitting ? (
            <>
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
              Creating Grade Record...
            </>
          ) : (
            <>
              <Check className="w-4 h-4 mr-2" />
              Create Grade Record
            </>
          )}
        </Button>
      </div>
    </div>
  );
}
