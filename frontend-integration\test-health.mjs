/**
 * Simple Health Check Test for FastAPI Backend
 * 
 * Tests direct connectivity to the FastAPI backend
 * Run with: node test-health.mjs
 */

const API_URL = process.env.NEXT_PUBLIC_API_URL || 'http://127.0.0.1:8000';

async function testHealth() {
  console.log('🏥 Testing backend health...');
  console.log('API URL:', API_URL);
  
  try {
    // Test basic health endpoint
    console.log('\n1. Testing /api/v1/health endpoint...');
    const healthResponse = await fetch(`${API_URL}/api/v1/health`);
    
    if (healthResponse.ok) {
      const healthData = await healthResponse.json();
      console.log('✅ Health check successful:', healthData);
    } else {
      console.log(`⚠️ Health endpoint returned ${healthResponse.status}: ${healthResponse.statusText}`);
    }
    
    // Test teachers endpoint (should return 401 without auth or empty array)
    console.log('\n2. Testing /api/v1/teachers/ endpoint...');
    const teachersResponse = await fetch(`${API_URL}/api/v1/teachers/`);
    
    if (teachersResponse.ok) {
      const teachersData = await teachersResponse.json();
      console.log('✅ Teachers endpoint accessible:', Array.isArray(teachersData) ? `${teachersData.length} teachers` : 'Response received');
    } else if (teachersResponse.status === 401) {
      console.log('✅ Teachers endpoint requires auth (401) - this is expected');
    } else if (teachersResponse.status === 404) {
      console.log('✅ Teachers endpoint returns 404 - will be handled as empty list');
    } else {
      console.log(`⚠️ Teachers endpoint returned ${teachersResponse.status}: ${teachersResponse.statusText}`);
    }
    
    // Test auth endpoint
    console.log('\n3. Testing /api/v1/auth/login endpoint...');
    const authResponse = await fetch(`${API_URL}/api/v1/auth/login`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ username: 'test', password: 'test' })
    });
    
    if (authResponse.status === 401 || authResponse.status === 422) {
      console.log('✅ Auth endpoint accessible (returns expected error for invalid credentials)');
    } else if (authResponse.ok) {
      console.log('✅ Auth endpoint accessible (login successful)');
    } else {
      console.log(`⚠️ Auth endpoint returned ${authResponse.status}: ${authResponse.statusText}`);
    }
    
    console.log('\n🎉 Backend connectivity test completed!');
    console.log('\n📋 Next steps:');
    console.log('1. Ensure your FastAPI backend is running on', API_URL);
    console.log('2. Make sure CORS is configured to allow http://localhost:3000');
    console.log('3. Start your Next.js frontend with: npm run dev');
    
  } catch (error) {
    console.error('\n❌ Backend connectivity test failed:');
    console.error('Error:', error.message);
    console.log('\n🔧 Troubleshooting:');
    console.log('1. Is your FastAPI backend running?');
    console.log('2. Check the API URL:', API_URL);
    console.log('3. Verify CORS configuration in your FastAPI app');
    console.log('4. Check for firewall/network issues');
    process.exit(1);
  }
}

// Run the test
testHealth();
