'use client';

import {
  useDebouncedValue,
  useRenderCount,
  useStable<PERSON><PERSON>back,
  useWhyDidYouUpdate,
} from '@/hooks/usePerformance';
import React, { memo, useMemo } from 'react';
import { Control, FieldPath, FieldValues, useController } from 'react-hook-form';

// UI Components
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';

// Types
interface BaseFieldProps<T extends FieldValues> {
  name: FieldPath<T>;
  control: Control<T>;
  label: string;
  placeholder?: string;
  disabled?: boolean;
  required?: boolean;
  className?: string;
}

interface TextFieldProps<T extends FieldValues> extends BaseFieldProps<T> {
  type?: 'text' | 'email' | 'password' | 'tel' | 'url' | 'number';
  debounceMs?: number;
}

interface SelectFieldProps<T extends FieldValues> extends BaseFieldProps<T> {
  options: Array<{ value: string; label: string }>;
}

interface TextAreaFieldProps<T extends FieldValues> extends BaseFieldProps<T> {
  rows?: number;
  maxLength?: number;
}

// Memoized error message component
const ErrorMessage = memo(({ error }: { error: string | undefined }) => {
  if (!error) return null;

  return <p className='text-sm font-medium text-destructive mt-1'>{error}</p>;
});

ErrorMessage.displayName = 'ErrorMessage';

// Memoized label component
const FieldLabel = memo(
  ({ label, required, htmlFor }: { label: string; required?: boolean; htmlFor?: string }) => (
    <Label htmlFor={htmlFor} className='text-sm font-medium text-gray-700'>
      {label}
      {required && <span className='text-red-500 ml-1'>*</span>}
    </Label>
  )
);

FieldLabel.displayName = 'FieldLabel';

/**
 * Optimized Text Field Component
 *
 * Performance optimizations:
 * - Memoized to prevent unnecessary re-renders
 * - Debounced input to reduce validation frequency
 * - Stable callbacks to prevent prop drilling
 * - Separated error and label components
 */
const OptimizedTextField = memo(
  <T extends FieldValues>({
    name,
    control,
    label,
    type = 'text',
    placeholder,
    disabled = false,
    required = false,
    debounceMs = 300,
    className = '',
  }: TextFieldProps<T>) => {
    const renderCount = useRenderCount(`OptimizedTextField-${name}`);

    // Track why component re-renders in development
    if (process.env.NODE_ENV === 'development') {
      useWhyDidYouUpdate(`OptimizedTextField-${name}`, {
        name,
        label,
        type,
        placeholder,
        disabled,
        required,
        debounceMs,
        className,
      });
    }

    const {
      field: { onChange, onBlur, value, ref },
      fieldState: { error },
    } = useController({
      name,
      control,
      defaultValue: '' as any,
    });

    // Debounce the onChange to reduce validation frequency
    const debouncedValue = useDebouncedValue(value, debounceMs);

    // Stable callback to prevent unnecessary re-renders
    const handleChange = useStableCallback(
      (e: React.ChangeEvent<HTMLInputElement>) => {
        const newValue = type === 'number' ? parseFloat(e.target.value) || 0 : e.target.value;
        onChange(newValue);
      },
      [onChange, type]
    );

    const handleBlur = useStableCallback(
      (e: React.FocusEvent<HTMLInputElement>) => {
        onBlur();
      },
      [onBlur]
    );

    // Memoized input props to prevent object recreation
    const inputProps = useMemo(
      () => ({
        id: name,
        type,
        placeholder,
        disabled,
        className: `${className} ${error ? 'border-red-500' : ''}`,
        value: value || '',
        onChange: handleChange,
        onBlur: handleBlur,
        ref,
      }),
      [name, type, placeholder, disabled, className, error, value, handleChange, handleBlur, ref]
    );

    return (
      <div className='space-y-2'>
        <FieldLabel label={label} required={required} htmlFor={name} />
        <Input {...inputProps} />
        <ErrorMessage error={error?.message || ''} />

        {/* Development info */}
        {process.env.NODE_ENV === 'development' && (
          <div className='text-xs text-gray-400'>
            Renders: {renderCount} | Debounced: {debouncedValue !== value ? 'Yes' : 'No'}
          </div>
        )}
      </div>
    );
  }
);

OptimizedTextField.displayName = 'OptimizedTextField';

/**
 * Optimized Select Field Component
 */
const OptimizedSelectField = memo(
  <T extends FieldValues>({
    name,
    control,
    label,
    options,
    placeholder,
    disabled = false,
    required = false,
    className = '',
  }: SelectFieldProps<T>) => {
    const renderCount = useRenderCount(`OptimizedSelectField-${name}`);

    const {
      field: { onChange, value },
      fieldState: { error },
    } = useController({
      name,
      control,
      defaultValue: '' as any,
    });

    // Stable callback for select change
    const handleChange = useStableCallback(
      (newValue: string) => {
        onChange(newValue);
      },
      [onChange]
    );

    // Memoized options to prevent recreation
    const memoizedOptions = useMemo(() => options, [options]);

    return (
      <div className='space-y-2'>
        <FieldLabel label={label} required={required} />
        <Select onValueChange={handleChange} defaultValue={value} disabled={disabled}>
          <SelectTrigger className={`${className} ${error ? 'border-red-500' : ''}`}>
            <SelectValue placeholder={placeholder} />
          </SelectTrigger>
          <SelectContent>
            {memoizedOptions.map(option => (
              <SelectItem key={option.value} value={option.value}>
                {option.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
        <ErrorMessage error={error?.message || ''} />

        {/* Development info */}
        {process.env.NODE_ENV === 'development' && (
          <div className='text-xs text-gray-400'>
            Renders: {renderCount} | Options: {memoizedOptions.length}
          </div>
        )}
      </div>
    );
  }
);

OptimizedSelectField.displayName = 'OptimizedSelectField';

/**
 * Optimized TextArea Field Component
 */
const OptimizedTextAreaField = memo(
  <T extends FieldValues>({
    name,
    control,
    label,
    placeholder,
    rows = 3,
    maxLength,
    disabled = false,
    required = false,
    className = '',
  }: TextAreaFieldProps<T>) => {
    const renderCount = useRenderCount(`OptimizedTextAreaField-${name}`);

    const {
      field: { onChange, onBlur, value, ref },
      fieldState: { error },
    } = useController({
      name,
      control,
      defaultValue: '' as any,
    });

    // Stable callbacks
    const handleChange = useStableCallback(
      (e: React.ChangeEvent<HTMLTextAreaElement>) => {
        onChange(e.target.value);
      },
      [onChange]
    );

    const handleBlur = useStableCallback(
      (e: React.FocusEvent<HTMLTextAreaElement>) => {
        onBlur();
      },
      [onBlur]
    );

    // Character count for maxLength
    const characterCount = useMemo(() => {
      if (!maxLength) return null;
      const current = (value || '').length;
      return `${current}/${maxLength}`;
    }, [value, maxLength]);

    return (
      <div className='space-y-2'>
        <FieldLabel label={label} required={required} htmlFor={name} />
        <textarea
          id={name}
          ref={ref}
          rows={rows}
          maxLength={maxLength}
          placeholder={placeholder}
          disabled={disabled}
          className={`flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 ${className} ${
            error ? 'border-red-500' : ''
          }`}
          value={value || ''}
          onChange={handleChange}
          onBlur={handleBlur}
        />

        <div className='flex justify-between items-center'>
          <ErrorMessage error={error?.message || ''} />
          {characterCount && <span className='text-xs text-gray-500'>{characterCount}</span>}
        </div>

        {/* Development info */}
        {process.env.NODE_ENV === 'development' && (
          <div className='text-xs text-gray-400'>Renders: {renderCount}</div>
        )}
      </div>
    );
  }
);

OptimizedTextAreaField.displayName = 'OptimizedTextAreaField';

// Export all optimized field components
export {
  ErrorMessage,
  FieldLabel,
  OptimizedSelectField,
  OptimizedTextAreaField,
  OptimizedTextField,
};

// Default export for lazy loading (main text field)
export default OptimizedTextField;
