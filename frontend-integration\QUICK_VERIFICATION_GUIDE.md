# 🚀 Quick CSP Verification Guide

## **IMMEDIATE STEPS TO TEST CSP FIXES**

### 1. **Restart Your Dev Server (CRITICAL)**
```bash
# Stop current server (Ctrl+C)
npm run dev
```

### 2. **Check Configuration Files**
Verify these files have the correct content:

**`next.config.mjs`:**
```javascript
async rewrites() {
  return [
    {
      source: "/api/:path*",
      destination: "http://localhost:8000/api/v1/:path*", // ✅ backend
    },
  ];
}
```

**`api/apiClient.ts`:**
```javascript
baseURL: "/api", // ✅ Always relative
```

### 3. **Test Backend Directly**
```bash
# Test if backend is running
node test-csp-verification.js
```
**Expected**: Backend accessible on `http://localhost:8000`

### 4. **Test API Connectivity (NEW!)**
Navigate to `http://localhost:3000/test` to debug API connectivity:
- ✅ **Page loads** without errors
- ✅ **Console shows** `[API ▶]` and `[API ✓]` logs
- ✅ **Data displays** if backend is reachable
- ❌ **Error displays** if rewrites aren't working

### 5. **Test in Browser Console**
Navigate to `http://localhost:3000/dashboard/students` and run in console:
```javascript
// Test if rewrites are working
await fetch('/api/students/').then(r => r.status)
```
**Expected**: `200` (not blocked by CSP)

### 6. **Check CSP Diagnostics**
Look in browser console for:
```
[CSP] Current Configuration: { baseURL: "/api", note: "Requests go to /api/* → Next.js rewrites to backend" }
```

### 7. **Test Student Creation**
- Click "Add Student" button
- Fill out the form and submit
- **Check Console**: Should see `[API ▶]` and `[API ✓]` logs
- **Check Network**: Should see requests to `/api/students/` (not localhost:8000)

## 🔍 **What to Look For**

### ✅ **Success Indicators**
- Console shows `[CSP]` diagnostics with `/api` baseURL
- API calls use `/api/students/` (relative URLs)
- No CSP violations in console
- Student creation succeeds with toast message
- Network tab shows requests to `localhost:3000/api/students/`
- Backend test script passes: `node test-csp-verification.js`
- Test page at `/test` shows successful data fetch

### ❌ **Failure Indicators**
- Console shows "Refused to connect" errors
- API calls still go to `localhost:8000`
- CSP violations in console
- Student creation fails with network errors
- Backend test script fails
- Test page at `/test` shows error

## 🚨 **If Still Blocked**

### Check for Duplicate CSP
```bash
# Search for duplicate CSP headers
grep -R "Content-Security-Policy" .
```
**Should only show**: `next.config.mjs` (if any)

### Check for Meta Tags
In `app/layout.tsx`, ensure NO meta CSP tag:
```tsx
// ❌ REMOVE THIS if it exists:
<meta httpEquiv="Content-Security-Policy" content="..." />
```

### Verify Rewrites
Check `next.config.mjs` has:
```javascript
async rewrites() {
  return [
    { source: '/api/:path*', destination: 'http://localhost:8000/api/v1/:path*' }
  ];
}
```

### Check API Client
Ensure `apiClient.ts` has:
```javascript
baseURL: "/api", // relative, not http://localhost:8000
```

### Verify Backend
Ensure backend is running on `localhost:8000`:
```bash
uvicorn app.main:app --reload --host localhost --port 8000
```

### Test Rewrites with Debug Page
Visit `http://localhost:3000/test` and check:
- Console logs for API requests
- Network tab for request URLs
- Error messages if rewrites fail

## 🎯 **Expected Result**

Your Students module should now work with:
- ✅ **No CSP blocks** (using same-origin rewrites)
- ✅ **API calls to `/api/students/`** (not localhost:8000)
- ✅ **Clear diagnostic logging** in console
- ✅ **Successful student creation** with toast messages
- ✅ **Network requests to localhost:3000/api/students/**
- ✅ **Backend accessible on localhost:8000**
- ✅ **Test page at /test shows successful API calls**

## 📋 **Quick Test Commands**

```bash
# 1. Test backend
node test-csp-verification.js

# 2. Restart dev server
npm run dev

# 3. Test API connectivity
# Navigate to http://localhost:3000/test

# 4. Test students page
# Navigate to http://localhost:3000/dashboard/students

# 5. Run in console: await fetch('/api/students/').then(r => r.status)
# 6. Check for [CSP] logs in console
# 7. Test student creation in UI
```

## 🔧 **Browser Console Test**

Copy and paste this into your browser console:
```javascript
// Quick CSP test
fetch('/api/students/')
  .then(r => console.log('✅ API working, status:', r.status))
  .catch(e => console.log('❌ API blocked:', e.message));
```

## 🚀 **Backend Startup Command**

```bash
# Start backend on localhost:8000
uvicorn app.main:app --reload --host localhost --port 8000
```

## 🧪 **Debug Test Page**

Visit `http://localhost:3000/test` to:
- Test API connectivity directly
- See detailed error messages
- Verify rewrites are working
- Debug network issues

**Remember**: Always restart dev server after config changes!
