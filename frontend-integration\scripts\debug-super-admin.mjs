// Debug script to check SUPER_ADMIN role and JW<PERSON> token
// Run with: node scripts/debug-super-admin.mjs

import jwt from 'jsonwebtoken';

console.log('🔍 SUPER_ADMIN Debug Script');
console.log('=' .repeat(50));

// Check environment variables
const apiToken = process.env.API_TOKEN;
console.log('1. Environment Check:');
console.log(`   API_TOKEN exists: ${!!apiToken}`);
console.log(`   API_TOKEN length: ${apiToken ? apiToken.length : 0}`);

if (!apiToken) {
  console.log('❌ No API_TOKEN found. Set it with:');
  console.log('   $env:API_TOKEN="your_jwt_token"; node scripts/debug-super-admin.mjs');
  process.exit(1);
}

// Decode JWT token (without verification for debugging)
console.log('\n2. JWT Token Analysis:');
try {
  const decoded = jwt.decode(apiToken, { complete: true });
  
  if (!decoded) {
    console.log('❌ Failed to decode JWT token');
    process.exit(1);
  }

  console.log('   Token Header:');
  console.log(`     Algorithm: ${decoded.header.alg}`);
  console.log(`     Type: ${decoded.header.typ}`);
  
  console.log('   Token Payload:');
  const payload = decoded.payload;
  console.log(`     Subject (sub): ${payload.sub}`);
  console.log(`     Email: ${payload.email || 'Not set'}`);
  console.log(`     Role: ${payload.role || 'Not set'}`);
  console.log(`     Issued At: ${payload.iat ? new Date(payload.iat * 1000).toISOString() : 'Not set'}`);
  console.log(`     Expires At: ${payload.exp ? new Date(payload.exp * 1000).toISOString() : 'Not set'}`);
  
  // Check if token is expired
  if (payload.exp) {
    const now = Math.floor(Date.now() / 1000);
    const isExpired = now > payload.exp;
    console.log(`     Is Expired: ${isExpired ? '❌ YES' : '✅ NO'}`);
    
    if (isExpired) {
      console.log('     ⚠️  Token is expired! Get a new one with: node scripts/get-jwt-token.mjs');
    }
  }

  // Role analysis
  console.log('\n3. Role Analysis:');
  const userRole = payload.role;
  console.log(`   User Role: "${userRole}"`);
  console.log(`   Role Type: ${typeof userRole}`);
  console.log(`   Is SUPER_ADMIN: ${userRole === 'SUPER_ADMIN' ? '✅ YES' : '❌ NO'}`);
  console.log(`   Case Sensitive Check: "${userRole}" === "SUPER_ADMIN" = ${userRole === 'SUPER_ADMIN'}`);
  
  if (userRole !== 'SUPER_ADMIN') {
    console.log('   ⚠️  Role is not SUPER_ADMIN. Expected: "SUPER_ADMIN"');
    console.log('   💡 Possible issues:');
    console.log('      - Role is different (ADMIN, TEACHER, etc.)');
    console.log('      - Case mismatch (super_admin vs SUPER_ADMIN)');
    console.log('      - Role not set in JWT token');
  }

  // Permission simulation
  console.log('\n4. Permission Simulation:');
  const canCreateClass = userRole === 'SUPER_ADMIN';
  console.log(`   canCreateClass(${userRole}): ${canCreateClass ? '✅ TRUE' : '❌ FALSE'}`);
  
  if (canCreateClass) {
    console.log('   ✅ User should see Create Class button');
  } else {
    console.log('   ❌ User will NOT see Create Class button');
    console.log('   💡 To fix: Ensure JWT token has role: "SUPER_ADMIN"');
  }

} catch (error) {
  console.log('❌ Error decoding JWT token:', error.message);
  console.log('💡 Token might be malformed or corrupted');
}

// Test API endpoint
console.log('\n5. API Endpoint Test:');
const baseUrl = process.env.NEXT_PUBLIC_API_URL || 'http://127.0.0.1:8000/api/v1';

try {
  const response = await fetch(`${baseUrl}/classes/`, {
    headers: {
      'Authorization': `Bearer ${apiToken}`,
      'Content-Type': 'application/json'
    }
  });

  console.log(`   GET ${baseUrl}/classes/`);
  console.log(`   Status: ${response.status} ${response.statusText}`);
  
  if (response.ok) {
    console.log('   ✅ API authentication successful');
  } else {
    console.log('   ❌ API authentication failed');
    const errorText = await response.text();
    console.log(`   Error: ${errorText}`);
  }
} catch (error) {
  console.log('   ❌ API request failed:', error.message);
  console.log('   💡 Make sure backend server is running');
}

console.log('\n6. Troubleshooting Checklist:');
console.log('   □ JWT token is not expired');
console.log('   □ JWT token contains role: "SUPER_ADMIN"');
console.log('   □ Frontend user object is loaded correctly');
console.log('   □ canCreateClass() function returns true');
console.log('   □ ModulePageLayout receives createRoute prop');
console.log('   □ Backend server is running and accessible');

console.log('\n🔧 Next Steps:');
console.log('   1. If role is not SUPER_ADMIN, get a new token with correct role');
console.log('   2. If token is expired, run: node scripts/get-jwt-token.mjs');
console.log('   3. Check frontend with debug component on classes page');
console.log('   4. Verify ModulePageLayout component implementation');

console.log('\n' + '=' .repeat(50));
console.log('Debug complete! Check the results above. 🔍');
