'use client';

import React, { useState } from 'react';
import { Calendar, Plus, Edit2, Trash2, BookOpen, Target, AlertTriangle } from 'lucide-react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle } from '@/components/ui/alert-dialog';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useToast } from '@/hooks/use-toast';

interface ExamTerm {
  id: string;
  name: string;
  startDate: string;
  endDate: string;
  examStartDate: string;
  examEndDate: string;
  weightage: number;
  subjects: string[];
  defaultMaxMarks: number;
  isActive: boolean;
}

interface Subject {
  id: string;
  name: string;
  code: string;
}

// Mock data
const mockTerms: ExamTerm[] = [
  {
    id: '1',
    name: 'First Term',
    startDate: '2024-04-01',
    endDate: '2024-08-31',
    examStartDate: '2024-08-15',
    examEndDate: '2024-08-31',
    weightage: 40,
    subjects: ['math', 'english', 'science'],
    defaultMaxMarks: 100,
    isActive: true,
  },
  {
    id: '2',
    name: 'Second Term',
    startDate: '2024-09-01',
    endDate: '2024-12-31',
    examStartDate: '2024-12-15',
    examEndDate: '2024-12-31',
    weightage: 35,
    subjects: ['math', 'english', 'science', 'history'],
    defaultMaxMarks: 100,
    isActive: true,
  },
  {
    id: '3',
    name: 'Final Term',
    startDate: '2025-01-01',
    endDate: '2025-03-31',
    examStartDate: '2025-03-15',
    examEndDate: '2025-03-31',
    weightage: 25,
    subjects: ['math', 'english', 'science', 'history', 'geography'],
    defaultMaxMarks: 100,
    isActive: true,
  },
];

const mockSubjects: Subject[] = [
  { id: 'math', name: 'Mathematics', code: 'MATH' },
  { id: 'english', name: 'English', code: 'ENG' },
  { id: 'science', name: 'Science', code: 'SCI' },
  { id: 'history', name: 'History', code: 'HIST' },
  { id: 'geography', name: 'Geography', code: 'GEO' },
];

interface ExamTermsFormProps {
  onDataChange?: () => void;
}

/**
 * ExamTermsForm Component
 * 
 * Features:
 * - Terms with date ranges and weightage %
 * - Progress indicator for total weightage
 * - Subject attachment with default max marks
 * - Validation: total weightage ≤ 100%
 */
export function ExamTermsForm({ onDataChange }: ExamTermsFormProps) {
  const { toast } = useToast();
  const [terms, setTerms] = useState<ExamTerm[]>(mockTerms);
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [editingTerm, setEditingTerm] = useState<ExamTerm | null>(null);
  const [deletingTerm, setDeletingTerm] = useState<ExamTerm | null>(null);
  const [formData, setFormData] = useState<Partial<ExamTerm>>({
    weightage: 0,
    defaultMaxMarks: 100,
    subjects: [],
    isActive: true,
  });

  // Calculate total weightage
  const totalWeightage = terms.filter(term => term.isActive).reduce((sum, term) => sum + term.weightage, 0);
  const isWeightageValid = totalWeightage <= 100;

  // Handle form field change
  const handleFieldChange = (field: keyof ExamTerm, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  // Handle subject selection
  const handleSubjectToggle = (subjectId: string) => {
    setFormData(prev => ({
      ...prev,
      subjects: prev.subjects?.includes(subjectId)
        ? prev.subjects.filter(id => id !== subjectId)
        : [...(prev.subjects || []), subjectId]
    }));
  };

  // Validate form
  const validateForm = () => {
    if (!formData.name?.trim()) {
      toast({
        title: 'Validation Error',
        description: 'Term name is required.',
        variant: 'destructive',
      });
      return false;
    }

    if (!formData.startDate || !formData.endDate || !formData.examStartDate || !formData.examEndDate) {
      toast({
        title: 'Validation Error',
        description: 'All dates are required.',
        variant: 'destructive',
      });
      return false;
    }

    if (new Date(formData.startDate!) >= new Date(formData.endDate!)) {
      toast({
        title: 'Validation Error',
        description: 'End date must be after start date.',
        variant: 'destructive',
      });
      return false;
    }

    if (new Date(formData.examStartDate!) >= new Date(formData.examEndDate!)) {
      toast({
        title: 'Validation Error',
        description: 'Exam end date must be after exam start date.',
        variant: 'destructive',
      });
      return false;
    }

    if (!formData.weightage || formData.weightage <= 0 || formData.weightage > 100) {
      toast({
        title: 'Validation Error',
        description: 'Weightage must be between 1 and 100.',
        variant: 'destructive',
      });
      return false;
    }

    // Check total weightage
    const currentTotal = terms
      .filter(term => term.isActive && term.id !== editingTerm?.id)
      .reduce((sum, term) => sum + term.weightage, 0);
    
    if (currentTotal + formData.weightage! > 100) {
      toast({
        title: 'Validation Error',
        description: `Total weightage cannot exceed 100%. Current total: ${currentTotal}%`,
        variant: 'destructive',
      });
      return false;
    }

    if (!formData.subjects?.length) {
      toast({
        title: 'Validation Error',
        description: 'At least one subject must be selected.',
        variant: 'destructive',
      });
      return false;
    }

    return true;
  };

  // Handle add term
  const handleAdd = () => {
    if (!validateForm()) return;

    const newTerm: ExamTerm = {
      ...formData as ExamTerm,
      id: Date.now().toString(),
    };

    setTerms([...terms, newTerm]);
    setIsAddDialogOpen(false);
    setFormData({ weightage: 0, defaultMaxMarks: 100, subjects: [], isActive: true });
    onDataChange?.();

    toast({
      title: 'Term added',
      description: 'Exam term has been added successfully.',
    });
  };

  // Handle edit term
  const handleEdit = () => {
    if (!validateForm() || !editingTerm) return;

    const updatedTerms = terms.map(term =>
      term.id === editingTerm.id
        ? { ...term, ...formData }
        : term
    );

    setTerms(updatedTerms);
    setEditingTerm(null);
    setFormData({ weightage: 0, defaultMaxMarks: 100, subjects: [], isActive: true });
    onDataChange?.();

    toast({
      title: 'Term updated',
      description: 'Exam term has been updated successfully.',
    });
  };

  // Handle delete term
  const handleDelete = () => {
    if (!deletingTerm) return;

    setTerms(terms.filter(term => term.id !== deletingTerm.id));
    setDeletingTerm(null);
    onDataChange?.();

    toast({
      title: 'Term deleted',
      description: 'Exam term has been deleted successfully.',
    });
  };

  // Open edit dialog
  const openEditDialog = (term: ExamTerm) => {
    setEditingTerm(term);
    setFormData(term);
  };

  // Format date for display
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  return (
    <div className="p-6">
      {/* Header */}
      <div className="mb-6">
        <div className="flex items-center gap-3 mb-4">
          <div className="p-2 bg-gradient-to-r from-sky-50 to-violet-50 rounded-lg">
            <Calendar className="h-6 w-6 text-sky-600" />
          </div>
          <div>
            <h2 className="text-2xl font-bold text-slate-900">Exam Terms</h2>
            <p className="text-slate-600">Configure examination periods and grading system</p>
          </div>
        </div>

        {/* Weightage Progress */}
        <Card className="mb-6">
          <CardHeader className="pb-3">
            <CardTitle className="text-lg flex items-center gap-2">
              <Target className="h-5 w-5 text-sky-600" />
              Total Weightage Progress
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Active Terms Weightage</span>
                <span className={`text-lg font-bold ${isWeightageValid ? 'text-green-600' : 'text-red-600'}`}>
                  {totalWeightage}%
                </span>
              </div>
              <Progress 
                value={totalWeightage} 
                className={`h-3 ${totalWeightage > 100 ? '[&>div]:bg-red-500' : '[&>div]:bg-green-500'}`}
              />
              <div className="flex items-center gap-2 text-sm">
                {isWeightageValid ? (
                  <Badge className="bg-green-100 text-green-800">
                    ✓ Valid ({100 - totalWeightage}% remaining)
                  </Badge>
                ) : (
                  <Badge className="bg-red-100 text-red-800">
                    ⚠ Exceeds 100% by {totalWeightage - 100}%
                  </Badge>
                )}
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Terms Table */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle>Exam Terms</CardTitle>
            <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
              <DialogTrigger asChild>
                <Button size="sm" className="bg-gradient-to-r from-sky-600 to-violet-600">
                  <Plus className="h-4 w-4 mr-2" />
                  Add Term
                </Button>
              </DialogTrigger>
              <DialogContent className="max-w-2xl">
                <DialogHeader>
                  <DialogTitle>Add New Exam Term</DialogTitle>
                </DialogHeader>
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label>Term Name *</Label>
                    <Input
                      value={formData.name || ''}
                      onChange={(e) => handleFieldChange('name', e.target.value)}
                      placeholder="Enter term name"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label>Weightage (%) *</Label>
                    <Input
                      type="number"
                      value={formData.weightage || ''}
                      onChange={(e) => handleFieldChange('weightage', Number(e.target.value))}
                      placeholder="Enter weightage"
                      min="1"
                      max="100"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label>Start Date *</Label>
                    <Input
                      type="date"
                      value={formData.startDate || ''}
                      onChange={(e) => handleFieldChange('startDate', e.target.value)}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label>End Date *</Label>
                    <Input
                      type="date"
                      value={formData.endDate || ''}
                      onChange={(e) => handleFieldChange('endDate', e.target.value)}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label>Exam Start Date *</Label>
                    <Input
                      type="date"
                      value={formData.examStartDate || ''}
                      onChange={(e) => handleFieldChange('examStartDate', e.target.value)}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label>Exam End Date *</Label>
                    <Input
                      type="date"
                      value={formData.examEndDate || ''}
                      onChange={(e) => handleFieldChange('examEndDate', e.target.value)}
                    />
                  </div>
                  <div className="space-y-2 col-span-2">
                    <Label>Default Max Marks</Label>
                    <Input
                      type="number"
                      value={formData.defaultMaxMarks || ''}
                      onChange={(e) => handleFieldChange('defaultMaxMarks', Number(e.target.value))}
                      placeholder="Enter default max marks"
                      min="1"
                    />
                  </div>
                  <div className="space-y-2 col-span-2">
                    <Label>Subjects *</Label>
                    <div className="grid grid-cols-2 gap-2 p-3 border rounded-lg max-h-32 overflow-y-auto">
                      {mockSubjects.map((subject) => (
                        <label key={subject.id} className="flex items-center space-x-2 cursor-pointer">
                          <input
                            type="checkbox"
                            checked={formData.subjects?.includes(subject.id) || false}
                            onChange={() => handleSubjectToggle(subject.id)}
                            className="rounded"
                          />
                          <span className="text-sm">{subject.name}</span>
                        </label>
                      ))}
                    </div>
                  </div>
                </div>
                <div className="flex justify-end gap-2 pt-4">
                  <Button variant="outline" onClick={() => setIsAddDialogOpen(false)}>
                    Cancel
                  </Button>
                  <Button onClick={handleAdd}>Add Term</Button>
                </div>
              </DialogContent>
            </Dialog>
          </div>
        </CardHeader>
        <CardContent>
          {terms.length === 0 ? (
            <div className="text-center py-12 text-slate-500">
              <Calendar className="h-12 w-12 mx-auto mb-4 text-slate-300" />
              <p>No exam terms configured</p>
              <p className="text-sm">Add your first exam term to get started</p>
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Term Name</TableHead>
                  <TableHead>Period</TableHead>
                  <TableHead>Exam Period</TableHead>
                  <TableHead>Weightage</TableHead>
                  <TableHead>Subjects</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead className="w-24">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {terms.map((term) => (
                  <TableRow key={term.id}>
                    <TableCell className="font-medium">{term.name}</TableCell>
                    <TableCell className="text-sm">
                      {formatDate(term.startDate)} - {formatDate(term.endDate)}
                    </TableCell>
                    <TableCell className="text-sm">
                      {formatDate(term.examStartDate)} - {formatDate(term.examEndDate)}
                    </TableCell>
                    <TableCell>
                      <Badge variant="outline" className="font-mono">
                        {term.weightage}%
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-1">
                        <BookOpen className="h-4 w-4 text-slate-400" />
                        <span className="text-sm">{term.subjects.length} subjects</span>
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge variant={term.isActive ? 'default' : 'secondary'}>
                        {term.isActive ? 'Active' : 'Inactive'}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-1">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => openEditDialog(term)}
                        >
                          <Edit2 className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => setDeletingTerm(term)}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>

      {/* Edit Dialog */}
      <Dialog open={!!editingTerm} onOpenChange={() => setEditingTerm(null)}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Edit Exam Term</DialogTitle>
          </DialogHeader>
          {/* Same form fields as add dialog */}
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label>Term Name *</Label>
              <Input
                value={formData.name || ''}
                onChange={(e) => handleFieldChange('name', e.target.value)}
                placeholder="Enter term name"
              />
            </div>
            <div className="space-y-2">
              <Label>Weightage (%) *</Label>
              <Input
                type="number"
                value={formData.weightage || ''}
                onChange={(e) => handleFieldChange('weightage', Number(e.target.value))}
                placeholder="Enter weightage"
                min="1"
                max="100"
              />
            </div>
            {/* Add other fields similar to add dialog */}
          </div>
          <div className="flex justify-end gap-2 pt-4">
            <Button variant="outline" onClick={() => setEditingTerm(null)}>
              Cancel
            </Button>
            <Button onClick={handleEdit}>Save Changes</Button>
          </div>
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={!!deletingTerm} onOpenChange={() => setDeletingTerm(null)}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <div className="flex items-center gap-3">
              <div className="p-2 bg-red-50 rounded-lg">
                <AlertTriangle className="h-5 w-5 text-red-600" />
              </div>
              <div>
                <AlertDialogTitle>Delete Exam Term</AlertDialogTitle>
              </div>
            </div>
            <AlertDialogDescription>
              Are you sure you want to delete "{deletingTerm?.name}"? This will remove all associated exam data and cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDelete}
              className="bg-red-600 hover:bg-red-700"
            >
              Delete Term
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}
