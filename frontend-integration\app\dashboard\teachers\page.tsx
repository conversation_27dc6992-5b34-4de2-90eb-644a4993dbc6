'use client';

import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import {
  Award,
  Building,
  Edit,
  MoreHorizontal,
  Plus,
  Save,
  Search,
  Trash2,
  Users,
  X,
} from 'lucide-react';
import { useMemo, useState } from 'react';

// API Service
const api = {
  async fetchTeachers() {
    // Get token from either storage location
    const token = localStorage.getItem('access_token') || localStorage.getItem('auth.token');

    const response = await fetch('http://127.0.0.1:8000/api/v1/teachers/', {
      headers: {
        ...(token && { Authorization: `Bearer ${token}` }),
        'Content-Type': 'application/json',
      },
    });

    if (response.status === 404) {
      return []; // Return empty array for 404
    }

    if (!response.ok) {
      throw new Error(`API Error: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    return Array.isArray(data) ? data : [];
  },

  async createTeacher(teacherData: any) {
    const token = localStorage.getItem('access_token') || localStorage.getItem('auth.token');

    const response = await fetch('http://127.0.0.1:8000/api/v1/teachers/', {
      method: 'POST',
      headers: {
        ...(token && { Authorization: `Bearer ${token}` }),
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(teacherData),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.detail || 'Failed to create teacher');
    }

    return response.json();
  },

  async updateTeacher(teacherId, teacherData) {
    const response = await fetch(`http://127.0.0.1:8000/api/v1/teachers/${teacherId}`, {
      method: 'PUT',
      headers: {
        ...(localStorage.getItem('access_token') || localStorage.getItem('auth.token')
          ? {
              Authorization: `Bearer ${
                localStorage.getItem('access_token') || localStorage.getItem('auth.token')
              }`,
            }
          : {}),
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(teacherData),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.detail || 'Failed to update teacher');
    }

    return response.json();
  },

  async deleteTeacher(teacherId) {
    const response = await fetch(`http://127.0.0.1:8000/api/v1/teachers/${teacherId}`, {
      method: 'DELETE',
      headers: {
        ...(localStorage.getItem('access_token') || localStorage.getItem('auth.token')
          ? {
              Authorization: `Bearer ${
                localStorage.getItem('access_token') || localStorage.getItem('auth.token')
              }`,
            }
          : {}),
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.detail || 'Failed to delete teacher');
    }

    return { success: true };
  },
};

export default function TeachersPage() {
  const queryClient = useQueryClient();
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('ALL');
  const [departmentFilter, setDepartmentFilter] = useState('ALL');
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [editingTeacher, setEditingTeacher] = useState(null);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [deletingTeacher, setDeletingTeacher] = useState(null);

  // Fetch Teachers Query
  const {
    data: teachers = [],
    isLoading,
    error,
    refetch,
  } = useQuery({
    queryKey: ['teachers'],
    queryFn: api.fetchTeachers,
    retry: 1,
    staleTime: 5 * 60 * 1000,
    refetchOnWindowFocus: false,
  });

  // Create Teacher Mutation
  const createMutation = useMutation({
    mutationFn: api.createTeacher,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['teachers'] });
      setIsCreateDialogOpen(false);
      showToast('Teacher created successfully!', 'success');
    },
    onError: error => {
      showToast(error.message, 'error');
    },
  });

  // Update Teacher Mutation
  const updateMutation = useMutation({
    mutationFn: ({ id, data }) => api.updateTeacher(id, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['teachers'] });
      setIsEditDialogOpen(false);
      setEditingTeacher(null);
      showToast('Teacher updated successfully!', 'success');
    },
    onError: error => {
      showToast(error.message, 'error');
    },
  });

  // Delete Teacher Mutation
  const deleteMutation = useMutation({
    mutationFn: api.deleteTeacher,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['teachers'] });
      setIsDeleteDialogOpen(false);
      setDeletingTeacher(null);
      showToast('Teacher deleted successfully!', 'success');
    },
    onError: error => {
      showToast(error.message, 'error');
    },
  });

  // Toast notification
  const showToast = (message, type) => {
    // Simple toast implementation - replace with your toast library
    const toast = document.createElement('div');
    toast.className = `fixed top-4 right-4 p-4 rounded-lg z-50 ${
      type === 'success' ? 'bg-green-500' : 'bg-red-500'
    } text-white`;
    toast.textContent = message;
    document.body.appendChild(toast);
    setTimeout(() => document.body.removeChild(toast), 3000);
  };

  // Filtered data
  const filteredTeachers = useMemo(() => {
    return teachers.filter(teacher => {
      const searchFields = [
        teacher.name || teacher.full_name || '',
        teacher.email || '',
        teacher.subject || '',
        teacher.department || '',
      ]
        .join(' ')
        .toLowerCase();

      const matchesSearch = searchTerm === '' || searchFields.includes(searchTerm.toLowerCase());
      const matchesStatus = statusFilter === 'ALL' || teacher.status === statusFilter;
      const matchesDepartment =
        departmentFilter === 'ALL' || teacher.department === departmentFilter;

      return matchesSearch && matchesStatus && matchesDepartment;
    });
  }, [teachers, searchTerm, statusFilter, departmentFilter]);

  // Get unique departments
  const departments = useMemo(() => {
    const uniqueDepts = [...new Set(teachers.map(t => t.department).filter(Boolean))];
    return ['ALL', ...uniqueDepts];
  }, [teachers]);

  // Stats
  const stats = useMemo(() => {
    const activeTeachers = teachers.filter(
      t => t.status === 'ACTIVE' || t.status === 'active'
    ).length;
    const avgExperience =
      teachers.length && teachers.some(t => t.experience)
        ? Math.round(
            teachers.filter(t => t.experience).reduce((sum, t) => sum + (t.experience || 0), 0) /
              teachers.filter(t => t.experience).length
          )
        : 0;

    return {
      total: teachers.length,
      active: activeTeachers,
      departments: departments.length - 1,
      avgExperience,
    };
  }, [teachers, departments]);

  // Action handlers
  const handleEdit = teacher => {
    setEditingTeacher(teacher);
    setIsEditDialogOpen(true);
  };

  const handleDelete = teacher => {
    setDeletingTeacher(teacher);
    setIsDeleteDialogOpen(true);
  };

  const confirmDelete = () => {
    if (deletingTeacher) {
      deleteMutation.mutate(deletingTeacher.id);
    }
  };

  if (isLoading) {
    return <LoadingSkeleton />;
  }

  if (error) {
    return <ErrorState onRetry={refetch} error={error} />;
  }

  return (
    <div className='min-h-screen bg-gray-50 p-6'>
      <div className='max-w-7xl mx-auto space-y-6'>
        {/* Header */}
        <div className='flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4'>
          <div>
            <h1 className='text-3xl font-bold text-gray-900'>Teachers</h1>
            <p className='text-gray-600 mt-1'>Manage and view all teachers in the system</p>
          </div>
          <button
            onClick={() => setIsCreateDialogOpen(true)}
            className='flex items-center gap-2 bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors'
          >
            <Plus className='h-4 w-4' />
            Add Teacher
          </button>
        </div>

        {/* Stats Cards */}
        <div className='grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6'>
          <StatCard
            title='Total Teachers'
            value={stats.total}
            icon={Users}
            color='bg-blue-100 text-blue-600'
          />
          <StatCard
            title='Active Teachers'
            value={stats.active}
            icon={Award}
            color='bg-green-100 text-green-600'
          />
          <StatCard
            title='Departments'
            value={stats.departments}
            icon={Building}
            color='bg-purple-100 text-purple-600'
          />
          <StatCard
            title='Avg Experience'
            value={stats.avgExperience ? `${stats.avgExperience} years` : 'N/A'}
            icon={Award}
            color='bg-orange-100 text-orange-600'
          />
        </div>

        {/* Filters */}
        <div className='bg-white rounded-lg shadow-sm p-6'>
          <div className='grid grid-cols-1 md:grid-cols-3 gap-4'>
            {/* Search */}
            <div className='relative'>
              <Search className='absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4' />
              <input
                type='text'
                placeholder='Search teachers...'
                value={searchTerm}
                onChange={e => setSearchTerm(e.target.value)}
                className='w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent'
              />
            </div>

            {/* Department Filter */}
            <select
              value={departmentFilter}
              onChange={e => setDepartmentFilter(e.target.value)}
              className='px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent'
            >
              {departments.map(dept => (
                <option key={dept} value={dept}>
                  {dept === 'ALL' ? 'All Departments' : dept}
                </option>
              ))}
            </select>

            {/* Status Filter */}
            <select
              value={statusFilter}
              onChange={e => setStatusFilter(e.target.value)}
              className='px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent'
            >
              <option value='ALL'>All Status</option>
              <option value='ACTIVE'>Active</option>
              <option value='active'>active</option>
              <option value='INACTIVE'>Inactive</option>
              <option value='inactive'>inactive</option>
            </select>
          </div>
        </div>

        {/* Teachers Table */}
        <div className='bg-white rounded-lg shadow-sm overflow-hidden'>
          <div className='px-6 py-4 border-b border-gray-200'>
            <h3 className='text-lg font-semibold text-gray-900'>
              Teachers ({filteredTeachers.length})
            </h3>
          </div>

          {filteredTeachers.length === 0 ? (
            <EmptyState searchTerm={searchTerm} onAddClick={() => setIsCreateDialogOpen(true)} />
          ) : (
            <div className='overflow-x-auto'>
              <table className='w-full'>
                <thead className='bg-gray-50'>
                  <tr>
                    <th className='px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider'>
                      Teacher
                    </th>
                    <th className='px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider hidden md:table-cell'>
                      Department
                    </th>
                    <th className='px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider hidden lg:table-cell'>
                      Subject
                    </th>
                    <th className='px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider hidden xl:table-cell'>
                      Phone
                    </th>
                    <th className='px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider'>
                      Status
                    </th>
                    <th className='px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider'>
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className='bg-white divide-y divide-gray-200'>
                  {filteredTeachers.map(teacher => (
                    <TeacherRow
                      key={teacher.id}
                      teacher={teacher}
                      onEdit={handleEdit}
                      onDelete={handleDelete}
                    />
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>

        {/* Create Teacher Dialog */}
        {isCreateDialogOpen && (
          <TeacherDialog
            title='Add New Teacher'
            teacher={null}
            onClose={() => setIsCreateDialogOpen(false)}
            onSubmit={data => createMutation.mutate(data)}
            isLoading={createMutation.isPending}
          />
        )}

        {/* Edit Teacher Dialog */}
        {isEditDialogOpen && editingTeacher && (
          <TeacherDialog
            title='Edit Teacher'
            teacher={editingTeacher}
            onClose={() => {
              setIsEditDialogOpen(false);
              setEditingTeacher(null);
            }}
            onSubmit={data => updateMutation.mutate({ id: editingTeacher.id, data })}
            isLoading={updateMutation.isPending}
          />
        )}

        {/* Delete Confirmation Dialog */}
        {isDeleteDialogOpen && deletingTeacher && (
          <DeleteDialog
            teacherName={deletingTeacher.name || deletingTeacher.full_name}
            onClose={() => {
              setIsDeleteDialogOpen(false);
              setDeletingTeacher(null);
            }}
            onConfirm={confirmDelete}
            isLoading={deleteMutation.isPending}
          />
        )}
      </div>
    </div>
  );
}

// Teacher Row Component
function TeacherRow({ teacher, onEdit, onDelete }) {
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const teacherName = teacher.name || teacher.full_name || 'Unknown';
  const initials = teacherName
    .split(' ')
    .map(n => n[0])
    .join('')
    .toUpperCase();

  return (
    <tr className='hover:bg-gray-50 transition-colors'>
      <td className='px-6 py-4 whitespace-nowrap'>
        <div className='flex items-center'>
          <div className='h-10 w-10 rounded-full bg-gradient-to-r from-blue-500 to-purple-600 flex items-center justify-center text-white font-semibold'>
            {initials}
          </div>
          <div className='ml-4'>
            <div className='text-sm font-medium text-gray-900'>{teacherName}</div>
            <div className='text-sm text-gray-500'>{teacher.email || 'No email'}</div>
          </div>
        </div>
      </td>
      <td className='px-6 py-4 whitespace-nowrap text-sm text-gray-900 hidden md:table-cell'>
        {teacher.department || 'N/A'}
      </td>
      <td className='px-6 py-4 whitespace-nowrap text-sm text-gray-900 hidden lg:table-cell'>
        {teacher.subject || 'N/A'}
      </td>
      <td className='px-6 py-4 whitespace-nowrap text-sm text-gray-900 hidden xl:table-cell'>
        {teacher.phone || 'N/A'}
      </td>
      <td className='px-6 py-4 whitespace-nowrap'>
        <span
          className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
            teacher.status === 'ACTIVE' || teacher.status === 'active'
              ? 'bg-green-100 text-green-800'
              : 'bg-gray-100 text-gray-800'
          }`}
        >
          {teacher.status || 'INACTIVE'}
        </span>
      </td>
      <td className='px-6 py-4 whitespace-nowrap text-right text-sm font-medium'>
        <div className='relative inline-block'>
          <button
            onClick={() => setIsDropdownOpen(!isDropdownOpen)}
            className='p-2 hover:bg-gray-100 rounded-lg transition-colors'
          >
            <MoreHorizontal className='h-4 w-4 text-gray-500' />
          </button>

          {isDropdownOpen && (
            <div className='absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg z-10 border'>
              <div className='py-1'>
                <button
                  onClick={() => {
                    onEdit(teacher);
                    setIsDropdownOpen(false);
                  }}
                  className='w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 flex items-center'
                >
                  <Edit className='h-4 w-4 mr-2' />
                  Edit
                </button>
                <button
                  onClick={() => {
                    onDelete(teacher);
                    setIsDropdownOpen(false);
                  }}
                  className='w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-gray-100 flex items-center'
                >
                  <Trash2 className='h-4 w-4 mr-2' />
                  Delete
                </button>
              </div>
            </div>
          )}
        </div>
      </td>
    </tr>
  );
}

// Teacher Dialog Component
function TeacherDialog({ title, teacher, onClose, onSubmit, isLoading }) {
  const [formData, setFormData] = useState({
    name: teacher?.name || teacher?.full_name || '',
    email: teacher?.email || '',
    department: teacher?.department || '',
    subject: teacher?.subject || '',
    phone: teacher?.phone || '',
    status: teacher?.status || 'ACTIVE',
    experience: teacher?.experience || '',
  });

  const handleSubmit = e => {
    e.preventDefault();
    onSubmit(formData);
  };

  return (
    <div className='fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50'>
      <div className='bg-white rounded-lg p-6 w-full max-w-md mx-4'>
        <div className='flex items-center justify-between mb-4'>
          <h2 className='text-xl font-semibold'>{title}</h2>
          <button onClick={onClose} className='text-gray-400 hover:text-gray-600'>
            <X className='h-5 w-5' />
          </button>
        </div>

        <form onSubmit={handleSubmit} className='space-y-4'>
          <div>
            <label className='block text-sm font-medium text-gray-700 mb-1'>Name</label>
            <input
              type='text'
              value={formData.name}
              onChange={e => setFormData({ ...formData, name: e.target.value })}
              className='w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500'
              required
            />
          </div>

          <div>
            <label className='block text-sm font-medium text-gray-700 mb-1'>Email</label>
            <input
              type='email'
              value={formData.email}
              onChange={e => setFormData({ ...formData, email: e.target.value })}
              className='w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500'
              required
            />
          </div>

          <div>
            <label className='block text-sm font-medium text-gray-700 mb-1'>Department</label>
            <input
              type='text'
              value={formData.department}
              onChange={e => setFormData({ ...formData, department: e.target.value })}
              className='w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500'
            />
          </div>

          <div>
            <label className='block text-sm font-medium text-gray-700 mb-1'>Subject</label>
            <input
              type='text'
              value={formData.subject}
              onChange={e => setFormData({ ...formData, subject: e.target.value })}
              className='w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500'
            />
          </div>

          <div>
            <label className='block text-sm font-medium text-gray-700 mb-1'>Phone</label>
            <input
              type='text'
              value={formData.phone}
              onChange={e => setFormData({ ...formData, phone: e.target.value })}
              className='w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500'
            />
          </div>

          <div>
            <label className='block text-sm font-medium text-gray-700 mb-1'>Status</label>
            <select
              value={formData.status}
              onChange={e => setFormData({ ...formData, status: e.target.value })}
              className='w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500'
            >
              <option value='ACTIVE'>Active</option>
              <option value='INACTIVE'>Inactive</option>
            </select>
          </div>

          <div className='flex gap-3 pt-4'>
            <button
              type='button'
              onClick={onClose}
              className='flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50'
              disabled={isLoading}
            >
              Cancel
            </button>
            <button
              type='submit'
              className='flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 flex items-center justify-center'
              disabled={isLoading}
            >
              {isLoading ? (
                'Saving...'
              ) : (
                <>
                  <Save className='h-4 w-4 mr-2' />
                  Save
                </>
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}

// Delete Dialog Component
function DeleteDialog({ teacherName, onClose, onConfirm, isLoading }) {
  return (
    <div className='fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50'>
      <div className='bg-white rounded-lg p-6 w-full max-w-sm mx-4'>
        <h2 className='text-xl font-semibold mb-2'>Delete Teacher</h2>
        <p className='text-gray-600 mb-6'>
          Are you sure you want to delete <strong>{teacherName}</strong>? This action cannot be
          undone.
        </p>

        <div className='flex gap-3'>
          <button
            type='button'
            onClick={onClose}
            className='flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50'
            disabled={isLoading}
          >
            Cancel
          </button>
          <button
            onClick={onConfirm}
            className='flex-1 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 disabled:opacity-50'
            disabled={isLoading}
          >
            {isLoading ? 'Deleting...' : 'Delete'}
          </button>
        </div>
      </div>
    </div>
  );
}

// Supporting Components
function StatCard({ title, value, icon: Icon, color }) {
  return (
    <div className='bg-white rounded-lg shadow-sm p-6'>
      <div className='flex items-center justify-between'>
        <div>
          <p className='text-sm font-medium text-gray-600'>{title}</p>
          <p className='text-2xl font-bold text-gray-900 mt-1'>{value}</p>
        </div>
        <div className={`p-3 rounded-lg ${color}`}>
          <Icon className='h-6 w-6' />
        </div>
      </div>
    </div>
  );
}

function LoadingSkeleton() {
  return (
    <div className='min-h-screen bg-gray-50 p-6'>
      <div className='max-w-7xl mx-auto space-y-6'>
        <div className='animate-pulse'>
          <div className='h-8 bg-gray-300 rounded w-1/4 mb-2' />
          <div className='h-4 bg-gray-300 rounded w-1/2' />
        </div>

        <div className='grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6'>
          {[...Array(4)].map((_, i) => (
            <div key={i} className='bg-white rounded-lg shadow-sm p-6 animate-pulse'>
              <div className='h-12 bg-gray-300 rounded' />
            </div>
          ))}
        </div>

        <div className='bg-white rounded-lg shadow-sm p-6 animate-pulse'>
          <div className='h-32 bg-gray-300 rounded' />
        </div>
      </div>
    </div>
  );
}

function ErrorState({ onRetry, error }) {
  return (
    <div className='min-h-screen bg-gray-50 flex items-center justify-center p-6'>
      <div className='text-center'>
        <div className='text-red-500 text-6xl mb-4'>⚠️</div>
        <h2 className='text-2xl font-bold text-gray-900 mb-2'>Failed to Load Teachers</h2>
        <p className='text-gray-600 mb-2'>{error?.message || 'Something went wrong'}</p>
        <p className='text-sm text-gray-500 mb-6'>Please check your connection and try again.</p>
        <div className='space-x-4'>
          <button
            onClick={onRetry}
            className='bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors'
          >
            Try Again
          </button>
          <button
            onClick={() => window.location.reload()}
            className='bg-gray-200 text-gray-800 px-6 py-2 rounded-lg hover:bg-gray-300 transition-colors'
          >
            Refresh Page
          </button>
        </div>
      </div>
    </div>
  );
}

function EmptyState({ searchTerm, onAddClick }) {
  return (
    <div className='text-center py-12'>
      <div className='text-gray-400 text-6xl mb-4'>👨‍🏫</div>
      <h3 className='text-lg font-semibold text-gray-900 mb-2'>
        {searchTerm ? 'No teachers found' : 'No teachers added yet'}
      </h3>
      <p className='text-gray-600 mb-6'>
        {searchTerm
          ? 'Try adjusting your search or filters'
          : 'Get started by adding your first teacher'}
      </p>
      {!searchTerm && (
        <button
          onClick={onAddClick}
          className='bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors'
        >
          Add Teacher
        </button>
      )}
    </div>
  );
}
