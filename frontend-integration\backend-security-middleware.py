# Backend Security Middleware for SUPER_ADMIN Role Implementation
# FastAPI middleware with enhanced security features

from fastapi import FastAPI, HTTPException, Depends, Request, status
from fastapi.security import H<PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from functools import wraps
from typing import List, Optional, Callable, Any
import jwt
import time
import logging
from datetime import datetime, timedelta
import json

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Security configuration
SECURITY_CONFIG = {
    "SESSION_TIMEOUT": 30 * 60,  # 30 minutes
    "MAX_LOGIN_ATTEMPTS": 3,
    "LOCKOUT_DURATION": 15 * 60,  # 15 minutes
    "REQUIRE_MFA_FOR_SUPER_ADMIN": True,
    "JWT_SECRET": "your-secret-key",  # Use environment variable in production
    "JWT_ALGORITHM": "HS256"
}

# Role definitions
class Role:
    SUPER_ADMIN = "SUPER_ADMIN"
    ADMIN = "ADMIN"
    TEACHER = "TEACHER"
    STAFF = "STAFF"
    STUDENT = "STUDENT"

# Security bearer
security = HTTPBearer()

# Audit log model
class AuditLogEntry:
    def __init__(self, user_id: str, user_email: str, user_role: str, 
                 action: str, resource: str = None, resource_id: str = None,
                 success: bool = True, error_message: str = None,
                 ip_address: str = None, user_agent: str = None):
        self.id = f"audit_{int(time.time() * 1000)}"
        self.user_id = user_id
        self.user_email = user_email
        self.user_role = user_role
        self.action = action
        self.resource = resource
        self.resource_id = resource_id
        self.success = success
        self.error_message = error_message
        self.ip_address = ip_address
        self.user_agent = user_agent
        self.timestamp = datetime.utcnow().isoformat()

# In-memory audit log storage (use database in production)
audit_logs = []

# Audit logging function
async def log_audit_event(entry: AuditLogEntry):
    """Log audit event to storage"""
    audit_logs.append(entry)
    logger.info(f"🔍 Audit Log: {entry.action} by {entry.user_email} ({entry.user_role})")
    
    # Keep only last 10000 entries
    if len(audit_logs) > 10000:
        audit_logs.pop(0)

# JWT token validation
async def get_current_user(credentials: HTTPAuthorizationCredentials = Depends(security)):
    """Extract and validate JWT token"""
    try:
        token = credentials.credentials
        payload = jwt.decode(token, SECURITY_CONFIG["JWT_SECRET"], algorithms=[SECURITY_CONFIG["JWT_ALGORITHM"]])
        
        user_id = payload.get("sub")
        user_email = payload.get("email")
        user_role = payload.get("role")
        exp = payload.get("exp")
        
        if not user_id or not user_email or not user_role:
            raise HTTPException(status_code=401, detail="Invalid token payload")
        
        # Check token expiration
        if exp and datetime.utcnow().timestamp() > exp:
            raise HTTPException(status_code=401, detail="Token expired")
        
        return {
            "id": user_id,
            "email": user_email,
            "role": user_role,
            "exp": exp
        }
    except jwt.InvalidTokenError:
        raise HTTPException(status_code=401, detail="Invalid token")

# Role-based access control decorator
def require_role(allowed_roles: List[str]):
    """Decorator to require specific roles for endpoint access"""
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        async def wrapper(*args, **kwargs):
            # Extract user from kwargs (injected by FastAPI)
            user = None
            request = None
            
            for key, value in kwargs.items():
                if isinstance(value, dict) and "role" in value:
                    user = value
                elif hasattr(value, "client") and hasattr(value, "url"):
                    request = value
            
            if not user:
                raise HTTPException(status_code=401, detail="Authentication required")
            
            if user["role"] not in allowed_roles:
                # Log unauthorized access attempt
                await log_audit_event(AuditLogEntry(
                    user_id=user["id"],
                    user_email=user["email"],
                    user_role=user["role"],
                    action="UNAUTHORIZED_ACCESS_ATTEMPT",
                    resource=func.__name__,
                    success=False,
                    error_message=f"Required roles: {allowed_roles}, user role: {user['role']}",
                    ip_address=request.client.host if request else None,
                    user_agent=request.headers.get("user-agent") if request else None
                ))
                
                raise HTTPException(
                    status_code=403, 
                    detail=f"Access denied. Required roles: {allowed_roles}"
                )
            
            return await func(*args, **kwargs)
        return wrapper
    return decorator

# SUPER_ADMIN only decorator
def require_super_admin(func: Callable) -> Callable:
    """Decorator to require SUPER_ADMIN role"""
    return require_role([Role.SUPER_ADMIN])(func)

# Enhanced audit logging decorator
def audit_action(action: str, resource: str = None):
    """Decorator to automatically log actions"""
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        async def wrapper(*args, **kwargs):
            user = None
            request = None
            
            # Extract user and request from kwargs
            for key, value in kwargs.items():
                if isinstance(value, dict) and "role" in value:
                    user = value
                elif hasattr(value, "client") and hasattr(value, "url"):
                    request = value
            
            start_time = time.time()
            success = True
            error_message = None
            
            try:
                result = await func(*args, **kwargs)
                return result
            except Exception as e:
                success = False
                error_message = str(e)
                raise
            finally:
                if user:
                    await log_audit_event(AuditLogEntry(
                        user_id=user["id"],
                        user_email=user["email"],
                        user_role=user["role"],
                        action=action,
                        resource=resource or func.__name__,
                        success=success,
                        error_message=error_message,
                        ip_address=request.client.host if request else None,
                        user_agent=request.headers.get("user-agent") if request else None
                    ))
        return wrapper
    return decorator

# Example FastAPI endpoints with SUPER_ADMIN security

app = FastAPI(title="School Management API with Enhanced Security")

# Classes endpoints with SUPER_ADMIN restrictions
@app.post("/api/v1/classes/")
@require_super_admin
@audit_action("CREATE_CLASS", "classes")
async def create_class(class_data: dict, user: dict = Depends(get_current_user), request: Request = None):
    """Create class - SUPER_ADMIN only"""
    try:
        # Your class creation logic here
        new_class = {
            "id": f"class_{int(time.time())}",
            **class_data,
            "created_by": user["id"],
            "created_at": datetime.utcnow().isoformat()
        }
        
        logger.info(f"✅ Class created by {user['email']}: {new_class['id']}")
        return new_class
    except Exception as e:
        logger.error(f"❌ Failed to create class: {e}")
        raise HTTPException(status_code=500, detail="Failed to create class")

@app.put("/api/v1/classes/{class_id}")
@require_super_admin
@audit_action("UPDATE_CLASS", "classes")
async def update_class(class_id: str, class_data: dict, user: dict = Depends(get_current_user), request: Request = None):
    """Update class - SUPER_ADMIN only"""
    try:
        # Your class update logic here
        updated_class = {
            "id": class_id,
            **class_data,
            "updated_by": user["id"],
            "updated_at": datetime.utcnow().isoformat()
        }
        
        logger.info(f"✅ Class updated by {user['email']}: {class_id}")
        return updated_class
    except Exception as e:
        logger.error(f"❌ Failed to update class: {e}")
        raise HTTPException(status_code=500, detail="Failed to update class")

@app.delete("/api/v1/classes/{class_id}")
@require_super_admin
@audit_action("DELETE_CLASS", "classes")
async def delete_class(class_id: str, user: dict = Depends(get_current_user), request: Request = None):
    """Delete class - SUPER_ADMIN only"""
    try:
        # Your class deletion logic here
        logger.info(f"✅ Class deleted by {user['email']}: {class_id}")
        return {"message": f"Class {class_id} deleted successfully"}
    except Exception as e:
        logger.error(f"❌ Failed to delete class: {e}")
        raise HTTPException(status_code=500, detail="Failed to delete class")

@app.post("/api/v1/classes/bulk-delete")
@require_super_admin
@audit_action("BULK_DELETE_CLASSES", "classes")
async def bulk_delete_classes(class_ids: List[str], user: dict = Depends(get_current_user), request: Request = None):
    """Bulk delete classes - SUPER_ADMIN only"""
    try:
        # Your bulk deletion logic here
        deleted_count = len(class_ids)
        logger.info(f"✅ Bulk delete by {user['email']}: {deleted_count} classes")
        return {"message": f"{deleted_count} classes deleted successfully", "deleted_ids": class_ids}
    except Exception as e:
        logger.error(f"❌ Failed to bulk delete classes: {e}")
        raise HTTPException(status_code=500, detail="Failed to bulk delete classes")

# System management endpoints
@app.get("/api/v1/audit-logs")
@require_super_admin
@audit_action("VIEW_AUDIT_LOGS", "system")
async def get_audit_logs(user: dict = Depends(get_current_user), request: Request = None):
    """Get audit logs - SUPER_ADMIN only"""
    return {
        "logs": audit_logs[-100:],  # Return last 100 entries
        "total": len(audit_logs)
    }

@app.post("/api/v1/system/backup")
@require_super_admin
@audit_action("BACKUP_SYSTEM", "system")
async def backup_system(user: dict = Depends(get_current_user), request: Request = None):
    """Backup system - SUPER_ADMIN only"""
    try:
        # Your backup logic here
        backup_id = f"backup_{int(time.time())}"
        logger.info(f"✅ System backup initiated by {user['email']}: {backup_id}")
        return {"message": "Backup initiated successfully", "backup_id": backup_id}
    except Exception as e:
        logger.error(f"❌ Failed to backup system: {e}")
        raise HTTPException(status_code=500, detail="Failed to backup system")

# Usage instructions:
"""
To implement this security middleware in your FastAPI backend:

1. Install dependencies:
   pip install fastapi python-jose[cryptography] python-multipart

2. Copy this middleware to your main.py file

3. Update the JWT_SECRET in SECURITY_CONFIG with a secure secret key

4. Apply decorators to your endpoints:
   @require_super_admin  # For SUPER_ADMIN only endpoints
   @audit_action("ACTION_NAME", "resource")  # For audit logging

5. Test with JWT token:
   - Include Authorization: Bearer <token> header
   - Token payload should include: {"sub": "user_id", "email": "<EMAIL>", "role": "SUPER_ADMIN"}

Expected behavior:
- Only SUPER_ADMIN role can access protected endpoints
- All actions are logged with user details, timestamps, and success status
- Unauthorized access attempts are logged and blocked
- Comprehensive audit trail for security compliance
"""
