/**
 * Fees & Billing Validation Schemas
 * 
 * Comprehensive Zod schemas for:
 * - Fee Types Management
 * - Class-wise Fee Schedule
 * - Late Fee & Discount Rules
 * - Opening Balance Management
 */

import { z } from 'zod';

// Common validation patterns
const currencyAmount = z.coerce.number().int().nonnegative().max(9999999, 'Amount cannot exceed 9,999,999');
const percentage = z.coerce.number().min(0).max(100, 'Percentage must be between 0 and 100');

// Fee Types Schema
export const FeeTypeSchema = z.object({
  id: z.string().optional(),
  name: z.string().min(1, 'Fee type name is required').max(50, 'Name must be less than 50 characters'),
  description: z.string().max(200, 'Description must be less than 200 characters').optional(),
  category: z.enum(['ACADEMIC', 'TRANSPORT', 'HOSTEL', 'LIBRARY', 'LABORATORY', 'SPORTS', 'OTHER']),
  isActive: z.boolean().default(true),
  sortOrder: z.number().int().nonnegative().default(0),
  isRequired: z.boolean().default(false),
  createdAt: z.string().optional(),
  updatedAt: z.string().optional(),
});

export const FeeTypesListSchema = z.object({
  feeTypes: z.array(FeeTypeSchema),
});

// Class-wise Fee Schedule Schema (matches your specification exactly)
export const ClassFeeRowSchema = z.object({
  id: z.string().optional(), // client temp id ok
  className: z.string().min(1, 'Class name is required').max(20, 'Class name must be less than 20 characters'),
  admissionFee: currencyAmount,
  monthlyFee: currencyAmount,
  examFee: currencyAmount,
  securityDeposit: currencyAmount,
  certificateFee: currencyAmount,
  // Additional metadata
  isNew: z.boolean().optional(),
  isEdited: z.boolean().optional(),
  version: z.number().int().default(1),
  updatedAt: z.string().optional(),
});

export const ClassFeeScheduleSchema = z.object({
  rows: z.array(ClassFeeRowSchema),
  version: z.number().int().default(1),
  lastSavedAt: z.string().optional(),
});

// Bulk operations schema
export const BulkFeeOperationSchema = z.object({
  operation: z.enum(['SET_MONTHLY_FEE', 'COPY_PREVIOUS_ROW', 'ZERO_EMPTY_CELLS', 'DUPLICATE_ROW']),
  value: z.number().optional(), // for SET_MONTHLY_FEE
  targetRowId: z.string().optional(), // for row-specific operations
});

// CSV Import/Export Schema
export const CSVImportSchema = z.object({
  data: z.array(z.object({
    class: z.string(),
    admissionFee: z.string().or(z.number()),
    monthlyFee: z.string().or(z.number()),
    examFee: z.string().or(z.number()),
    securityDeposit: z.string().or(z.number()),
    certificateFee: z.string().or(z.number()),
  })),
  updateExisting: z.boolean().default(false),
});

export const CSVImportResultSchema = z.object({
  validRows: z.array(ClassFeeRowSchema),
  errors: z.array(z.object({
    row: z.number(),
    field: z.string().optional(),
    message: z.string(),
  })),
  summary: z.object({
    total: z.number(),
    valid: z.number(),
    errors: z.number(),
    duplicates: z.number(),
  }),
});

// Late Fee & Discount Rules Schema
export const LateFeeRuleSchema = z.object({
  id: z.string().optional(),
  type: z.enum(['FLAT', 'PERCENTAGE']),
  value: z.number().min(0, 'Late fee value must be positive'),
  graceDays: z.number().int().min(0, 'Grace days must be non-negative').max(365, 'Grace days cannot exceed 365'),
  maxAmount: z.number().min(0, 'Maximum amount must be positive').optional(),
  isActive: z.boolean().default(true),
  description: z.string().max(200, 'Description must be less than 200 characters').optional(),
});

export const DiscountRuleSchema = z.object({
  id: z.string().optional(),
  name: z.string().min(1, 'Discount name is required').max(50, 'Name must be less than 50 characters'),
  type: z.enum(['FLAT', 'PERCENTAGE']),
  value: z.number().min(0, 'Discount value must be positive'),
  maxAmount: z.number().min(0, 'Maximum amount must be positive').optional(),
  applicableToFees: z.array(z.string()), // Fee type IDs
  conditions: z.object({
    siblingDiscount: z.boolean().default(false),
    earlyPayment: z.boolean().default(false),
    earlyPaymentDays: z.number().int().min(0).optional(),
    minimumAmount: z.number().min(0).optional(),
  }).optional(),
  isActive: z.boolean().default(true),
});

// Per-class rule overrides
export const ClassSpecificRuleSchema = z.object({
  className: z.string().min(1, 'Class name is required'),
  lateFeeRule: LateFeeRuleSchema.optional(),
  discountRules: z.array(DiscountRuleSchema).optional(),
});

export const FeeRulesSchema = z.object({
  globalLateFeeRule: LateFeeRuleSchema,
  globalDiscountRules: z.array(DiscountRuleSchema),
  classSpecificRules: z.array(ClassSpecificRuleSchema),
});

// Fee Calculation Preview Schema
export const FeeCalculationPreviewSchema = z.object({
  baseAmount: z.number(),
  lateFee: z.number(),
  discount: z.number(),
  finalAmount: z.number(),
  daysLate: z.number(),
  appliedRules: z.array(z.object({
    type: z.enum(['LATE_FEE', 'DISCOUNT']),
    name: z.string(),
    value: z.number(),
    calculation: z.string(),
  })),
});

// Opening Balance Schema
export const OpeningBalanceEntrySchema = z.object({
  id: z.string().optional(),
  type: z.enum(['CLASS', 'STUDENT']),
  targetId: z.string().min(1, 'Target ID is required'), // class name or student ID
  targetName: z.string().min(1, 'Target name is required'),
  amount: z.number(), // can be negative for credits
  description: z.string().max(200, 'Description must be less than 200 characters').optional(),
  effectiveDate: z.string().min(1, 'Effective date is required'),
  createdBy: z.string().optional(),
  createdAt: z.string().optional(),
});

export const OpeningBalanceSchema = z.object({
  entries: z.array(OpeningBalanceEntrySchema),
  totalDebit: z.number(),
  totalCredit: z.number(),
  netBalance: z.number(),
});

// Comprehensive Fees Settings Schema
export const FeesSettingsSchema = z.object({
  feeTypes: z.array(FeeTypeSchema),
  classFeeSchedule: ClassFeeScheduleSchema,
  feeRules: FeeRulesSchema,
  openingBalance: OpeningBalanceSchema,
  currency: z.string().default('USD'),
  currencySymbol: z.string().default('$'),
  decimalPlaces: z.number().int().min(0).max(4).default(2),
  settings: z.object({
    allowNegativeBalance: z.boolean().default(false),
    autoCalculateLateFee: z.boolean().default(true),
    sendLateFeeNotifications: z.boolean().default(true),
    gracePeriodForNewStudents: z.number().int().min(0).default(30),
  }),
});

// Export types
export type FeeType = z.infer<typeof FeeTypeSchema>;
export type FeeTypesList = z.infer<typeof FeeTypesListSchema>;
export type ClassFeeRow = z.infer<typeof ClassFeeRowSchema>;
export type ClassFeeSchedule = z.infer<typeof ClassFeeScheduleSchema>;
export type BulkFeeOperation = z.infer<typeof BulkFeeOperationSchema>;
export type CSVImport = z.infer<typeof CSVImportSchema>;
export type CSVImportResult = z.infer<typeof CSVImportResultSchema>;
export type LateFeeRule = z.infer<typeof LateFeeRuleSchema>;
export type DiscountRule = z.infer<typeof DiscountRuleSchema>;
export type ClassSpecificRule = z.infer<typeof ClassSpecificRuleSchema>;
export type FeeRules = z.infer<typeof FeeRulesSchema>;
export type FeeCalculationPreview = z.infer<typeof FeeCalculationPreviewSchema>;
export type OpeningBalanceEntry = z.infer<typeof OpeningBalanceEntrySchema>;
export type OpeningBalance = z.infer<typeof OpeningBalanceSchema>;
export type FeesSettings = z.infer<typeof FeesSettingsSchema>;

// Form schemas for create/update operations
export const CreateFeeTypeSchema = FeeTypeSchema.omit({ id: true, createdAt: true, updatedAt: true });
export const UpdateFeeTypeSchema = FeeTypeSchema.partial().omit({ createdAt: true });
export const CreateClassFeeRowSchema = ClassFeeRowSchema.omit({ id: true, updatedAt: true });
export const UpdateClassFeeRowSchema = ClassFeeRowSchema.partial().omit({ updatedAt: true });

export type CreateFeeType = z.infer<typeof CreateFeeTypeSchema>;
export type UpdateFeeType = z.infer<typeof UpdateFeeTypeSchema>;
export type CreateClassFeeRow = z.infer<typeof CreateClassFeeRowSchema>;
export type UpdateClassFeeRow = z.infer<typeof UpdateClassFeeRowSchema>;
