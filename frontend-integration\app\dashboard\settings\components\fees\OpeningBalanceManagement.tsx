'use client';

import React, { useState } from 'react';
import { DollarSign, Plus, Trash2, Users, User, Calendar, AlertTriangle } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialog<PERSON>ooter, AlertDialogHeader, AlertDialogTitle } from '@/components/ui/alert-dialog';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { useToast } from '@/hooks/use-toast';
import { 
  OpeningBalanceEntrySchema, 
  type OpeningBalanceEntry 
} from '../../schemas/fees.schemas';

// Mock data
const mockOpeningBalances: OpeningBalanceEntry[] = [
  {
    id: '1',
    type: 'CLASS',
    targetId: 'class-10',
    targetName: 'Class 10',
    amount: -5000,
    description: 'Previous year fee adjustment',
    effectiveDate: '2024-04-01',
    createdBy: 'admin',
    createdAt: '2024-03-15T10:00:00Z',
  },
  {
    id: '2',
    type: 'STUDENT',
    targetId: 'std-001',
    targetName: 'John Doe',
    amount: 2500,
    description: 'Outstanding fee from last term',
    effectiveDate: '2024-04-01',
    createdBy: 'admin',
    createdAt: '2024-03-15T11:00:00Z',
  },
  {
    id: '3',
    type: 'CLASS',
    targetId: 'class-9',
    targetName: 'Class 9',
    amount: -1200,
    description: 'Scholarship fund allocation',
    effectiveDate: '2024-04-01',
    createdBy: 'admin',
    createdAt: '2024-03-15T12:00:00Z',
  },
];

interface OpeningBalanceManagementProps {
  onDataChange?: () => void;
}

/**
 * OpeningBalanceManagement Component
 * 
 * Manages opening balances for classes and students with:
 * - Add/Edit/Delete balance entries
 * - Class and student balance tracking
 * - Idempotent operations
 * - Balance summary and validation
 */
export function OpeningBalanceManagement({ onDataChange }: OpeningBalanceManagementProps) {
  const { toast } = useToast();
  const [balanceEntries, setBalanceEntries] = useState<OpeningBalanceEntry[]>(mockOpeningBalances);
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [deletingEntry, setDeletingEntry] = useState<OpeningBalanceEntry | null>(null);

  const form = useForm<OpeningBalanceEntry>({
    resolver: zodResolver(OpeningBalanceEntrySchema.omit({ id: true, createdAt: true, createdBy: true })),
    defaultValues: {
      type: 'CLASS',
      targetId: '',
      targetName: '',
      amount: 0,
      description: '',
      effectiveDate: new Date().toISOString().split('T')[0],
    },
  });

  // Calculate totals
  const totals = balanceEntries.reduce(
    (acc, entry) => {
      if (entry.amount > 0) {
        acc.totalDebit += entry.amount;
      } else {
        acc.totalCredit += Math.abs(entry.amount);
      }
      acc.netBalance += entry.amount;
      return acc;
    },
    { totalDebit: 0, totalCredit: 0, netBalance: 0 }
  );

  // Handle add balance entry
  const handleAddEntry = async (data: Omit<OpeningBalanceEntry, 'id' | 'createdAt' | 'createdBy'>) => {
    try {
      // Check for duplicate entries
      const existingEntry = balanceEntries.find(
        entry => entry.type === data.type && entry.targetId === data.targetId
      );

      if (existingEntry) {
        toast({
          title: 'Duplicate Entry',
          description: `An opening balance already exists for this ${data.type.toLowerCase()}.`,
          variant: 'destructive',
        });
        return;
      }

      const newEntry: OpeningBalanceEntry = {
        ...data,
        id: Date.now().toString(),
        createdBy: 'current-user',
        createdAt: new Date().toISOString(),
      };

      setBalanceEntries(prev => [...prev, newEntry]);
      setIsAddDialogOpen(false);
      form.reset();
      onDataChange?.();

      toast({
        title: 'Balance Added',
        description: 'Opening balance entry has been added successfully.',
      });
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to add opening balance entry.',
        variant: 'destructive',
      });
    }
  };

  // Handle delete entry
  const handleDeleteEntry = () => {
    if (!deletingEntry) return;

    setBalanceEntries(prev => prev.filter(entry => entry.id !== deletingEntry.id));
    setDeletingEntry(null);
    onDataChange?.();

    toast({
      title: 'Balance Deleted',
      description: 'Opening balance entry has been deleted successfully.',
    });
  };

  // Format currency
  const formatCurrency = (amount: number) => {
    const absAmount = Math.abs(amount);
    const sign = amount >= 0 ? '+' : '-';
    return `${sign}₹${absAmount.toLocaleString()}`;
  };

  return (
    <div className="space-y-6">
      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-red-50 rounded-lg">
                <DollarSign className="h-5 w-5 text-red-600" />
              </div>
              <div>
                <p className="text-sm text-slate-600">Total Debit</p>
                <p className="text-xl font-bold text-red-600">₹{totals.totalDebit.toLocaleString()}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-green-50 rounded-lg">
                <DollarSign className="h-5 w-5 text-green-600" />
              </div>
              <div>
                <p className="text-sm text-slate-600">Total Credit</p>
                <p className="text-xl font-bold text-green-600">₹{totals.totalCredit.toLocaleString()}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className={`p-2 rounded-lg ${totals.netBalance >= 0 ? 'bg-blue-50' : 'bg-orange-50'}`}>
                <DollarSign className={`h-5 w-5 ${totals.netBalance >= 0 ? 'text-blue-600' : 'text-orange-600'}`} />
              </div>
              <div>
                <p className="text-sm text-slate-600">Net Balance</p>
                <p className={`text-xl font-bold ${totals.netBalance >= 0 ? 'text-blue-600' : 'text-orange-600'}`}>
                  {formatCurrency(totals.netBalance)}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Opening Balance Entries */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <Calendar className="h-5 w-5 text-sky-600" />
              Opening Balance Entries
            </CardTitle>
            <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
              <DialogTrigger asChild>
                <Button size="sm" className="bg-gradient-to-r from-sky-600 to-violet-600">
                  <Plus className="h-4 w-4 mr-2" />
                  Add Balance Entry
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Add Opening Balance Entry</DialogTitle>
                </DialogHeader>
                <form onSubmit={form.handleSubmit(handleAddEntry)} className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label>Entry Type</Label>
                      <Select
                        value={form.watch('type')}
                        onValueChange={(value) => form.setValue('type', value as any)}
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="CLASS">Class</SelectItem>
                          <SelectItem value="STUDENT">Student</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="space-y-2">
                      <Label>Effective Date</Label>
                      <Input
                        type="date"
                        {...form.register('effectiveDate')}
                      />
                    </div>
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label>
                        {form.watch('type') === 'CLASS' ? 'Class ID' : 'Student ID'}
                      </Label>
                      <Input
                        {...form.register('targetId')}
                        placeholder={form.watch('type') === 'CLASS' ? 'class-10' : 'std-001'}
                      />
                    </div>

                    <div className="space-y-2">
                      <Label>
                        {form.watch('type') === 'CLASS' ? 'Class Name' : 'Student Name'}
                      </Label>
                      <Input
                        {...form.register('targetName')}
                        placeholder={form.watch('type') === 'CLASS' ? 'Class 10' : 'John Doe'}
                      />
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label>Amount (₹)</Label>
                    <Input
                      type="number"
                      {...form.register('amount', { valueAsNumber: true })}
                      placeholder="Enter amount (positive for debit, negative for credit)"
                    />
                    <p className="text-xs text-slate-500">
                      Positive values represent outstanding amounts (debit), negative values represent advance payments (credit)
                    </p>
                  </div>

                  <div className="space-y-2">
                    <Label>Description</Label>
                    <Textarea
                      {...form.register('description')}
                      placeholder="Enter description for this balance entry"
                      rows={2}
                    />
                  </div>

                  <div className="flex justify-end gap-2">
                    <Button type="button" variant="outline" onClick={() => setIsAddDialogOpen(false)}>
                      Cancel
                    </Button>
                    <Button type="submit">Add Entry</Button>
                  </div>
                </form>
              </DialogContent>
            </Dialog>
          </div>
        </CardHeader>
        <CardContent>
          {balanceEntries.length === 0 ? (
            <div className="text-center py-8 text-slate-500">
              <Calendar className="h-12 w-12 mx-auto mb-4 text-slate-300" />
              <p>No opening balance entries</p>
              <p className="text-sm">Add entries to set initial balances for classes or students</p>
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Type</TableHead>
                  <TableHead>Target</TableHead>
                  <TableHead className="text-right">Amount</TableHead>
                  <TableHead>Description</TableHead>
                  <TableHead>Effective Date</TableHead>
                  <TableHead>Created</TableHead>
                  <TableHead className="w-12"></TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {balanceEntries.map((entry) => (
                  <TableRow key={entry.id}>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        {entry.type === 'CLASS' ? (
                          <Users className="h-4 w-4 text-blue-600" />
                        ) : (
                          <User className="h-4 w-4 text-green-600" />
                        )}
                        <Badge variant="outline">
                          {entry.type}
                        </Badge>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div>
                        <p className="font-medium">{entry.targetName}</p>
                        <p className="text-xs text-slate-500">{entry.targetId}</p>
                      </div>
                    </TableCell>
                    <TableCell className="text-right">
                      <span className={`font-medium ${
                        entry.amount >= 0 ? 'text-red-600' : 'text-green-600'
                      }`}>
                        {formatCurrency(entry.amount)}
                      </span>
                    </TableCell>
                    <TableCell>
                      <p className="text-sm">{entry.description || '-'}</p>
                    </TableCell>
                    <TableCell>
                      {new Date(entry.effectiveDate).toLocaleDateString()}
                    </TableCell>
                    <TableCell>
                      <div className="text-xs text-slate-500">
                        <p>{entry.createdBy}</p>
                        <p>{new Date(entry.createdAt!).toLocaleDateString()}</p>
                      </div>
                    </TableCell>
                    <TableCell>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => setDeletingEntry(entry)}
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>

      {/* Balance Validation Warning */}
      {Math.abs(totals.netBalance) > 10000 && (
        <Card className="border-orange-200 bg-orange-50">
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <AlertTriangle className="h-5 w-5 text-orange-600" />
              <div>
                <p className="font-medium text-orange-800">Large Net Balance Detected</p>
                <p className="text-sm text-orange-700">
                  The net opening balance is {formatCurrency(totals.netBalance)}. 
                  Please verify all entries are correct before proceeding.
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={!!deletingEntry} onOpenChange={() => setDeletingEntry(null)}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete Opening Balance Entry</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete this opening balance entry for "{deletingEntry?.targetName}"? 
              This action cannot be undone and may affect financial calculations.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDeleteEntry}
              className="bg-red-600 hover:bg-red-700"
            >
              Delete Entry
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}
