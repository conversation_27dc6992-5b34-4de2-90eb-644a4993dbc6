/**
 * API Services - Barrel Export
 *
 * Centralized export for all API services
 */

// Base API client
export { api as apiClient, default as client } from './apiClient';

// Service exports
export * from './services/attendanceService';
export * from './services/authService';
export * from './services/classService';
export * from './services/examService';
export * from './services/feeService';
export * from './services/gradeService';
export * from './services/studentService';
export * from './services/teacherService';

// Types
export type { ApiError as ApiErrorType, ApiResponse, PaginatedResponse } from '../types';
