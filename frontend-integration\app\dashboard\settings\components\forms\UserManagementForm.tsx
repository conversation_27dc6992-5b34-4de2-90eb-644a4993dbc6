'use client';

import { zod<PERSON>esolver } from '@hookform/resolvers/zod';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { Edit2, Eye, EyeOff, Filter, Plus, Search, Shield, Trash2, Users } from 'lucide-react';
import { useEffect, useMemo, useState } from 'react';
import { useForm } from 'react-hook-form';

import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { useToast } from '@/hooks/use-toast';

// If you already have these types/schemas/utilities, keep the imports.
// Otherwise you can inline your own simple types.
import type {
  CreateUser,
  UpdateUser,
  User,
  UserRoleType,
  UserStatusType,
} from '../../schemas/users.schemas';
import {
  CreateUserSchema,
  UpdateUserSchema,
  canAccessUserManagement,
  getRoleColor,
  getStatusColor,
} from '../../schemas/users.schemas';

/* ------------------------------------------------------------------ */
/* Seed data (used until your real API is connected)                  */
/* ------------------------------------------------------------------ */

const mockUsers: User[] = [
  {
    id: '1',
    name: 'John Admin',
    email: '<EMAIL>',
    role: 'Admin',
    status: 'Active',
    phone: '+91-**********',
    lastLogin: '2024-03-15T10:30:00Z',
    createdAt: '2024-01-01T00:00:00Z',
  },
  {
    id: '2',
    name: 'Sarah Teacher',
    email: '<EMAIL>',
    role: 'Teacher',
    status: 'Active',
    phone: '+91-**********',
    lastLogin: '2024-03-14T15:45:00Z',
    createdAt: '2024-01-15T00:00:00Z',
  },
  {
    id: '3',
    name: 'Mike Accountant',
    email: '<EMAIL>',
    role: 'Accountant',
    status: 'Active',
    phone: '+91-**********',
    lastLogin: '2024-03-13T09:20:00Z',
    createdAt: '2024-02-01T00:00:00Z',
  },
  {
    id: '4',
    name: 'Lisa Clerk',
    email: '<EMAIL>',
    role: 'Clerk',
    status: 'Inactive',
    phone: '+91-**********',
    lastLogin: '2024-03-10T14:15:00Z',
    createdAt: '2024-02-15T00:00:00Z',
  },
];

const mockCurrentUser: User = {
  id: 'current',
  name: 'Current Admin',
  email: '<EMAIL>',
  role: 'Admin',
  status: 'Active',
};

/* ------------------------------------------------------------------ */
/* Local data hooks (fix for “useUsers is not defined”)               */
/* Replace with real API adapters later (same signatures).            */
/* ------------------------------------------------------------------ */

const USERS_QK = ['settings', 'users'] as const;

function useUsers(params?: { search?: string; role?: UserRoleType; status?: UserStatusType }) {
  const qc = useQueryClient();

  // seed cache on first mount
  useEffect(() => {
    const existing = qc.getQueryData<User[]>(USERS_QK);
    if (!existing || existing.length === 0) {
      qc.setQueryData<User[]>(USERS_QK, mockUsers);
    }
  }, [qc]);

  return useQuery<User[]>({
    queryKey: USERS_QK,
    queryFn: async () => qc.getQueryData<User[]>(USERS_QK) || mockUsers,
    staleTime: 60_000,
  });
}

function useCreateUser() {
  const qc = useQueryClient();
  const { toast } = useToast();
  return useMutation({
    mutationFn: async (payload: CreateUser) => {
      // simulate server id + timestamps
      const newUser: User = {
        id: crypto.randomUUID(),
        name: payload.name,
        email: payload.email,
        role: payload.role,
        status: payload.status ?? 'Active',
        phone: payload.phone,
        createdAt: new Date().toISOString(),
      };
      return newUser;
    },
    onSuccess: newUser => {
      qc.setQueryData<User[]>(USERS_QK, prev => [newUser, ...(prev ?? [])]);
      toast({ title: 'User added', description: `${newUser.name} created successfully.` });
    },
  });
}

function useUpdateUser() {
  const qc = useQueryClient();
  const { toast } = useToast();
  return useMutation({
    mutationFn: async ({ id, data }: { id: string; data: UpdateUser }) => ({ id, data }),
    onSuccess: ({ id, data }) => {
      qc.setQueryData<User[]>(USERS_QK, prev =>
        (prev ?? []).map(u => (u.id === id ? ({ ...u, ...data, id } as User) : u))
      );
      toast({ title: 'User updated', description: 'Changes saved.' });
    },
  });
}

function useUpdateUserStatus() {
  const qc = useQueryClient();
  return useMutation({
    mutationFn: async ({ id, data }: { id: string; data: { status: UserStatusType } }) => ({
      id,
      data,
    }),
    onSuccess: ({ id, data }) => {
      qc.setQueryData<User[]>(USERS_QK, prev =>
        (prev ?? []).map(u => (u.id === id ? { ...u, status: data.status } : u))
      );
    },
  });
}

function useDeleteUser() {
  const qc = useQueryClient();
  const { toast } = useToast();
  return useMutation({
    mutationFn: async (id: string) => id,
    onSuccess: id => {
      qc.setQueryData<User[]>(USERS_QK, prev => (prev ?? []).filter(u => u.id !== id));
      toast({ title: 'User deleted' });
    },
  });
}

/* ------------------------------------------------------------------ */
/* Component                                                           */
/* ------------------------------------------------------------------ */

interface UserManagementFormProps {
  onDataChange?: () => void;
}

export function UserManagementForm({ onDataChange }: UserManagementFormProps) {
  const { toast } = useToast();
  const [searchTerm, setSearchTerm] = useState('');
  const [roleFilter, setRoleFilter] = useState<UserRoleType | 'all'>('all');
  const [statusFilter, setStatusFilter] = useState<UserStatusType | 'all'>('all');
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [editingUser, setEditingUser] = useState<User | null>(null);
  const [deletingUser, setDeletingUser] = useState<User | null>(null);
  const [showPassword, setShowPassword] = useState(false);

  // Queries / Mutations
  const {
    data: users = [],
    isLoading,
    error,
  } = useUsers({
    search: searchTerm || undefined,
    role: roleFilter !== 'all' ? roleFilter : undefined,
    status: statusFilter !== 'all' ? statusFilter : undefined,
  });
  const createUserMutation = useCreateUser();
  const updateUserMutation = useUpdateUser();
  const updateStatusMutation = useUpdateUserStatus();
  const deleteUserMutation = useDeleteUser();

  // Access control
  if (!canAccessUserManagement(mockCurrentUser.role)) {
    return (
      <div className='p-6'>
        <div className='text-center py-12'>
          <Shield className='h-16 w-16 mx-auto mb-4 text-slate-300' />
          <h3 className='text-lg font-semibold text-slate-900 mb-2'>Access Denied</h3>
          <p className='text-slate-600'>You don't have permission to access user management.</p>
        </div>
      </div>
    );
  }

  // Client-side filtering (keeps UI snappy; swap to server filters when API is ready)
  const filteredUsers = useMemo(() => {
    const needle = searchTerm.trim().toLowerCase();
    return users.filter(u => {
      const bySearch =
        !needle ||
        u.name?.toLowerCase().includes(needle) ||
        u.email?.toLowerCase().includes(needle) ||
        u.phone?.toLowerCase().includes(needle);
      const byRole = roleFilter === 'all' || u.role === roleFilter;
      const byStatus = statusFilter === 'all' || u.status === statusFilter;
      return bySearch && byRole && byStatus;
    });
  }, [users, searchTerm, roleFilter, statusFilter]);

  // Forms
  const addForm = useForm<CreateUser>({
    resolver: zodResolver(CreateUserSchema),
    defaultValues: {
      name: '',
      email: '',
      role: 'Teacher',
      status: 'Active',
      phone: '',
      password: '',
      confirmPassword: '',
    },
  });

  const editForm = useForm<UpdateUser>({ resolver: zodResolver(UpdateUserSchema) });

  // Handlers
  const handleAddUser = (data: CreateUser) => {
    createUserMutation.mutate(data, {
      onSuccess: () => {
        setIsAddDialogOpen(false);
        addForm.reset();
        onDataChange?.();
      },
    });
  };

  const handleEditUser = (data: UpdateUser) => {
    if (!editingUser) {
      return;
    }
    updateUserMutation.mutate(
      { id: editingUser.id, data },
      {
        onSuccess: () => {
          setEditingUser(null);
          editForm.reset();
          onDataChange?.();
        },
      }
    );
  };

  const handleStatusToggle = (user: User, next: boolean) => {
    updateStatusMutation.mutate({ id: user.id, data: { status: next ? 'Active' : 'Inactive' } });
  };

  const handleDeleteUser = () => {
    if (!deletingUser) {
      return;
    }
    deleteUserMutation.mutate(deletingUser.id, {
      onSuccess: () => {
        setDeletingUser(null);
        onDataChange?.();
      },
    });
  };

  const openEditDialog = (user: User) => {
    setEditingUser(user);
    editForm.reset({
      name: user.name,
      email: user.email,
      role: user.role,
      status: user.status,
      phone: user.phone || '',
    });
  };

  const formatLastLogin = (d?: string) => (d ? new Date(d).toLocaleDateString() : 'Never');

  return (
    <div className='p-6'>
      {/* Header */}
      <div className='mb-6'>
        <div className='flex items-center gap-3 mb-4'>
          <div className='p-2 bg-gradient-to-r from-sky-50 to-violet-50 rounded-lg'>
            <Users className='h-6 w-6 text-sky-600' />
          </div>
          <div>
            <h2 className='text-2xl font-bold text-slate-900'>User Management</h2>
            <p className='text-slate-600'>Manage users, roles, and permissions</p>
          </div>
        </div>

        {/* Filters */}
        <div className='flex flex-col sm:flex-row gap-4 mb-6'>
          <div className='relative flex-1'>
            <Search className='absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-slate-400' />
            <Input
              placeholder='Search users...'
              value={searchTerm}
              onChange={e => setSearchTerm(e.target.value)}
              className='pl-10'
            />
          </div>
          <Select value={roleFilter} onValueChange={v => setRoleFilter(v as UserRoleType | 'all')}>
            <SelectTrigger className='w-full sm:w-48'>
              <Filter className='h-4 w-4 mr-2' />
              <SelectValue placeholder='Filter by role' />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value='all'>All Roles</SelectItem>
              <SelectItem value='Admin'>Admin</SelectItem>
              <SelectItem value='Teacher'>Teacher</SelectItem>
              <SelectItem value='Accountant'>Accountant</SelectItem>
              <SelectItem value='Clerk'>Clerk</SelectItem>
              <SelectItem value='Student'>Student</SelectItem>
            </SelectContent>
          </Select>
          <Select
            value={statusFilter}
            onValueChange={v => setStatusFilter(v as UserStatusType | 'all')}
          >
            <SelectTrigger className='w-full sm:w-48'>
              <SelectValue placeholder='Filter by status' />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value='all'>All Status</SelectItem>
              <SelectItem value='Active'>Active</SelectItem>
              <SelectItem value='Inactive'>Inactive</SelectItem>
              <SelectItem value='Pending'>Pending</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Users Table */}
      <Card>
        <CardHeader>
          <div className='flex items-center justify-between'>
            <CardTitle>Users ({filteredUsers.length})</CardTitle>
            <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
              <DialogTrigger asChild>
                <Button size='sm' className='bg-gradient-to-r from-sky-600 to-violet-600'>
                  <Plus className='h-4 w-4 mr-2' />
                  Add User
                </Button>
              </DialogTrigger>
              <DialogContent className='max-w-md'>
                <DialogHeader>
                  <DialogTitle>Add New User</DialogTitle>
                </DialogHeader>
                <form onSubmit={addForm.handleSubmit(handleAddUser)} className='space-y-4'>
                  <div className='space-y-2'>
                    <Label>Name *</Label>
                    <Input {...addForm.register('name')} placeholder='Enter full name' />
                    {addForm.formState.errors.name && (
                      <p className='text-sm text-red-600'>
                        {addForm.formState.errors.name.message}
                      </p>
                    )}
                  </div>
                  <div className='space-y-2'>
                    <Label>Email *</Label>
                    <Input
                      {...addForm.register('email')}
                      type='email'
                      placeholder='Enter email address'
                    />
                    {addForm.formState.errors.email && (
                      <p className='text-sm text-red-600'>
                        {addForm.formState.errors.email.message}
                      </p>
                    )}
                  </div>
                  <div className='space-y-2'>
                    <Label>Role *</Label>
                    <Select
                      value={addForm.watch('role')}
                      onValueChange={v => addForm.setValue('role', v as UserRoleType)}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value='Admin'>Admin</SelectItem>
                        <SelectItem value='Teacher'>Teacher</SelectItem>
                        <SelectItem value='Accountant'>Accountant</SelectItem>
                        <SelectItem value='Clerk'>Clerk</SelectItem>
                        <SelectItem value='Student'>Student</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className='space-y-2'>
                    <Label>Phone</Label>
                    <Input {...addForm.register('phone')} placeholder='Enter phone number' />
                  </div>
                  <div className='space-y-2'>
                    <Label>Password *</Label>
                    <div className='relative'>
                      <Input
                        {...addForm.register('password')}
                        type={showPassword ? 'text' : 'password'}
                        placeholder='Enter password'
                      />
                      <Button
                        type='button'
                        variant='ghost'
                        size='sm'
                        className='absolute right-0 top-0 h-full px-3'
                        onClick={() => setShowPassword(v => !v)}
                      >
                        {showPassword ? (
                          <EyeOff className='h-4 w-4' />
                        ) : (
                          <Eye className='h-4 w-4' />
                        )}
                      </Button>
                    </div>
                    {addForm.formState.errors.password && (
                      <p className='text-sm text-red-600'>
                        {addForm.formState.errors.password.message}
                      </p>
                    )}
                  </div>
                  <div className='space-y-2'>
                    <Label>Confirm Password *</Label>
                    <Input
                      {...addForm.register('confirmPassword')}
                      type={showPassword ? 'text' : 'password'}
                      placeholder='Confirm password'
                    />
                    {addForm.formState.errors.confirmPassword && (
                      <p className='text-sm text-red-600'>
                        {addForm.formState.errors.confirmPassword.message}
                      </p>
                    )}
                  </div>
                  <div className='flex justify-end gap-2 pt-4'>
                    <Button
                      type='button'
                      variant='outline'
                      onClick={() => setIsAddDialogOpen(false)}
                    >
                      Cancel
                    </Button>
                    <Button type='submit' disabled={createUserMutation.isPending}>
                      {createUserMutation.isPending ? 'Adding...' : 'Add User'}
                    </Button>
                  </div>
                </form>
              </DialogContent>
            </Dialog>
          </div>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className='text-center py-12'>
              <div className='animate-spin rounded-full h-8 w-8 border-b-2 border-sky-600 mx-auto mb-4' />
              <p className='text-slate-600'>Loading users...</p>
            </div>
          ) : error ? (
            <div className='text-center py-12 text-red-600'>
              <p>Error loading users</p>
              <p className='text-sm text-slate-600 mt-1'>Please try again later</p>
            </div>
          ) : filteredUsers.length === 0 ? (
            <div className='text-center py-12 text-slate-500'>
              <Users className='h-12 w-12 mx-auto mb-4 text-slate-300' />
              <p>No users found</p>
              <p className='text-sm'>
                {searchTerm || roleFilter !== 'all' || statusFilter !== 'all'
                  ? 'Try adjusting your filters'
                  : 'Add your first user to get started'}
              </p>
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>User</TableHead>
                  <TableHead>Role</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Last Login</TableHead>
                  <TableHead className='w-32'>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredUsers.map(user => (
                  <TableRow key={user.id}>
                    <TableCell>
                      <div>
                        <p className='font-medium'>{user.name}</p>
                        <p className='text-sm text-slate-600'>{user.email}</p>
                        {user.phone && <p className='text-sm text-slate-500'>{user.phone}</p>}
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge className={getRoleColor(user.role)}>{user.role}</Badge>
                    </TableCell>
                    <TableCell>
                      <div className='flex items-center gap-2'>
                        <Badge className={getStatusColor(user.status)}>{user.status}</Badge>
                        <Switch
                          checked={user.status === 'Active'}
                          onCheckedChange={checked => handleStatusToggle(user, checked)}
                        />
                      </div>
                    </TableCell>
                    <TableCell className='text-sm text-slate-600'>
                      {formatLastLogin(user.lastLogin)}
                    </TableCell>
                    <TableCell>
                      <div className='flex items-center gap-1'>
                        <Button variant='ghost' size='sm' onClick={() => openEditDialog(user)}>
                          <Edit2 className='h-4 w-4' />
                        </Button>
                        <Button
                          variant='ghost'
                          size='sm'
                          onClick={() => setDeletingUser(user)}
                          disabled={user.id === mockCurrentUser.id}
                        >
                          <Trash2 className='h-4 w-4' />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>

      {/* Edit User Dialog */}
      <Dialog open={!!editingUser} onOpenChange={() => setEditingUser(null)}>
        <DialogContent className='max-w-md'>
          <DialogHeader>
            <DialogTitle>Edit User</DialogTitle>
          </DialogHeader>
          <form onSubmit={editForm.handleSubmit(handleEditUser)} className='space-y-4'>
            <div className='space-y-2'>
              <Label>Name *</Label>
              <Input {...editForm.register('name')} placeholder='Enter full name' />
              {editForm.formState.errors.name && (
                <p className='text-sm text-red-600'>{editForm.formState.errors.name.message}</p>
              )}
            </div>
            <div className='space-y-2'>
              <Label>Email *</Label>
              <Input
                {...editForm.register('email')}
                type='email'
                placeholder='Enter email address'
              />
              {editForm.formState.errors.email && (
                <p className='text-sm text-red-600'>{editForm.formState.errors.email.message}</p>
              )}
            </div>
            <div className='space-y-2'>
              <Label>Role *</Label>
              <Select
                value={editForm.watch('role')}
                onValueChange={v => editForm.setValue('role', v as UserRoleType)}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value='Admin'>Admin</SelectItem>
                  <SelectItem value='Teacher'>Teacher</SelectItem>
                  <SelectItem value='Accountant'>Accountant</SelectItem>
                  <SelectItem value='Clerk'>Clerk</SelectItem>
                  <SelectItem value='Student'>Student</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className='space-y-2'>
              <Label>Phone</Label>
              <Input {...editForm.register('phone')} placeholder='Enter phone number' />
            </div>
            <div className='space-y-2'>
              <Label>New Password (optional)</Label>
              <div className='relative'>
                <Input
                  {...editForm.register('password')}
                  type={showPassword ? 'text' : 'password'}
                  placeholder='Leave blank to keep current password'
                />
                <Button
                  type='button'
                  variant='ghost'
                  size='sm'
                  className='absolute right-0 top-0 h-full px-3'
                  onClick={() => setShowPassword(v => !v)}
                >
                  {showPassword ? <EyeOff className='h-4 w-4' /> : <Eye className='h-4 w-4' />}
                </Button>
              </div>
              {editForm.formState.errors.password && (
                <p className='text-sm text-red-600'>{editForm.formState.errors.password.message}</p>
              )}
            </div>
            {editForm.watch('password') && (
              <div className='space-y-2'>
                <Label>Confirm New Password</Label>
                <Input
                  {...editForm.register('confirmPassword')}
                  type={showPassword ? 'text' : 'password'}
                  placeholder='Confirm new password'
                />
                {editForm.formState.errors.confirmPassword && (
                  <p className='text-sm text-red-600'>
                    {editForm.formState.errors.confirmPassword.message}
                  </p>
                )}
              </div>
            )}
            <div className='flex justify-end gap-2 pt-4'>
              <Button type='button' variant='outline' onClick={() => setEditingUser(null)}>
                Cancel
              </Button>
              <Button type='submit'>Save Changes</Button>
            </div>
          </form>
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={!!deletingUser} onOpenChange={() => setDeletingUser(null)}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete User</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete "{deletingUser?.name}"? This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={handleDeleteUser} className='bg-red-600 hover:bg-red-700'>
              Delete User
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}
