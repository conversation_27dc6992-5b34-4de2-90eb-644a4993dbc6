/**
 * Class Service - Production-Grade FastAPI Integration
 *
 * Handles all class-related API calls with:
 * - Real FastAPI backend integration (/api/v1/classes)
 * - JWT Bearer authentication
 * - Proper error handling and data transformation
 * - Dummy data fallback for development
 * - CRUD operations with optimistic updates
 * - Filtering, pagination, and statistics
 */

import {
  createMockClass,
  deleteMockClass,
  filterMockClasses,
  getMockClassById,
  mockClasses,
  paginateMockClasses,
  updateMockClass,
} from '@/lib/mockClasses';
import type { Class, ClassCreate, ClassUpdate } from '@/schemas/zodSchemas';
import type { PaginatedResponse } from '@/types';

import { api as apiClient } from '../apiClient';

// Configuration - Use real API by default, fallback to dummy data in development
const USE_DUMMY_DATA = process.env.NEXT_PUBLIC_USE_DUMMY_DATA === 'true' || false;

// FastAPI endpoints - Normalized for FastAPI routing conventions
const ENDPOINTS = {
  classes: '/classes/', // Collection endpoint - keep trailing slash
  class: (id: string) => `/classes/${id}`, // Individual resource - no trailing slash
  classStats: '/classes/stats', // Sub-route - no trailing slash
  classSearch: '/classes/search', // Sub-route - no trailing slash
  classBulk: '/classes/bulk', // Sub-route - no trailing slash
} as const;

// Types for API parameters
export interface ClassFilters {
  search?: string;
  grade?: string;
  status?: 'ACTIVE' | 'INACTIVE';
  teacher_id?: string;
  academic_year?: string;
  page?: number;
  pageSize?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export interface ClassStats {
  total: number;
  active: number;
  inactive: number;
  totalStudents: number;
  averageCapacity: number;
}

// Class Service Functions
export const classService = {
  // Get all classes with filtering and pagination
  async getClasses(filters: ClassFilters = {}): Promise<PaginatedResponse<Class>> {
    if (USE_DUMMY_DATA) {
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 800));

      // Apply filters to mock data
      const filterParams: {
        search?: string;
        grade?: string;
        status?: string;
        teacher_id?: string;
        academic_year?: string;
      } = {};

      if (filters.search) {
        filterParams.search = filters.search;
      }
      if (filters.grade) {
        filterParams.grade = filters.grade;
      }
      if (filters.status) {
        filterParams.status = filters.status;
      }
      if (filters.teacher_id) {
        filterParams.teacher_id = filters.teacher_id;
      }
      if (filters.academic_year) {
        filterParams.academic_year = filters.academic_year;
      }

      const filteredClasses = filterMockClasses(filterParams);

      // Apply pagination
      const paginatedClasses = paginateMockClasses(
        filteredClasses,
        filters.page || 1,
        filters.pageSize || 12
      );

      return paginatedClasses;
    }

    // Real API call with proper error handling
    try {
      console.log(`🔗 ClassService.getClasses calling: ${ENDPOINTS.classes}`);
      const response = await apiClient.get<any>(ENDPOINTS.classes, {
        params: filters,
      });

      // Transform backend response to frontend format if needed
      const transformedData = {
        data: response.data.data || response.data,
        total: response.data.total || response.data.length || 0,
        page: response.data.page || filters.page || 1,
        pageSize: response.data.pageSize || filters.pageSize || 12,
        totalPages:
          response.data.totalPages ||
          Math.ceil((response.data.total || 0) / (filters.pageSize || 12)),
        hasNext:
          (response.data.page || filters.page || 1) <
          (response.data.totalPages ||
            Math.ceil((response.data.total || 0) / (filters.pageSize || 12))),
        hasPrev: (response.data.page || filters.page || 1) > 1,
      };

      console.log(`✅ ClassService.getClasses success:`, transformedData);
      return transformedData;
    } catch (error: any) {
      console.error(`❌ ClassService.getClasses error:`, error);
      throw new Error(error.response?.data?.detail || error.message || 'Failed to fetch classes');
    }
  },

  // Get single class by ID
  async getClass(id: string): Promise<Class> {
    if (USE_DUMMY_DATA) {
      await new Promise(resolve => setTimeout(resolve, 500));

      const classItem = getMockClassById(id);
      if (!classItem) {
        throw new Error(`Class with ID ${id} not found`);
      }

      return classItem;
    }

    try {
      console.log(`🔗 ClassService.getClass calling: ${ENDPOINTS.class(id)}`);
      const response = await apiClient.get<any>(ENDPOINTS.class(id));

      // Transform backend response to frontend format if needed
      const classItem = response.data;
      console.log(`✅ ClassService.getClass success:`, classItem);
      return classItem;
    } catch (error: any) {
      console.error(`❌ ClassService.getClass error:`, error);
      if (error.response?.status === 404) {
        throw new Error(`Class with ID ${id} not found`);
      }
      throw new Error(error.response?.data?.detail || error.message || 'Failed to fetch class');
    }
  },

  // Create new class
  async createClass(classData: ClassCreate): Promise<Class> {
    if (USE_DUMMY_DATA) {
      await new Promise(resolve => setTimeout(resolve, 1000));

      const newClass = createMockClass(classData);
      return newClass;
    }

    try {
      console.log(`🔗 ClassService.createClass calling: ${ENDPOINTS.classes}`);
      console.log(`📤 ClassService.createClass data:`, classData);

      const response = await apiClient.post<any>(ENDPOINTS.classes, classData);
      const newClass = response.data;

      console.log(`✅ ClassService.createClass success:`, newClass);
      return newClass;
    } catch (error: any) {
      console.error(`❌ ClassService.createClass error:`, error);
      throw new Error(error.response?.data?.detail || error.message || 'Failed to create class');
    }
  },

  // Update existing class
  async updateClass(id: string, classData: ClassUpdate): Promise<Class> {
    if (USE_DUMMY_DATA) {
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Filter out undefined values to satisfy exactOptionalPropertyTypes
      const filteredData: Partial<Class> = {};
      Object.entries(classData).forEach(([key, value]) => {
        if (value !== undefined) {
          (filteredData as any)[key] = value;
        }
      });

      const updatedClass = updateMockClass(id, filteredData);
      if (!updatedClass) {
        throw new Error(`Class with ID ${id} not found`);
      }

      return updatedClass;
    }

    try {
      console.log(`🔗 ClassService.updateClass calling: ${ENDPOINTS.class(id)}`);
      console.log(`📤 ClassService.updateClass data:`, classData);

      const response = await apiClient.put<any>(ENDPOINTS.class(id), classData);
      const updatedClass = response.data;

      console.log(`✅ ClassService.updateClass success:`, updatedClass);
      return updatedClass;
    } catch (error: any) {
      console.error(`❌ ClassService.updateClass error:`, error);
      if (error.response?.status === 404) {
        throw new Error(`Class with ID ${id} not found`);
      }
      throw new Error(error.response?.data?.detail || error.message || 'Failed to update class');
    }
  },

  // Delete class
  async deleteClass(id: string): Promise<{ success: boolean }> {
    if (USE_DUMMY_DATA) {
      await new Promise(resolve => setTimeout(resolve, 800));

      const deleteSuccess = deleteMockClass(id);
      if (!deleteSuccess) {
        throw new Error(`Class with ID ${id} not found`);
      }

      return { success: true };
    }

    try {
      console.log(`🔗 ClassService.deleteClass calling: ${ENDPOINTS.class(id)}`);

      await apiClient.delete(ENDPOINTS.class(id));

      console.log(`✅ ClassService.deleteClass success for ID: ${id}`);
      return { success: true };
    } catch (error: any) {
      console.error(`❌ ClassService.deleteClass error:`, error);
      if (error.response?.status === 404) {
        throw new Error(`Class with ID ${id} not found`);
      }
      throw new Error(error.response?.data?.detail || error.message || 'Failed to delete class');
    }
  },

  // Get class statistics
  async getClassStats(): Promise<ClassStats> {
    if (USE_DUMMY_DATA) {
      await new Promise(resolve => setTimeout(resolve, 300));

      const allClasses = mockClasses;
      const activeClasses = allClasses.filter(classItem => classItem.status === 'ACTIVE');
      const totalStudents = allClasses.reduce(
        (sum, classItem) => sum + (classItem.enrolled || 0),
        0
      );
      const averageCapacity =
        allClasses.length > 0
          ? Math.round(
              allClasses.reduce((sum, classItem) => sum + classItem.capacity, 0) / allClasses.length
            )
          : 0;

      return {
        total: allClasses.length,
        active: activeClasses.length,
        inactive: allClasses.length - activeClasses.length,
        totalStudents,
        averageCapacity,
      };
    }

    try {
      console.log(`🔗 ClassService.getClassStats calling: ${ENDPOINTS.classStats}`);

      const response = await apiClient.get<any>(ENDPOINTS.classStats);
      const stats = response.data;

      console.log(`✅ ClassService.getClassStats success:`, stats);
      return stats;
    } catch (error: any) {
      console.error(`❌ ClassService.getClassStats error:`, error);
      throw new Error(
        error.response?.data?.detail || error.message || 'Failed to fetch class statistics'
      );
    }
  },

  // Bulk operations
  async bulkUpdateClasses(updates: Array<{ id: string; data: ClassUpdate }>): Promise<Class[]> {
    if (USE_DUMMY_DATA) {
      await new Promise(resolve => setTimeout(resolve, 2000));

      const updatedClasses = updates.map(({ id, data: classUpdateData }) => {
        // Filter out undefined values to satisfy exactOptionalPropertyTypes
        const filteredData: Partial<Class> = {};
        Object.entries(classUpdateData).forEach(([key, value]) => {
          if (value !== undefined) {
            (filteredData as any)[key] = value;
          }
        });

        const updatedClass = updateMockClass(id, filteredData);
        if (!updatedClass) {
          throw new Error(`Class with ID ${id} not found`);
        }
        return updatedClass;
      });

      return updatedClasses;
    }

    const bulkUpdateResponse = await apiClient.put<Class[]>('/classes/bulk', { updates });
    return bulkUpdateResponse.data;
  },

  async bulkDeleteClasses(ids: string[]): Promise<{ success: boolean; deleted: number }> {
    if (USE_DUMMY_DATA) {
      await new Promise(resolve => setTimeout(resolve, 1500));

      let deletedCount = 0;
      ids.forEach(classId => {
        if (deleteMockClass(classId)) {
          deletedCount++;
        }
      });

      return { success: true, deleted: deletedCount };
    }

    const bulkDeleteResponse = await apiClient.delete<{ success: boolean; deleted: number }>(
      '/classes/bulk',
      {
        data: { ids },
      }
    );
    return bulkDeleteResponse.data;
  },

  // Search classes
  async searchClasses(query: string, limit = 10): Promise<Class[]> {
    if (USE_DUMMY_DATA) {
      await new Promise(resolve => setTimeout(resolve, 300));

      const filteredClasses = filterMockClasses(query ? { search: query } : {});
      return filteredClasses.slice(0, limit);
    }

    const searchResponse = await apiClient.get<Class[]>(ENDPOINTS.classSearch, {
      params: { q: query, limit },
    });
    return searchResponse.data;
  },

  // Get classes by teacher
  async getClassesByTeacher(teacherId: string): Promise<Class[]> {
    if (USE_DUMMY_DATA) {
      await new Promise(resolve => setTimeout(resolve, 400));

      return filterMockClasses(teacherId ? { teacher_id: teacherId } : {});
    }

    const teacherResponse = await apiClient.get<Class[]>('/classes/by-teacher', {
      params: { teacher_id: teacherId },
    });
    return teacherResponse.data;
  },
};

// Export individual functions for convenience
export const {
  getClasses,
  getClass,
  createClass,
  updateClass,
  deleteClass,
  getClassStats,
  bulkUpdateClasses,
  bulkDeleteClasses,
  searchClasses,
  getClassesByTeacher,
} = classService;
