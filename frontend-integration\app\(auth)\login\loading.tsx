/**
 * Login Page Loading Component
 * 
 * Shows loading state while the login page is being loaded
 */
export default function LoginLoading() {
  return (
    <div className="space-y-6">
      {/* Header skeleton */}
      <div className="text-center">
        <div className="h-8 bg-gray-200 rounded animate-pulse mb-2"></div>
        <div className="h-4 bg-gray-200 rounded animate-pulse w-3/4 mx-auto"></div>
      </div>

      {/* Form skeleton */}
      <div className="space-y-6">
        {/* Email field */}
        <div>
          <div className="h-4 bg-gray-200 rounded animate-pulse mb-2 w-1/4"></div>
          <div className="h-10 bg-gray-200 rounded animate-pulse"></div>
        </div>

        {/* Password field */}
        <div>
          <div className="h-4 bg-gray-200 rounded animate-pulse mb-2 w-1/4"></div>
          <div className="h-10 bg-gray-200 rounded animate-pulse"></div>
        </div>

        {/* Remember me and forgot password */}
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <div className="h-4 w-4 bg-gray-200 rounded animate-pulse mr-2"></div>
            <div className="h-4 bg-gray-200 rounded animate-pulse w-20"></div>
          </div>
          <div className="h-4 bg-gray-200 rounded animate-pulse w-24"></div>
        </div>

        {/* Submit button */}
        <div className="h-10 bg-gray-200 rounded animate-pulse"></div>
      </div>
    </div>
  );
}
