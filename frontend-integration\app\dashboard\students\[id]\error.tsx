'use client';

/**
 * Student Detail Error Boundary
 *
 * Handles errors that occur when loading student details
 */

import { <PERSON><PERSON><PERSON><PERSON><PERSON>, ArrowLeft, RefreshCw, Users } from 'lucide-react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { useEffect } from 'react';

import { Alert, AlertDescription } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

interface StudentDetailErrorProps {
  error: Error & { digest?: string };
  reset: () => void;
}

export default function StudentDetailError({ error, reset }: StudentDetailErrorProps) {
  const router = useRouter();

  useEffect(() => {
    // Log the error to an error reporting service
    console.error('Student detail error:', error);
  }, [error]);

  const isNotFound = error.message.includes('404') || error.message.includes('not found');

  return (
    <div className='container mx-auto p-4 sm:p-6 lg:p-8'>
      <Card className='max-w-2xl mx-auto border-0 shadow-xl'>
        <CardHeader className='text-center space-y-4'>
          <div className='mx-auto w-16 h-16 bg-red-100 rounded-2xl flex items-center justify-center'>
            <AlertTriangle className='w-8 h-8 text-red-600' />
          </div>
          <div>
            <CardTitle className='text-2xl font-bold text-gray-900 flex items-center justify-center gap-2'>
              <Users className='w-6 h-6' />
              {isNotFound ? 'Student Not Found' : 'Student Detail Error'}
            </CardTitle>
          </div>
        </CardHeader>

        <CardContent className='space-y-6'>
          <Alert variant='destructive'>
            <AlertTriangle className='h-4 w-4' />
            <AlertDescription className='text-sm'>
              {isNotFound
                ? 'The student you are looking for could not be found. It may have been deleted or the ID is incorrect.'
                : error.message ||
                  'An unexpected error occurred while loading the student details. This could be due to a network issue or server problem.'}
            </AlertDescription>
          </Alert>

          {/* Error Details for Development */}
          {process.env.NODE_ENV === 'development' && (
            <Alert>
              <AlertDescription className='text-xs font-mono bg-gray-50 p-2 rounded'>
                <strong>Error Details:</strong>
                <br />
                {error.stack || error.message}
                {error.digest && (
                  <>
                    <br />
                    <strong>Error ID:</strong> {error.digest}
                  </>
                )}
              </AlertDescription>
            </Alert>
          )}

          <div className='space-y-3'>
            {!isNotFound && (
              <Button
                onClick={reset}
                className='w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700'
              >
                <RefreshCw className='w-4 h-4 mr-2' />
                Try Again
              </Button>
            )}

            <div className='grid grid-cols-1 sm:grid-cols-2 gap-3'>
              <Button variant='outline' className='w-full' onClick={() => router.back()}>
                <ArrowLeft className='w-4 h-4 mr-2' />
                Go Back
              </Button>

              <Button variant='outline' className='w-full' asChild>
                <Link href='/dashboard/students'>
                  <Users className='w-4 h-4 mr-2' />
                  All Students
                </Link>
              </Button>
            </div>
          </div>

          <div className='text-center text-sm text-gray-600 space-y-2'>
            <p>
              {isNotFound
                ? 'Try searching for the student in the students list or check the URL.'
                : 'If the problem persists, please contact support or try refreshing the page.'}
            </p>

            <div className='flex items-center justify-center space-x-4 text-xs text-gray-500'>
              <span>Error occurred at: {new Date().toLocaleString()}</span>
              {error.digest && <span>ID: {error.digest.slice(0, 8)}</span>}
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
