'use client';

import { Button } from '@/components/ui/button';

export default function ErrorPage({ error, reset }: { error: Error; reset: () => void }) {
  return (
    <div className='container mx-auto p-6 max-w-md text-center'>
      <h2 className='text-xl font-semibold mb-4'>Something went wrong</h2>
      <p className='text-muted-foreground mb-6'>
        {error.message || 'Failed to load the fee record.'}
      </p>
      <Button onClick={() => reset()}>Try Again</Button>
    </div>
  );
}
