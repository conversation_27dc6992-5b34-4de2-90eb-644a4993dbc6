/**
 * Global TypeScript Type Definitions for School Management System
 *
 * Clean, focused type definitions that match backend API schemas
 * Designed for easy validation with Zod schemas
 */

// Base types
export type BaseEntity = {
  id: string;
  created_at?: string;
  updated_at?: string;
};

// API Response types
export type ApiResponse<T> = {
  data: T;
  message?: string;
  success: boolean;
};

export type PaginatedResponse<T> = {
  data: T[];
  total: number;
  page: number;
  size: number;
};

// User and Authentication types
export type UserRole = 'SUPER_ADMIN' | 'ADMIN' | 'STAFF' | 'TEACHER' | 'STUDENT' | 'PARENT';

export type User = {
  id: string;
  email: string;
  first_name: string;
  last_name: string;
  role: UserRole;
  is_active: boolean;
  profile_picture?: string;
};

export type LoginCredentials = {
  email: string;
  password: string;
};

export type LoginResponse = {
  access_token: string;
  user: User;
};

// Teacher types
export type Teacher = {
  id: string;
  name: string;
  subject: string;
  email?: string | undefined;
  department?: string | undefined;
  phone?: string | undefined;
  status?: 'ACTIVE' | 'INACTIVE' | undefined;
  hire_date?: string | undefined;
  created_at?: string | undefined;
  updated_at?: string | undefined;
};

// Student types
export type Student = {
  id: string;
  student_id: string;
  first_name: string;
  last_name: string;
  email: string;
  phone?: string;
  class_name?: string;
  grade_level?: string;
  status: 'ACTIVE' | 'INACTIVE' | 'GRADUATED';
  enrollment_date?: string;
  created_at?: string;
  updated_at?: string;
};

// Class types
export type Class = {
  id: string;
  name: string;
  grade_level: string;
  capacity: number;
  current_enrollment?: number;
  teacher_name?: string;
  room_number?: string;
  is_active?: boolean;
};

// Subject types
export type Subject = {
  id: string;
  name: string;
  code: string;
  category?: string;
  credits?: number;
  description?: string;
  is_active?: boolean;
};

// Attendance types
export type Attendance = {
  id: string;
  student_id: string;
  student_name?: string;
  class_id: string;
  class_name?: string;
  date: string;
  status: 'PRESENT' | 'ABSENT' | 'LATE' | 'EXCUSED';
  remarks?: string;
};

// Exam types
export type Exam = {
  id: string;
  title: string;
  subject_id: string;
  subject_name?: string;
  class_id: string;
  class_name?: string;
  exam_date: string;
  total_marks: number;
  passing_marks: number;
  status?: 'SCHEDULED' | 'COMPLETED' | 'CANCELLED';
};

// Fee types
export type Fee = {
  id: string;
  student_id: string;
  student_name?: string;
  fee_type: string;
  amount: number;
  due_date: string;
  status: 'PENDING' | 'PAID' | 'OVERDUE' | 'CANCELLED';
  paid_amount?: number;
  paid_date?: string;
};

// Parent types
export type Parent = {
  id: string;
  first_name: string;
  last_name: string;
  email: string;
  phone: string;
  relationship: 'FATHER' | 'MOTHER' | 'GUARDIAN';
  occupation?: string;
  address?: string;
};

// Announcement types
export type Announcement = {
  id: string;
  title: string;
  content: string;
  author_name?: string;
  priority: 'LOW' | 'MEDIUM' | 'HIGH' | 'URGENT';
  target_audience: string[];
  publish_date: string;
  is_published: boolean;
};

// Event types
export type Event = {
  id: string;
  title: string;
  description?: string;
  event_date: string;
  start_time: string;
  end_time: string;
  location?: string;
  organizer_name?: string;
  is_public: boolean;
};

// Grade/Result types
export type Grade = {
  id: string;
  student_id: string;
  student_name?: string;
  exam_id: string;
  exam_title?: string;
  marks_obtained: number;
  total_marks: number;
  grade_letter: string;
  percentage: number;
};

// Dashboard and Analytics types
export type DashboardStats = {
  total_students: number;
  total_teachers: number;
  total_classes: number;
  total_subjects: number;
  attendance_rate: number;
  fee_collection_rate: number;
};

export type AttendanceReport = {
  date: string;
  present: number;
  absent: number;
  late: number;
  total: number;
  percentage: number;
};

export type FeeReport = {
  month: string;
  collected: number;
  pending: number;
  total: number;
  collection_rate: number;
};

// Form and UI helper types
export type FormField = {
  name: string;
  label: string;
  type: 'text' | 'email' | 'password' | 'number' | 'date' | 'select' | 'textarea';
  required?: boolean;
  placeholder?: string;
  options?: { value: string; label: string }[];
};

export type TableColumn<T = any> = {
  key: keyof T;
  label: string;
  sortable?: boolean;
  render?: (value: any, row: T) => React.ReactNode;
};

export type FilterOption = {
  key: string;
  label: string;
  type: 'select' | 'date' | 'text';
  options?: { value: string; label: string }[];
};
