'use client';

import { zodResolver } from '@hookform/resolvers/zod';
import { Loader2, Save, User, X } from 'lucide-react';
import { useForm } from 'react-hook-form';

// Schema imports - demonstrating strict type usage
import {
  StudentCreateSchema,
  StudentUpdateSchema,
  type StudentCreate,
  type StudentUpdate,
} from '@/schemas/zodSchemas';

// UI Components
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  FormSection,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';

// Types for the component props - strict typing
interface StudentFormProps {
  mode: 'create' | 'edit';
  initialData?: StudentUpdate;
  onSubmit: (data: StudentCreate | StudentUpdate) => Promise<void>;
  onCancel: () => void;
  isLoading?: boolean;
  availableClasses?: Array<{ id: string; name: string }>;
  availableGrades?: Array<{ id: string; name: string }>;
  availableParents?: Array<{ id: string; name: string }>;
}

// Default options - these should ideally come from API
const DEFAULT_CLASSES = [
  { id: '1', name: 'Class A' },
  { id: '2', name: 'Class B' },
  { id: '3', name: 'Class C' },
  { id: '4', name: 'Class D' },
] as const;

const DEFAULT_GRADES = [
  { id: '1', name: 'Grade 1' },
  { id: '2', name: 'Grade 2' },
  { id: '3', name: 'Grade 3' },
  { id: '4', name: 'Grade 4' },
  { id: '5', name: 'Grade 5' },
  { id: '6', name: 'Grade 6' },
  { id: '7', name: 'Grade 7' },
  { id: '8', name: 'Grade 8' },
  { id: '9', name: 'Grade 9' },
  { id: '10', name: 'Grade 10' },
  { id: '11', name: 'Grade 11' },
  { id: '12', name: 'Grade 12' },
] as const;

const GENDER_OPTIONS = [
  { value: 'male', label: 'Male' },
  { value: 'female', label: 'Female' },
  { value: 'other', label: 'Other' },
] as const;

/**
 * StudentForm Component
 *
 * Demonstrates strict type usage with Zod + React Hook Form:
 * - Uses zodResolver for comprehensive validation
 * - Types inferred from Zod schemas
 * - Complex validation rules (email, phone, student ID)
 * - Real-time validation with proper error display
 * - Type-safe form submission
 */
export function StudentForm({
  mode,
  initialData,
  onSubmit,
  onCancel,
  isLoading = false,
  availableClasses = DEFAULT_CLASSES,
  availableGrades = DEFAULT_GRADES,
  availableParents = [],
}: StudentFormProps) {
  // Schema selection based on mode - demonstrating conditional schema usage
  const schema = mode === 'create' ? StudentCreateSchema : StudentUpdateSchema;

  // Form initialization with strict typing and backend-aligned defaults
  const form = useForm<StudentCreate | StudentUpdate>({
    resolver: zodResolver(schema),
    defaultValues:
      mode === 'create'
        ? {
            reg_no: '',
            first_name: '',
            last_name: '',
            email: '',
            gender: 'other' as const,
            dob: '',
            class_id: '',
            section_id: '',
            guardian_name: '',
            guardian_phone: '',
            address: '',
            password: '',
            parent_id: '',
          }
        : {
            reg_no: initialData?.reg_no || '',
            first_name: initialData?.first_name || '',
            last_name: initialData?.last_name || '',
            email: initialData?.email || '',
            gender: initialData?.gender || ('other' as const),
            dob: initialData?.dob || '',
            class_id: initialData?.class_id || '',
            section_id: initialData?.section_id || '',
            guardian_name: initialData?.guardian_name || '',
            guardian_phone: initialData?.guardian_phone || '',
            address: initialData?.address || '',
            password: initialData?.password || '',
            parent_id: initialData?.parent_id || '',
          },
    mode: 'onChange', // Real-time validation
  });

  // Type-safe form submission with validation
  const handleSubmit = async (data: StudentCreate | StudentUpdate) => {
    try {
      // Transform empty strings to null for proper API handling
      const transformedData = {
        ...data,
        class_id: data.class_id === '' ? null : data.class_id,
        section_id: data.section_id === '' ? null : data.section_id,
        parent_id: data.parent_id === '' ? null : data.parent_id,
        email: data.email === '' ? null : data.email,
        guardian_phone: data.guardian_phone === '' ? null : data.guardian_phone,
        address: data.address === '' ? null : data.address,
      };

      // Validate data against schema before submission
      const validatedData = schema.parse(transformedData);
      await onSubmit(validatedData);
    } catch (error) {
      console.error('Form validation error:', error);
      // Handle validation errors if needed
    }
  };

  return (
    <Card className='w-full max-w-3xl mx-auto'>
      <CardHeader>
        <CardTitle className='flex items-center gap-2'>
          <User className='w-5 h-5' />
          {mode === 'create' ? 'Add New Student' : 'Edit Student'}
        </CardTitle>
        <CardDescription>
          {mode === 'create'
            ? 'Fill in the details to register a new student in the system.'
            : 'Update the student information below.'}
        </CardDescription>
      </CardHeader>

      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(handleSubmit)} className='space-y-6'>
            {/* Personal Information Section */}
            <FormSection
              title='Personal Information'
              description='Basic student details and contact information'
            >
              <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
                {/* Registration Number Field - Required, unique identifier */}
                <FormField
                  control={form.control}
                  name='reg_no'
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Registration Number *</FormLabel>
                      <FormControl>
                        <Input placeholder='e.g., ADM2024001' {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Gender Field - Required enum validation */}
                <FormField
                  control={form.control}
                  name='gender'
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Gender *</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder='Select gender' />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {GENDER_OPTIONS.map(option => (
                            <SelectItem key={option.value} value={option.value}>
                              {option.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* First Name Field - Required */}
                <FormField
                  control={form.control}
                  name='first_name'
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>First Name *</FormLabel>
                      <FormControl>
                        <Input placeholder='Enter first name' {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Last Name Field - Required */}
                <FormField
                  control={form.control}
                  name='last_name'
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Last Name *</FormLabel>
                      <FormControl>
                        <Input placeholder='Enter last name' {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Date of Birth Field - Optional with validation */}
                <FormField
                  control={form.control}
                  name='dob'
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Date of Birth</FormLabel>
                      <FormControl>
                        <Input type='date' {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Email Field - Optional with validation */}
                <FormField
                  control={form.control}
                  name='email'
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Email Address</FormLabel>
                      <FormControl>
                        <Input type='email' placeholder='<EMAIL>' {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Guardian Name Field */}
                <FormField
                  control={form.control}
                  name='guardian_name'
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Guardian Name</FormLabel>
                      <FormControl>
                        <Input placeholder='Enter guardian name' {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Guardian Phone Field */}
                <FormField
                  control={form.control}
                  name='guardian_phone'
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Guardian Phone</FormLabel>
                      <FormControl>
                        <Input type='tel' placeholder='+****************' {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Address Field */}
                <FormField
                  control={form.control}
                  name='address'
                  render={({ field }) => (
                    <FormItem className='md:col-span-2'>
                      <FormLabel>Address</FormLabel>
                      <FormControl>
                        <Input placeholder='Enter student address' {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </FormSection>

            {/* Academic Information Section */}
            <FormSection
              title='Academic Information'
              description='Class assignment and enrollment details'
            >
              <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
                {/* Class ID Field - Required */}
                <FormField
                  control={form.control}
                  name='class_id'
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Class *</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder='Select class' />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {availableClasses.map(cls => (
                            <SelectItem key={cls.id} value={cls.id}>
                              {cls.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Section ID (Grade ID) Field - Required */}
                <FormField
                  control={form.control}
                  name='section_id'
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Grade/Section *</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder='Select grade/section' />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {availableGrades.map(grade => (
                            <SelectItem key={grade.id} value={grade.id}>
                              {grade.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </FormSection>

            {/* Authentication & Parent Information Section - Only for create mode */}
            {mode === 'create' && (
              <FormSection
                title='Authentication & Parent Information'
                description='Login credentials and parent assignment'
              >
                <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
                  {/* Password Field - Required for create */}
                  <FormField
                    control={form.control}
                    name='password'
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Password *</FormLabel>
                        <FormControl>
                          <Input type='password' placeholder='Enter secure password' {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {/* Parent ID Field - Required for create */}
                  <FormField
                    control={form.control}
                    name='parent_id'
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Parent *</FormLabel>
                        <Select onValueChange={field.onChange} defaultValue={field.value}>
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder='Select parent' />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {availableParents.length > 0 ? (
                              availableParents.map(parent => (
                                <SelectItem key={parent.id} value={parent.id}>
                                  {parent.name}
                                </SelectItem>
                              ))
                            ) : (
                              <SelectItem value='temp-parent-id' disabled>
                                No parents available - using temporary ID
                              </SelectItem>
                            )}
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </FormSection>
            )}

            {/* Form Actions */}
            <div className='flex justify-end space-x-4 pt-6 border-t'>
              <Button type='button' variant='outline' onClick={onCancel} disabled={isLoading}>
                <X className='w-4 h-4 mr-2' />
                Cancel
              </Button>

              <Button type='submit' disabled={isLoading}>
                {isLoading ? (
                  <Loader2 className='w-4 h-4 mr-2 animate-spin' />
                ) : (
                  <Save className='w-4 h-4 mr-2' />
                )}
                {mode === 'create' ? 'Register Student' : 'Update Student'}
              </Button>
            </div>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
}

// Export type for external usage
export type { StudentFormProps };

// Default export for lazy loading
export default StudentForm;
