/**
 * Authentication Store - Production-Grade Zustand with JWT + Cookie Support
 *
 * Features:
 * - JWT token management with persistence
 * - Role-based access control
 * - Cookie integration for SSR
 * - Clean API for auth state management
 */

'use client';
import { create } from 'zustand';
import { createJSONStorage, persist } from 'zustand/middleware';

// Role types
type Role = 'SUPER_ADMIN' | 'ADMIN' | 'TEACHER' | 'STAFF' | 'STUDENT' | null;

// User interface
export interface User {
  id: string;
  email: string;
  name?: string;
  username: string;
  first_name?: string;
  last_name?: string;
  role: Role;
  avatar?: string;
  permissions?: string[];
  is_active: boolean;
}

// Cookie utilities for SSR compatibility
const cookieUtils = {
  set: (name: string, value: string, days = 7) => {
    if (typeof document === 'undefined') return;
    const expires = new Date(Date.now() + days * 24 * 60 * 60 * 1000).toUTCString();
    document.cookie = `${name}=${value}; expires=${expires}; path=/; SameSite=Lax`;
  },

  get: (name: string): string | null => {
    if (typeof document === 'undefined') return null;
    const value = `; ${document.cookie}`;
    const parts = value.split(`; ${name}=`);
    if (parts.length === 2) return parts.pop()?.split(';').shift() || null;
    return null;
  },

  remove: (name: string) => {
    if (typeof document === 'undefined') return;
    document.cookie = `${name}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;`;
  },
};

// Auth state interface
type AuthState = {
  token: string | null;
  role: Role;
  userId: string | null;
  user: User | null;
  isLoading: boolean;
  setAuth: (params: { token: string; role: Role; userId?: string; user?: User }) => void;
  fetchUser: () => Promise<void>;
  clear: () => void;
  isAdmin: () => boolean;
  isSuperAdmin: () => boolean;
};

// Create the auth store with persistence
export const useAuthStore = create<AuthState>()(
  persist(
    (set, get) => ({
      // Initial state
      token: null,
      role: null,
      userId: null,
      user: null,
      isLoading: false,

      // Set authentication data
      setAuth: ({ token, role, userId, user }) => {
        console.log('🔐 AuthStore.setAuth called', { hasToken: !!token, role, userId });

        // Store in localStorage for API client
        if (typeof window !== 'undefined') {
          localStorage.setItem(process.env.NEXT_PUBLIC_AUTH_COOKIE || 'access_token', token);
          localStorage.setItem(process.env.NEXT_PUBLIC_ROLE_KEY || 'role', role || '');
        }

        // Store in cookie for SSR/middleware
        cookieUtils.set(process.env.NEXT_PUBLIC_AUTH_COOKIE || 'access_token', token);
        cookieUtils.set(process.env.NEXT_PUBLIC_ROLE_KEY || 'role', role || '');

        // Update store state
        set({
          token,
          role,
          userId: userId || null,
          user: user || null,
        });

        console.log('✅ Auth data stored successfully');

        // Automatically fetch user data if not provided
        if (!user && token) {
          get().fetchUser();
        }
      },

      // Fetch user data from backend
      fetchUser: async () => {
        const { token } = get();
        if (!token) {
          console.warn('⚠️ No token available for user fetch');
          return;
        }

        set({ isLoading: true });

        try {
          console.log('🔄 Fetching user data from backend...');

          // Use the backend API URL
          const baseUrl = process.env.NEXT_PUBLIC_API_URL || 'http://127.0.0.1:8000/api/v1';
          const response = await fetch(`${baseUrl}/auth/me`, {
            method: 'GET',
            headers: {
              Authorization: `Bearer ${token}`,
              'Content-Type': 'application/json',
            },
          });

          if (response.ok) {
            const userData = await response.json();
            console.log('✅ User data fetched successfully:', userData);

            set({
              user: userData,
              role: userData.role || get().role, // Update role from user data
              userId: userData.id || get().userId,
              isLoading: false,
            });
          } else {
            console.error('❌ Failed to fetch user data:', response.status, response.statusText);

            // If 401, clear auth data
            if (response.status === 401) {
              console.log('🚪 Token invalid, clearing auth data');
              get().clear();
            } else {
              set({ isLoading: false });
            }
          }
        } catch (error) {
          console.error('❌ Error fetching user data:', error);
          set({ isLoading: false });
        }
      },

      // Clear authentication data
      clear: () => {
        console.log('🚪 AuthStore.clear called');

        // Clear localStorage
        if (typeof window !== 'undefined') {
          localStorage.removeItem(process.env.NEXT_PUBLIC_AUTH_COOKIE || 'access_token');
          localStorage.removeItem(process.env.NEXT_PUBLIC_ROLE_KEY || 'role');
        }

        // Clear cookies
        cookieUtils.remove(process.env.NEXT_PUBLIC_AUTH_COOKIE || 'access_token');
        cookieUtils.remove(process.env.NEXT_PUBLIC_ROLE_KEY || 'role');

        // Clear store state
        set({
          token: null,
          role: null,
          userId: null,
          user: null,
          isLoading: false,
        });

        console.log('✅ Auth data cleared');
      },

      // Role checking utilities
      isAdmin: () => {
        const { role } = get();
        return role === 'ADMIN' || role === 'SUPER_ADMIN';
      },

      isSuperAdmin: () => {
        const { role } = get();
        return role === 'SUPER_ADMIN';
      },
    }),
    {
      name: 'auth',
      storage: createJSONStorage(() => localStorage),
      partialize: state => ({
        token: state.token,
        role: state.role,
        userId: state.userId,
        user: state.user,
      }),
    }
  )
);

// Permission utilities
export const canCreateTeacher = (role?: Role) => role === 'SUPER_ADMIN' || role === 'ADMIN';
export const canEditTeacher = (role?: Role) => role === 'SUPER_ADMIN' || role === 'ADMIN';
export const canDeleteTeacher = (role?: Role) => role === 'SUPER_ADMIN' || role === 'ADMIN';

// Legacy compatibility exports
export type { Role, User };

// Backward compatibility alias
export const useAuth = useAuthStore;
