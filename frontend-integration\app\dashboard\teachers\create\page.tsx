/**
 * Teachers Create Page - Production-Grade Implementation
 *
 * Features:
 * - Form validation with Zod + React Hook Form
 * - Real API integration with React Query
 * - Role-based access control (ADMIN only)
 * - Professional UI with loading and error states
 * - Redirect to detail page on success
 * - Optimistic updates and toast notifications
 * - Hard refresh support
 */

'use client';

import { zodResolver } from '@hookform/resolvers/zod';
import { AlertTriangle, ArrowLeft, Save, User } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { useEffect } from 'react';
import { useForm } from 'react-hook-form';

// UI Components
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { useToast } from '@/hooks/use-toast';

// Hooks and Services
import { usePermissions } from '@/components/auth/ProtectedRoute';
import { useCreateTeacher } from '@/hooks/useTeachers';
import { TeacherCreateSchema, type TeacherCreate } from '@/schemas/zodSchemas';

export default function TeacherCreatePage() {
  const router = useRouter();
  const { toast } = useToast();
  const { isAdmin } = usePermissions();

  // Check permissions
  useEffect(() => {
    if (!isAdmin()) {
      toast({
        title: 'Access Denied',
        description: 'Only administrators can create teachers',
        variant: 'destructive',
      });
      router.push('/dashboard/teachers');
    }
  }, [isAdmin, router, toast]);

  // Create mutation
  const createTeacherMutation = useCreateTeacher();

  // Form setup
  const form = useForm<TeacherCreate>({
    resolver: zodResolver(TeacherCreateSchema),
    defaultValues: {
      name: '',
      subject: '',
      email: '',
      department: '',
      phone: '',
      status: 'ACTIVE',
      hire_date: '',
    },
  });

  const onSubmit = async (data: TeacherCreate) => {
    console.log('📝 Creating teacher with data:', data);
    console.log('🔐 Current user role:', usePermissions().getCurrentRole());
    console.log('🔑 JWT token available:', !!localStorage.getItem('access_token'));

    try {
      const newTeacher = await createTeacherMutation.mutateAsync(data);
      console.log('✅ Teacher created successfully:', newTeacher);

      toast({
        title: 'Success',
        description: 'Teacher created successfully',
      });

      // Redirect to detail page
      router.push(`/dashboard/teachers/${newTeacher.id}`);
    } catch (error) {
      console.error('❌ Create teacher error:', error);

      // More detailed error logging
      if (error instanceof Error) {
        console.error('Error message:', error.message);
        console.error('Error stack:', error.stack);
      }

      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to create teacher',
        variant: 'destructive',
      });
    }
  };

  const handleBack = () => {
    router.push('/dashboard/teachers');
  };

  // Don't render if not admin
  if (!isAdmin()) {
    return (
      <div className='container mx-auto p-4 sm:p-6 lg:p-8'>
        <Card>
          <CardContent className='flex flex-col items-center justify-center py-12'>
            <AlertTriangle className='h-16 w-16 text-destructive mb-6' />
            <h2 className='text-2xl font-bold mb-2'>Access Denied</h2>
            <p className='text-muted-foreground text-center mb-6 max-w-md'>
              Only administrators can create teachers.
            </p>
            <Button onClick={handleBack}>
              <ArrowLeft className='h-4 w-4 mr-2' />
              Back to Teachers
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className='container mx-auto p-4 sm:p-6 lg:p-8 space-y-6'>
      {/* Header */}
      <div className='flex items-center justify-between'>
        <div className='flex items-center gap-4'>
          <Button variant='ghost' size='sm' onClick={handleBack}>
            <ArrowLeft className='h-4 w-4 mr-2' />
            Back to Teachers
          </Button>
          <div>
            <h1 className='text-2xl sm:text-3xl font-bold tracking-tight'>Create Teacher</h1>
            <p className='text-muted-foreground'>Add a new teacher to the system</p>
          </div>
        </div>
      </div>

      {/* Form */}
      <Card>
        <CardHeader>
          <CardTitle className='flex items-center gap-2'>
            <User className='h-5 w-5' />
            Teacher Information
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className='space-y-6'>
              <div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
                {/* Name */}
                <FormField
                  control={form.control}
                  name='name'
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Full Name</FormLabel>
                      <FormControl>
                        <Input placeholder="Enter teacher's full name" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Subject */}
                <FormField
                  control={form.control}
                  name='subject'
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Subject</FormLabel>
                      <FormControl>
                        <Input placeholder='Enter subject taught' {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Email */}
                <FormField
                  control={form.control}
                  name='email'
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Email</FormLabel>
                      <FormControl>
                        <Input type='email' placeholder='Enter email address' {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Phone */}
                <FormField
                  control={form.control}
                  name='phone'
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Phone</FormLabel>
                      <FormControl>
                        <Input placeholder='Enter phone number' {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Department */}
                <FormField
                  control={form.control}
                  name='department'
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Department</FormLabel>
                      <FormControl>
                        <Input placeholder='Enter department' {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Status */}
                <FormField
                  control={form.control}
                  name='status'
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Status</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder='Select status' />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value='ACTIVE'>Active</SelectItem>
                          <SelectItem value='INACTIVE'>Inactive</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Hire Date */}
                <FormField
                  control={form.control}
                  name='hire_date'
                  render={({ field }) => (
                    <FormItem className='md:col-span-2'>
                      <FormLabel>Hire Date</FormLabel>
                      <FormControl>
                        <Input type='date' {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              {/* Submit Button */}
              <div className='flex items-center justify-end gap-4 pt-6'>
                <Button type='button' variant='outline' onClick={handleBack}>
                  Cancel
                </Button>
                <Button type='submit' disabled={createTeacherMutation.isPending}>
                  <Save className='h-4 w-4 mr-2' />
                  {createTeacherMutation.isPending ? 'Creating...' : 'Create Teacher'}
                </Button>
              </div>
            </form>
          </Form>
        </CardContent>
      </Card>
    </div>
  );
}
