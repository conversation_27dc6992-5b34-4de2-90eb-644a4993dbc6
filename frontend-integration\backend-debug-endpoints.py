# Backend Debug Endpoints - Add these to your FastAPI main.py

from fastapi import FastAPI, HTTPException
import logging
import traceback
import os
from typing import Optional

# Configure detailed logging
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Debug endpoint to test database connectivity
@app.get("/api/v1/debug/database")
async def debug_database():
    """Debug database connection and basic operations"""
    debug_info = {
        "timestamp": str(datetime.now()),
        "environment_variables": {},
        "database_tests": {},
        "errors": []
    }
    
    try:
        # Check environment variables
        db_vars = [
            "DATABASE_URL", "DB_HOST", "DB_PORT", "DB_NAME", 
            "DB_USER", "DB_PASSWORD", "POSTGRES_URL"
        ]
        
        for var in db_vars:
            value = os.getenv(var)
            debug_info["environment_variables"][var] = "SET" if value else "NOT_SET"
            if value and "password" not in var.lower():
                debug_info["environment_variables"][f"{var}_preview"] = value[:20] + "..." if len(value) > 20 else value
        
        # Test 1: Basic database connection
        try:
            # TODO: Replace with your actual database connection test
            # Example for different database types:
            
            # For SQLAlchemy:
            # from your_database import engine
            # with engine.connect() as conn:
            #     result = conn.execute("SELECT 1 as test")
            #     debug_info["database_tests"]["connection"] = "SUCCESS"
            
            # For asyncpg (PostgreSQL):
            # import asyncpg
            # conn = await asyncpg.connect(DATABASE_URL)
            # await conn.fetchval("SELECT 1")
            # await conn.close()
            # debug_info["database_tests"]["connection"] = "SUCCESS"
            
            # For now, simulate connection test
            debug_info["database_tests"]["connection"] = "SIMULATED_SUCCESS"
            
        except Exception as e:
            debug_info["database_tests"]["connection"] = f"FAILED: {str(e)}"
            debug_info["errors"].append(f"Connection test: {str(e)}")
        
        # Test 2: Check if teachers table exists
        try:
            # TODO: Replace with actual table check
            # Example:
            # result = await database.fetch_one("SELECT COUNT(*) FROM information_schema.tables WHERE table_name = 'teachers'")
            # debug_info["database_tests"]["teachers_table_exists"] = result[0] > 0
            
            debug_info["database_tests"]["teachers_table_exists"] = "SIMULATED_TRUE"
            
        except Exception as e:
            debug_info["database_tests"]["teachers_table_exists"] = f"FAILED: {str(e)}"
            debug_info["errors"].append(f"Table check: {str(e)}")
        
        # Test 3: Try to count teachers
        try:
            # TODO: Replace with actual count query
            # count = await database.fetch_val("SELECT COUNT(*) FROM teachers")
            # debug_info["database_tests"]["teachers_count"] = count
            
            debug_info["database_tests"]["teachers_count"] = "SIMULATED_5"
            
        except Exception as e:
            debug_info["database_tests"]["teachers_count"] = f"FAILED: {str(e)}"
            debug_info["errors"].append(f"Count query: {str(e)}")
        
        return debug_info
        
    except Exception as e:
        logger.error(f"Debug endpoint failed: {e}")
        logger.error(f"Traceback: {traceback.format_exc()}")
        debug_info["errors"].append(f"Debug endpoint error: {str(e)}")
        return debug_info

# Fixed teachers endpoint with comprehensive error handling
@app.get("/api/v1/teachers/")
async def get_teachers_fixed():
    """Teachers endpoint with detailed error handling and logging"""
    try:
        logger.info("=== TEACHERS ENDPOINT CALLED ===")
        logger.info("Starting teachers query...")
        
        # TODO: Replace this section with your actual database query
        # Here are examples for different database setups:
        
        # Example 1: SQLAlchemy ORM
        # from your_models import Teacher
        # teachers = db.query(Teacher).all()
        # result = [teacher.to_dict() for teacher in teachers]
        
        # Example 2: Raw SQL with asyncpg
        # query = "SELECT id, name, email, subject, department, status FROM teachers"
        # rows = await database.fetch_all(query)
        # result = [dict(row) for row in rows]
        
        # Example 3: SQLAlchemy Core
        # from your_database import teachers_table, engine
        # with engine.connect() as conn:
        #     result = conn.execute(teachers_table.select()).fetchall()
        #     result = [dict(row) for row in result]
        
        # TEMPORARY: Return mock data to test frontend
        logger.info("Returning mock data for testing...")
        result = [
            {
                "id": "1",
                "name": "John Doe",
                "email": "<EMAIL>",
                "subject": "Mathematics",
                "department": "Science",
                "status": "ACTIVE",
                "created_at": "2024-01-01T00:00:00Z"
            },
            {
                "id": "2", 
                "name": "Jane Smith",
                "email": "<EMAIL>",
                "subject": "English Literature",
                "department": "Humanities",
                "status": "ACTIVE",
                "created_at": "2024-01-01T00:00:00Z"
            },
            {
                "id": "3",
                "name": "Bob Johnson", 
                "email": "<EMAIL>",
                "subject": "Physics",
                "department": "Science",
                "status": "INACTIVE",
                "created_at": "2024-01-01T00:00:00Z"
            }
        ]
        
        logger.info(f"Successfully returning {len(result)} teachers")
        return result
        
    except Exception as e:
        # Detailed error logging
        logger.error("=== TEACHERS ENDPOINT ERROR ===")
        logger.error(f"Error type: {type(e).__name__}")
        logger.error(f"Error message: {str(e)}")
        logger.error(f"Full traceback: {traceback.format_exc()}")
        
        # Check if it's a specific database error
        error_message = str(e).lower()
        if "connection" in error_message:
            logger.error("DATABASE CONNECTION ERROR detected")
        elif "table" in error_message or "relation" in error_message:
            logger.error("TABLE/RELATION ERROR detected - check if teachers table exists")
        elif "authentication" in error_message or "password" in error_message:
            logger.error("DATABASE AUTHENTICATION ERROR detected")
        
        # For development: return detailed error info
        # For production: return generic error
        if os.getenv("ENVIRONMENT") == "development":
            raise HTTPException(
                status_code=500,
                detail={
                    "error": "Database error in teachers endpoint",
                    "type": type(e).__name__,
                    "message": str(e),
                    "suggestion": "Check database connection and teachers table"
                }
            )
        else:
            # Production: return empty array instead of error
            logger.warning("Returning empty array due to database error")
            return []

# Health endpoint
@app.get("/api/v1/health")
async def health_check():
    """Health check with database connectivity test"""
    try:
        # Test database connection
        # TODO: Add actual database ping
        # db_healthy = await test_database_connection()
        db_healthy = True  # Simulate for now
        
        return {
            "status": "healthy" if db_healthy else "degraded",
            "database": "connected" if db_healthy else "disconnected",
            "timestamp": str(datetime.now()),
            "version": "1.0.0"
        }
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        return {
            "status": "unhealthy",
            "database": "error",
            "error": str(e),
            "timestamp": str(datetime.now())
        }
