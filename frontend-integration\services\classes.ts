// services/classes.ts - Classes service with proper auth
import { api } from '@/api/apiClient';
import axios from 'axios';

export interface Class {
  id: string;
  name: string;
  grade: string;
  section: string;
  capacity: number;
  enrolled?: number;
  teacher_id: string;
  teacher_name?: string;
  room?: string;
  schedule?: string;
  academic_year: string;
  status?: string;
  subjects?: string[];
  class_monitor?: string;
  created_at?: string;
  updated_at?: string;
}

export async function fetchClasses(): Promise<Class[]> {
  try {
    console.log('[Classes] Fetching classes list...');
    const res = await api.get('/api/v1/classes/');

    console.log('[Classes] Response:', res.status, res.data);

    if (Array.isArray(res.data)) {
      return res.data;
    } else if (res.data?.data && Array.isArray(res.data.data)) {
      return res.data.data;
    } else if (res.data?.classes && Array.isArray(res.data.classes)) {
      return res.data.classes;
    }

    console.warn('[Classes] Unexpected response format:', res.data);
    return [];
  } catch (err) {
    if (axios.isAxiosError(err)) {
      const status = err.response?.status;
      console.error('[Classes] API Error:', status, err.response?.data);

      if (status === 404) {
        console.log('[Classes] 404 treated as empty list');
        return [];
      } else if (status === 401) {
        console.error('[Classes] Unauthorized - check token');
        throw new Error('Authentication required. Please login again.');
      } else if (status === 403) {
        console.error('[Classes] Forbidden - insufficient permissions');
        throw new Error("You don't have permission to view classes.");
      }
    }

    console.error('[Classes] Fetch failed:', err);
    throw new Error(err instanceof Error ? err.message : 'Failed to fetch classes');
  }
}

export async function createClass(classData: Partial<Class>): Promise<Class> {
  try {
    console.log('[Classes] Creating class:', classData);
    const res = await api.post('/api/v1/classes/', classData);
    console.log('[Classes] Created:', res.status, res.data);
    return res.data;
  } catch (err) {
    console.error('[Classes] Create failed:', err);
    throw new Error(err instanceof Error ? err.message : 'Failed to create class');
  }
}

export async function updateClass(id: string, classData: Partial<Class>): Promise<Class> {
  try {
    console.log('[Classes] Updating class:', id, classData);
    const res = await api.put(`/api/v1/classes/${id}`, classData);
    console.log('[Classes] Updated:', res.status, res.data);
    return res.data;
  } catch (err) {
    console.error('[Classes] Update failed:', err);
    throw new Error(err instanceof Error ? err.message : 'Failed to update class');
  }
}

export async function deleteClass(id: string): Promise<void> {
  try {
    console.log('[Classes] Deleting class:', id);
    const res = await api.delete(`/api/v1/classes/${id}`);
    console.log('[Classes] Deleted:', res.status);
  } catch (err) {
    console.error('[Classes] Delete failed:', err);
    throw new Error(err instanceof Error ? err.message : 'Failed to delete class');
  }
}

export async function fetchClassById(id: string): Promise<Class> {
  try {
    console.log('[Classes] Fetching class:', id);
    const res = await api.get(`/api/v1/classes/${id}`);
    console.log('[Classes] Fetched:', res.status, res.data);
    return res.data;
  } catch (err) {
    console.error('[Classes] Fetch by ID failed:', err);
    throw new Error(err instanceof Error ? err.message : 'Failed to fetch class');
  }
}