/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/_not-found/page";
exports.ids = ["app/_not-found/page"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=private-next-app-dir%2Fnot-found.tsx&appDir=C%3A%5CUsers%5CImran%20Bhai%5COneDrive%5CPictures%5Cschool_pro%5Cschool-pro-backend%5Cfrontend-integration%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CImran%20Bhai%5COneDrive%5CPictures%5Cschool_pro%5Cschool-pro-backend%5Cfrontend-integration&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=private-next-app-dir%2Fnot-found.tsx&appDir=C%3A%5CUsers%5CImran%20Bhai%5COneDrive%5CPictures%5Cschool_pro%5Cschool-pro-backend%5Cfrontend-integration%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CImran%20Bhai%5COneDrive%5CPictures%5Cschool_pro%5Cschool-pro-backend%5Cfrontend-integration&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?d969\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n          children: [\"/_not-found\", {\n            children: ['__PAGE__', {}, {\n              page: [\n                () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/not-found.tsx */ \"(rsc)/./app/not-found.tsx\")),\n                \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Pictures\\\\school_pro\\\\school-pro-backend\\\\frontend-integration\\\\app\\\\not-found.tsx\"\n              ]\n            }]\n          }, {}]\n        },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\")), \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Pictures\\\\school_pro\\\\school-pro-backend\\\\frontend-integration\\\\app\\\\layout.tsx\"],\n'error': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/error.tsx */ \"(rsc)/./app/error.tsx\")), \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Pictures\\\\school_pro\\\\school-pro-backend\\\\frontend-integration\\\\app\\\\error.tsx\"],\n'loading': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/loading.tsx */ \"(rsc)/./app/loading.tsx\")), \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Pictures\\\\school_pro\\\\school-pro-backend\\\\frontend-integration\\\\app\\\\loading.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/not-found.tsx */ \"(rsc)/./app/not-found.tsx\")), \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Pictures\\\\school_pro\\\\school-pro-backend\\\\frontend-integration\\\\app\\\\not-found.tsx\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/_not-found/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/_not-found/page\",\n        pathname: \"/_not-found\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=private-next-app-dir%2Fnot-found.tsx&appDir=C%3A%5CUsers%5CImran%20Bhai%5COneDrive%5CPictures%5Cschool_pro%5Cschool-pro-backend%5Cfrontend-integration%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CImran%20Bhai%5COneDrive%5CPictures%5Cschool_pro%5Cschool-pro-backend%5Cfrontend-integration&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CImran%20Bhai%5C%5COneDrive%5C%5CPictures%5C%5Cschool_pro%5C%5Cschool-pro-backend%5C%5Cfrontend-integration%5C%5Capp%5C%5Cerror.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CImran%20Bhai%5C%5COneDrive%5C%5CPictures%5C%5Cschool_pro%5C%5Cschool-pro-backend%5C%5Cfrontend-integration%5C%5Capp%5C%5Cerror.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/error.tsx */ \"(ssr)/./app/error.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q0ltcmFuJTIwQmhhaSU1QyU1Q09uZURyaXZlJTVDJTVDUGljdHVyZXMlNUMlNUNzY2hvb2xfcHJvJTVDJTVDc2Nob29sLXByby1iYWNrZW5kJTVDJTVDZnJvbnRlbmQtaW50ZWdyYXRpb24lNUMlNUNhcHAlNUMlNUNlcnJvci50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLDBJQUFvSiIsInNvdXJjZXMiOlsid2VicGFjazovL3NjaG9vbC1tYW5hZ2VtZW50LWZyb250ZW5kLz8yMGQzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcSW1yYW4gQmhhaVxcXFxPbmVEcml2ZVxcXFxQaWN0dXJlc1xcXFxzY2hvb2xfcHJvXFxcXHNjaG9vbC1wcm8tYmFja2VuZFxcXFxmcm9udGVuZC1pbnRlZ3JhdGlvblxcXFxhcHBcXFxcZXJyb3IudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CImran%20Bhai%5C%5COneDrive%5C%5CPictures%5C%5Cschool_pro%5C%5Cschool-pro-backend%5C%5Cfrontend-integration%5C%5Capp%5C%5Cerror.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CImran%20Bhai%5C%5COneDrive%5C%5CPictures%5C%5Cschool_pro%5C%5Cschool-pro-backend%5C%5Cfrontend-integration%5C%5Ccomponents%5C%5Cproviders.tsx%22%2C%22ids%22%3A%5B%22Providers%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CImran%20Bhai%5C%5COneDrive%5C%5CPictures%5C%5Cschool_pro%5C%5Cschool-pro-backend%5C%5Cfrontend-integration%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CImran%20Bhai%5C%5COneDrive%5C%5CPictures%5C%5Cschool_pro%5C%5Cschool-pro-backend%5C%5Cfrontend-integration%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CImran%20Bhai%5C%5COneDrive%5C%5CPictures%5C%5Cschool_pro%5C%5Cschool-pro-backend%5C%5Cfrontend-integration%5C%5Cnode_modules%5C%5Csonner%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CImran%20Bhai%5C%5COneDrive%5C%5CPictures%5C%5Cschool_pro%5C%5Cschool-pro-backend%5C%5Cfrontend-integration%5C%5Ccomponents%5C%5Cproviders.tsx%22%2C%22ids%22%3A%5B%22Providers%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CImran%20Bhai%5C%5COneDrive%5C%5CPictures%5C%5Cschool_pro%5C%5Cschool-pro-backend%5C%5Cfrontend-integration%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CImran%20Bhai%5C%5COneDrive%5C%5CPictures%5C%5Cschool_pro%5C%5Cschool-pro-backend%5C%5Cfrontend-integration%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CImran%20Bhai%5C%5COneDrive%5C%5CPictures%5C%5Cschool_pro%5C%5Cschool-pro-backend%5C%5Cfrontend-integration%5C%5Cnode_modules%5C%5Csonner%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/providers.tsx */ \"(ssr)/./components/providers.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/sonner/dist/index.mjs */ \"(ssr)/./node_modules/sonner/dist/index.mjs\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CImran%20Bhai%5C%5COneDrive%5C%5CPictures%5C%5Cschool_pro%5C%5Cschool-pro-backend%5C%5Cfrontend-integration%5C%5Ccomponents%5C%5Cproviders.tsx%22%2C%22ids%22%3A%5B%22Providers%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CImran%20Bhai%5C%5COneDrive%5C%5CPictures%5C%5Cschool_pro%5C%5Cschool-pro-backend%5C%5Cfrontend-integration%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CImran%20Bhai%5C%5COneDrive%5C%5CPictures%5C%5Cschool_pro%5C%5Cschool-pro-backend%5C%5Cfrontend-integration%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CImran%20Bhai%5C%5COneDrive%5C%5CPictures%5C%5Cschool_pro%5C%5Cschool-pro-backend%5C%5Cfrontend-integration%5C%5Cnode_modules%5C%5Csonner%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CImran%20Bhai%5C%5COneDrive%5C%5CPictures%5C%5Cschool_pro%5C%5Cschool-pro-backend%5C%5Cfrontend-integration%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CImran%20Bhai%5C%5COneDrive%5C%5CPictures%5C%5Cschool_pro%5C%5Cschool-pro-backend%5C%5Cfrontend-integration%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CImran%20Bhai%5C%5COneDrive%5C%5CPictures%5C%5Cschool_pro%5C%5Cschool-pro-backend%5C%5Cfrontend-integration%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CImran%20Bhai%5C%5COneDrive%5C%5CPictures%5C%5Cschool_pro%5C%5Cschool-pro-backend%5C%5Cfrontend-integration%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CImran%20Bhai%5C%5COneDrive%5C%5CPictures%5C%5Cschool_pro%5C%5Cschool-pro-backend%5C%5Cfrontend-integration%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CImran%20Bhai%5C%5COneDrive%5C%5CPictures%5C%5Cschool_pro%5C%5Cschool-pro-backend%5C%5Cfrontend-integration%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CImran%20Bhai%5C%5COneDrive%5C%5CPictures%5C%5Cschool_pro%5C%5Cschool-pro-backend%5C%5Cfrontend-integration%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CImran%20Bhai%5C%5COneDrive%5C%5CPictures%5C%5Cschool_pro%5C%5Cschool-pro-backend%5C%5Cfrontend-integration%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CImran%20Bhai%5C%5COneDrive%5C%5CPictures%5C%5Cschool_pro%5C%5Cschool-pro-backend%5C%5Cfrontend-integration%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CImran%20Bhai%5C%5COneDrive%5C%5CPictures%5C%5Cschool_pro%5C%5Cschool-pro-backend%5C%5Cfrontend-integration%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CImran%20Bhai%5C%5COneDrive%5C%5CPictures%5C%5Cschool_pro%5C%5Cschool-pro-backend%5C%5Cfrontend-integration%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CImran%20Bhai%5C%5COneDrive%5C%5CPictures%5C%5Cschool_pro%5C%5Cschool-pro-backend%5C%5Cfrontend-integration%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q0ltcmFuJTIwQmhhaSU1QyU1Q09uZURyaXZlJTVDJTVDUGljdHVyZXMlNUMlNUNzY2hvb2xfcHJvJTVDJTVDc2Nob29sLXByby1iYWNrZW5kJTVDJTVDZnJvbnRlbmQtaW50ZWdyYXRpb24lNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZGlzdCU1QyU1Q2NsaWVudCU1QyU1Q2NvbXBvbmVudHMlNUMlNUNhcHAtcm91dGVyLmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q0ltcmFuJTIwQmhhaSU1QyU1Q09uZURyaXZlJTVDJTVDUGljdHVyZXMlNUMlNUNzY2hvb2xfcHJvJTVDJTVDc2Nob29sLXByby1iYWNrZW5kJTVDJTVDZnJvbnRlbmQtaW50ZWdyYXRpb24lNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZGlzdCU1QyU1Q2NsaWVudCU1QyU1Q2NvbXBvbmVudHMlNUMlNUNjbGllbnQtcGFnZS5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJDJTNBJTVDJTVDVXNlcnMlNUMlNUNJbXJhbiUyMEJoYWklNUMlNUNPbmVEcml2ZSU1QyU1Q1BpY3R1cmVzJTVDJTVDc2Nob29sX3BybyU1QyU1Q3NjaG9vbC1wcm8tYmFja2VuZCU1QyU1Q2Zyb250ZW5kLWludGVncmF0aW9uJTVDJTVDbm9kZV9tb2R1bGVzJTVDJTVDbmV4dCU1QyU1Q2Rpc3QlNUMlNUNjbGllbnQlNUMlNUNjb21wb25lbnRzJTVDJTVDZXJyb3ItYm91bmRhcnkuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyQyUzQSU1QyU1Q1VzZXJzJTVDJTVDSW1yYW4lMjBCaGFpJTVDJTVDT25lRHJpdmUlNUMlNUNQaWN0dXJlcyU1QyU1Q3NjaG9vbF9wcm8lNUMlNUNzY2hvb2wtcHJvLWJhY2tlbmQlNUMlNUNmcm9udGVuZC1pbnRlZ3JhdGlvbiU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNkaXN0JTVDJTVDY2xpZW50JTVDJTVDY29tcG9uZW50cyU1QyU1Q2xheW91dC1yb3V0ZXIuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyQyUzQSU1QyU1Q1VzZXJzJTVDJTVDSW1yYW4lMjBCaGFpJTVDJTVDT25lRHJpdmUlNUMlNUNQaWN0dXJlcyU1QyU1Q3NjaG9vbF9wcm8lNUMlNUNzY2hvb2wtcHJvLWJhY2tlbmQlNUMlNUNmcm9udGVuZC1pbnRlZ3JhdGlvbiU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNkaXN0JTVDJTVDY2xpZW50JTVDJTVDY29tcG9uZW50cyU1QyU1Q25vdC1mb3VuZC1ib3VuZGFyeS5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJDJTNBJTVDJTVDVXNlcnMlNUMlNUNJbXJhbiUyMEJoYWklNUMlNUNPbmVEcml2ZSU1QyU1Q1BpY3R1cmVzJTVDJTVDc2Nob29sX3BybyU1QyU1Q3NjaG9vbC1wcm8tYmFja2VuZCU1QyU1Q2Zyb250ZW5kLWludGVncmF0aW9uJTVDJTVDbm9kZV9tb2R1bGVzJTVDJTVDbmV4dCU1QyU1Q2Rpc3QlNUMlNUNjbGllbnQlNUMlNUNjb21wb25lbnRzJTVDJTVDcmVuZGVyLWZyb20tdGVtcGxhdGUtY29udGV4dC5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsa09BQWlNO0FBQ2pNO0FBQ0Esb09BQWtNO0FBQ2xNO0FBQ0EsME9BQXFNO0FBQ3JNO0FBQ0Esd09BQW9NO0FBQ3BNO0FBQ0Esa1BBQXlNO0FBQ3pNO0FBQ0Esc1FBQW1OIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vc2Nob29sLW1hbmFnZW1lbnQtZnJvbnRlbmQvP2Y4N2EiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxJbXJhbiBCaGFpXFxcXE9uZURyaXZlXFxcXFBpY3R1cmVzXFxcXHNjaG9vbF9wcm9cXFxcc2Nob29sLXByby1iYWNrZW5kXFxcXGZyb250ZW5kLWludGVncmF0aW9uXFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGNvbXBvbmVudHNcXFxcYXBwLXJvdXRlci5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcSW1yYW4gQmhhaVxcXFxPbmVEcml2ZVxcXFxQaWN0dXJlc1xcXFxzY2hvb2xfcHJvXFxcXHNjaG9vbC1wcm8tYmFja2VuZFxcXFxmcm9udGVuZC1pbnRlZ3JhdGlvblxcXFxub2RlX21vZHVsZXNcXFxcbmV4dFxcXFxkaXN0XFxcXGNsaWVudFxcXFxjb21wb25lbnRzXFxcXGNsaWVudC1wYWdlLmpzXCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxJbXJhbiBCaGFpXFxcXE9uZURyaXZlXFxcXFBpY3R1cmVzXFxcXHNjaG9vbF9wcm9cXFxcc2Nob29sLXByby1iYWNrZW5kXFxcXGZyb250ZW5kLWludGVncmF0aW9uXFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGNvbXBvbmVudHNcXFxcZXJyb3ItYm91bmRhcnkuanNcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXEltcmFuIEJoYWlcXFxcT25lRHJpdmVcXFxcUGljdHVyZXNcXFxcc2Nob29sX3Byb1xcXFxzY2hvb2wtcHJvLWJhY2tlbmRcXFxcZnJvbnRlbmQtaW50ZWdyYXRpb25cXFxcbm9kZV9tb2R1bGVzXFxcXG5leHRcXFxcZGlzdFxcXFxjbGllbnRcXFxcY29tcG9uZW50c1xcXFxsYXlvdXQtcm91dGVyLmpzXCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxJbXJhbiBCaGFpXFxcXE9uZURyaXZlXFxcXFBpY3R1cmVzXFxcXHNjaG9vbF9wcm9cXFxcc2Nob29sLXByby1iYWNrZW5kXFxcXGZyb250ZW5kLWludGVncmF0aW9uXFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGNvbXBvbmVudHNcXFxcbm90LWZvdW5kLWJvdW5kYXJ5LmpzXCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxJbXJhbiBCaGFpXFxcXE9uZURyaXZlXFxcXFBpY3R1cmVzXFxcXHNjaG9vbF9wcm9cXFxcc2Nob29sLXByby1iYWNrZW5kXFxcXGZyb250ZW5kLWludGVncmF0aW9uXFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGNvbXBvbmVudHNcXFxccmVuZGVyLWZyb20tdGVtcGxhdGUtY29udGV4dC5qc1wiKTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CImran%20Bhai%5C%5COneDrive%5C%5CPictures%5C%5Cschool_pro%5C%5Cschool-pro-backend%5C%5Cfrontend-integration%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CImran%20Bhai%5C%5COneDrive%5C%5CPictures%5C%5Cschool_pro%5C%5Cschool-pro-backend%5C%5Cfrontend-integration%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CImran%20Bhai%5C%5COneDrive%5C%5CPictures%5C%5Cschool_pro%5C%5Cschool-pro-backend%5C%5Cfrontend-integration%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CImran%20Bhai%5C%5COneDrive%5C%5CPictures%5C%5Cschool_pro%5C%5Cschool-pro-backend%5C%5Cfrontend-integration%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CImran%20Bhai%5C%5COneDrive%5C%5CPictures%5C%5Cschool_pro%5C%5Cschool-pro-backend%5C%5Cfrontend-integration%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CImran%20Bhai%5C%5COneDrive%5C%5CPictures%5C%5Cschool_pro%5C%5Cschool-pro-backend%5C%5Cfrontend-integration%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CImran%20Bhai%5C%5COneDrive%5C%5CPictures%5C%5Cschool_pro%5C%5Cschool-pro-backend%5C%5Cfrontend-integration%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Clink.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CImran%20Bhai%5C%5COneDrive%5C%5CPictures%5C%5Cschool_pro%5C%5Cschool-pro-backend%5C%5Cfrontend-integration%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Clink.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/link.js */ \"(ssr)/./node_modules/next/dist/client/link.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q0ltcmFuJTIwQmhhaSU1QyU1Q09uZURyaXZlJTVDJTVDUGljdHVyZXMlNUMlNUNzY2hvb2xfcHJvJTVDJTVDc2Nob29sLXByby1iYWNrZW5kJTVDJTVDZnJvbnRlbmQtaW50ZWdyYXRpb24lNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZGlzdCU1QyU1Q2NsaWVudCU1QyU1Q2xpbmsuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjIqJTIyJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxnTUFBK0siLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9zY2hvb2wtbWFuYWdlbWVudC1mcm9udGVuZC8/MDk3MiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXEltcmFuIEJoYWlcXFxcT25lRHJpdmVcXFxcUGljdHVyZXNcXFxcc2Nob29sX3Byb1xcXFxzY2hvb2wtcHJvLWJhY2tlbmRcXFxcZnJvbnRlbmQtaW50ZWdyYXRpb25cXFxcbm9kZV9tb2R1bGVzXFxcXG5leHRcXFxcZGlzdFxcXFxjbGllbnRcXFxcbGluay5qc1wiKTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CImran%20Bhai%5C%5COneDrive%5C%5CPictures%5C%5Cschool_pro%5C%5Cschool-pro-backend%5C%5Cfrontend-integration%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Clink.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./app/error.tsx":
/*!***********************!*\
  !*** ./app/error.tsx ***!
  \***********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Error)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction Error({ error, reset }) {\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Log the error to an error reporting service\n        console.error(\"Global error:\", error);\n    }, [\n        error\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen flex items-center justify-center bg-gray-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-center max-w-md mx-auto p-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mx-auto h-16 w-16 bg-red-100 rounded-full flex items-center justify-center mb-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-red-600 text-3xl\",\n                        children: \"⚠️\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Pictures\\\\school_pro\\\\school-pro-backend\\\\frontend-integration\\\\app\\\\error.tsx\",\n                        lineNumber: 27,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Pictures\\\\school_pro\\\\school-pro-backend\\\\frontend-integration\\\\app\\\\error.tsx\",\n                    lineNumber: 26,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                    className: \"text-2xl font-bold text-gray-800 mb-4\",\n                    children: \"Something went wrong!\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Pictures\\\\school_pro\\\\school-pro-backend\\\\frontend-integration\\\\app\\\\error.tsx\",\n                    lineNumber: 31,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-gray-600 mb-6\",\n                    children: \"We encountered an unexpected error. Please try again or contact support if the problem persists.\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Pictures\\\\school_pro\\\\school-pro-backend\\\\frontend-integration\\\\app\\\\error.tsx\",\n                    lineNumber: 35,\n                    columnNumber: 9\n                }, this),\n                 true && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-red-50 border border-red-200 rounded-lg p-4 mb-6 text-left\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"font-semibold text-red-800 mb-2\",\n                            children: \"Error Details:\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Pictures\\\\school_pro\\\\school-pro-backend\\\\frontend-integration\\\\app\\\\error.tsx\",\n                            lineNumber: 42,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm text-red-700 font-mono break-all\",\n                            children: error.message\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Pictures\\\\school_pro\\\\school-pro-backend\\\\frontend-integration\\\\app\\\\error.tsx\",\n                            lineNumber: 43,\n                            columnNumber: 13\n                        }, this),\n                        error.digest && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xs text-red-600 mt-2\",\n                            children: [\n                                \"Error ID: \",\n                                error.digest\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Pictures\\\\school_pro\\\\school-pro-backend\\\\frontend-integration\\\\app\\\\error.tsx\",\n                            lineNumber: 47,\n                            columnNumber: 15\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Pictures\\\\school_pro\\\\school-pro-backend\\\\frontend-integration\\\\app\\\\error.tsx\",\n                    lineNumber: 41,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col sm:flex-row gap-3 justify-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: reset,\n                            className: \"px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\",\n                            children: \"Try Again\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Pictures\\\\school_pro\\\\school-pro-backend\\\\frontend-integration\\\\app\\\\error.tsx\",\n                            lineNumber: 56,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>window.location.href = \"/\",\n                            className: \"px-6 py-3 bg-gray-200 text-gray-800 rounded-lg hover:bg-gray-300 transition-colors\",\n                            children: \"Go Home\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Pictures\\\\school_pro\\\\school-pro-backend\\\\frontend-integration\\\\app\\\\error.tsx\",\n                            lineNumber: 63,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Pictures\\\\school_pro\\\\school-pro-backend\\\\frontend-integration\\\\app\\\\error.tsx\",\n                    lineNumber: 55,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-sm text-gray-500 mt-6\",\n                    children: [\n                        \"Need help? Contact\",\n                        \" \",\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                            href: \"mailto:<EMAIL>\",\n                            className: \"text-blue-600 hover:underline\",\n                            children: \"<EMAIL>\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Pictures\\\\school_pro\\\\school-pro-backend\\\\frontend-integration\\\\app\\\\error.tsx\",\n                            lineNumber: 74,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Pictures\\\\school_pro\\\\school-pro-backend\\\\frontend-integration\\\\app\\\\error.tsx\",\n                    lineNumber: 72,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Pictures\\\\school_pro\\\\school-pro-backend\\\\frontend-integration\\\\app\\\\error.tsx\",\n            lineNumber: 24,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Pictures\\\\school_pro\\\\school-pro-backend\\\\frontend-integration\\\\app\\\\error.tsx\",\n        lineNumber: 23,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(ssr)/./app/error.tsx\n");

/***/ }),

/***/ "(ssr)/./components/providers.tsx":
/*!**********************************!*\
  !*** ./components/providers.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Providers: () => (/* binding */ Providers)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @tanstack/react-query */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/queryClient.js\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @tanstack/react-query */ \"(ssr)/./node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js\");\n/* harmony import */ var _tanstack_react_query_devtools__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @tanstack/react-query-devtools */ \"(ssr)/./node_modules/@tanstack/react-query-devtools/build/modern/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_providers_AuthProvider__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/providers/AuthProvider */ \"(ssr)/./components/providers/AuthProvider.tsx\");\n/**\n * Providers Component\n *\n * Wraps the app with all necessary providers:\n * - React Query for data fetching\n * - Theme provider for dark/light mode\n * - Auth provider for authentication state\n */ /* __next_internal_client_entry_do_not_use__ Providers auto */ \n\n\n\n\nfunction Providers({ children }) {\n    // Create a new QueryClient instance\n    const [queryClient] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(()=>new _tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.QueryClient({\n            defaultOptions: {\n                queries: {\n                    // Stale time: 5 minutes\n                    staleTime: 5 * 60 * 1000,\n                    // Cache time: 10 minutes\n                    gcTime: 10 * 60 * 1000,\n                    // Retry failed requests 3 times\n                    retry: 3,\n                    // Retry delay with exponential backoff\n                    retryDelay: (attemptIndex)=>Math.min(1000 * 2 ** attemptIndex, 30000),\n                    // Refetch on window focus in production\n                    refetchOnWindowFocus: \"development\" === \"production\",\n                    // Don't refetch on reconnect in development\n                    refetchOnReconnect: \"development\" === \"production\"\n                },\n                mutations: {\n                    // Retry failed mutations once\n                    retry: 1,\n                    // Retry delay for mutations\n                    retryDelay: 1000\n                }\n            }\n        }));\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_tanstack_react_query__WEBPACK_IMPORTED_MODULE_4__.QueryClientProvider, {\n        client: queryClient,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_providers_AuthProvider__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n            children: [\n                children,\n                 true && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_tanstack_react_query_devtools__WEBPACK_IMPORTED_MODULE_5__.ReactQueryDevtools, {\n                    initialIsOpen: false\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Pictures\\\\school_pro\\\\school-pro-backend\\\\frontend-integration\\\\components\\\\providers.tsx\",\n                    lineNumber: 53,\n                    columnNumber: 52\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Pictures\\\\school_pro\\\\school-pro-backend\\\\frontend-integration\\\\components\\\\providers.tsx\",\n            lineNumber: 50,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Pictures\\\\school_pro\\\\school-pro-backend\\\\frontend-integration\\\\components\\\\providers.tsx\",\n        lineNumber: 49,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/providers.tsx\n");

/***/ }),

/***/ "(ssr)/./components/providers/AuthProvider.tsx":
/*!***********************************************!*\
  !*** ./components/providers/AuthProvider.tsx ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _hooks_useAuthInitializer__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/hooks/useAuthInitializer */ \"(ssr)/./hooks/useAuthInitializer.ts\");\n/**\n * Auth Provider Component\n * Initializes authentication state and provides loading states\n */ /* __next_internal_client_entry_do_not_use__ AuthProvider,default auto */ \n\n\nconst AuthProvider = ({ children })=>{\n    const { isInitialized, isLoading } = (0,_hooks_useAuthInitializer__WEBPACK_IMPORTED_MODULE_2__.useAuthInitializer)();\n    // Show loading spinner while auth is initializing\n    if (!isInitialized && isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Pictures\\\\school_pro\\\\school-pro-backend\\\\frontend-integration\\\\components\\\\providers\\\\AuthProvider.tsx\",\n                        lineNumber: 23,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: \"Initializing authentication...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Pictures\\\\school_pro\\\\school-pro-backend\\\\frontend-integration\\\\components\\\\providers\\\\AuthProvider.tsx\",\n                        lineNumber: 24,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Pictures\\\\school_pro\\\\school-pro-backend\\\\frontend-integration\\\\components\\\\providers\\\\AuthProvider.tsx\",\n                lineNumber: 22,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Pictures\\\\school_pro\\\\school-pro-backend\\\\frontend-integration\\\\components\\\\providers\\\\AuthProvider.tsx\",\n            lineNumber: 21,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: children\n    }, void 0, false);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (AuthProvider);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9jb21wb25lbnRzL3Byb3ZpZGVycy9BdXRoUHJvdmlkZXIudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7QUFBQTs7O0NBR0M7QUFJeUI7QUFDc0M7QUFNekQsTUFBTUUsZUFBNEMsQ0FBQyxFQUFFQyxRQUFRLEVBQUU7SUFDcEUsTUFBTSxFQUFFQyxhQUFhLEVBQUVDLFNBQVMsRUFBRSxHQUFHSiw2RUFBa0JBO0lBRXZELGtEQUFrRDtJQUNsRCxJQUFJLENBQUNHLGlCQUFpQkMsV0FBVztRQUMvQixxQkFDRSw4REFBQ0M7WUFBSUMsV0FBVTtzQkFDYiw0RUFBQ0Q7Z0JBQUlDLFdBQVU7O2tDQUNiLDhEQUFDRDt3QkFBSUMsV0FBVTs7Ozs7O2tDQUNmLDhEQUFDQzt3QkFBRUQsV0FBVTtrQ0FBZ0I7Ozs7Ozs7Ozs7Ozs7Ozs7O0lBSXJDO0lBRUEscUJBQU87a0JBQUdKOztBQUNaLEVBQUU7QUFFRixpRUFBZUQsWUFBWUEsRUFBQyIsInNvdXJjZXMiOlsid2VicGFjazovL3NjaG9vbC1tYW5hZ2VtZW50LWZyb250ZW5kLy4vY29tcG9uZW50cy9wcm92aWRlcnMvQXV0aFByb3ZpZGVyLnRzeD9jY2I4Il0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQXV0aCBQcm92aWRlciBDb21wb25lbnRcbiAqIEluaXRpYWxpemVzIGF1dGhlbnRpY2F0aW9uIHN0YXRlIGFuZCBwcm92aWRlcyBsb2FkaW5nIHN0YXRlc1xuICovXG5cbid1c2UgY2xpZW50JztcblxuaW1wb3J0IFJlYWN0IGZyb20gJ3JlYWN0JztcbmltcG9ydCB7IHVzZUF1dGhJbml0aWFsaXplciB9IGZyb20gJ0AvaG9va3MvdXNlQXV0aEluaXRpYWxpemVyJztcblxuaW50ZXJmYWNlIEF1dGhQcm92aWRlclByb3BzIHtcbiAgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZTtcbn1cblxuZXhwb3J0IGNvbnN0IEF1dGhQcm92aWRlcjogUmVhY3QuRkM8QXV0aFByb3ZpZGVyUHJvcHM+ID0gKHsgY2hpbGRyZW4gfSkgPT4ge1xuICBjb25zdCB7IGlzSW5pdGlhbGl6ZWQsIGlzTG9hZGluZyB9ID0gdXNlQXV0aEluaXRpYWxpemVyKCk7XG5cbiAgLy8gU2hvdyBsb2FkaW5nIHNwaW5uZXIgd2hpbGUgYXV0aCBpcyBpbml0aWFsaXppbmdcbiAgaWYgKCFpc0luaXRpYWxpemVkICYmIGlzTG9hZGluZykge1xuICAgIHJldHVybiAoXG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1pbi1oLXNjcmVlbiBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlclwiPlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyXCI+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJhbmltYXRlLXNwaW4gcm91bmRlZC1mdWxsIGgtOCB3LTggYm9yZGVyLWItMiBib3JkZXItYmx1ZS02MDAgbXgtYXV0byBtYi00XCI+PC9kaXY+XG4gICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTYwMFwiPkluaXRpYWxpemluZyBhdXRoZW50aWNhdGlvbi4uLjwvcD5cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L2Rpdj5cbiAgICApO1xuICB9XG5cbiAgcmV0dXJuIDw+e2NoaWxkcmVufTwvPjtcbn07XG5cbmV4cG9ydCBkZWZhdWx0IEF1dGhQcm92aWRlcjtcbiJdLCJuYW1lcyI6WyJSZWFjdCIsInVzZUF1dGhJbml0aWFsaXplciIsIkF1dGhQcm92aWRlciIsImNoaWxkcmVuIiwiaXNJbml0aWFsaXplZCIsImlzTG9hZGluZyIsImRpdiIsImNsYXNzTmFtZSIsInAiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./components/providers/AuthProvider.tsx\n");

/***/ }),

/***/ "(ssr)/./hooks/useAuthInitializer.ts":
/*!*************************************!*\
  !*** ./hooks/useAuthInitializer.ts ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   useAuthInitializer: () => (/* binding */ useAuthInitializer)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _stores_authStore__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/stores/authStore */ \"(ssr)/./stores/authStore.ts\");\n/**\n * Auth Initializer Hook\n * Automatically fetches user data when the app loads if a token exists\n */ /* __next_internal_client_entry_do_not_use__ useAuthInitializer,default auto */ \n\nconst useAuthInitializer = ()=>{\n    const { token, user, fetchUser, isLoading } = (0,_stores_authStore__WEBPACK_IMPORTED_MODULE_1__.useAuthStore)();\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        // If we have a token but no user data, fetch it\n        if (token && !user && !isLoading) {\n            console.log(\"\\uD83D\\uDD04 Auth initializer: Token found but no user data, fetching...\");\n            fetchUser();\n        }\n    }, [\n        token,\n        user,\n        fetchUser,\n        isLoading\n    ]);\n    // Also check localStorage on mount (in case store hasn't hydrated yet)\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        const checkStoredToken = ()=>{\n            if (true) return;\n            const storedToken = localStorage.getItem(\"access_token\" || 0);\n            const storedRole = localStorage.getItem(\"role\" || 0);\n            // If we have stored auth data but store is empty, restore it\n            if (storedToken && storedRole && !token) {\n                console.log(\"\\uD83D\\uDD04 Auth initializer: Restoring auth from localStorage\");\n                const { setAuth } = _stores_authStore__WEBPACK_IMPORTED_MODULE_1__.useAuthStore.getState();\n                setAuth({\n                    token: storedToken,\n                    role: storedRole\n                });\n            }\n        };\n        // Small delay to ensure store has hydrated\n        const timer = setTimeout(checkStoredToken, 100);\n        return ()=>clearTimeout(timer);\n    }, [\n        token\n    ]);\n    return {\n        isInitialized: !isLoading && (!!user || !token),\n        isLoading,\n        hasToken: !!token,\n        hasUser: !!user\n    };\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (useAuthInitializer);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./hooks/useAuthInitializer.ts\n");

/***/ }),

/***/ "(ssr)/./stores/authStore.ts":
/*!*****************************!*\
  !*** ./stores/authStore.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   canCreateTeacher: () => (/* binding */ canCreateTeacher),\n/* harmony export */   canDeleteTeacher: () => (/* binding */ canDeleteTeacher),\n/* harmony export */   canEditTeacher: () => (/* binding */ canEditTeacher),\n/* harmony export */   useAuth: () => (/* binding */ useAuth),\n/* harmony export */   useAuthStore: () => (/* binding */ useAuthStore)\n/* harmony export */ });\n/* harmony import */ var zustand__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! zustand */ \"(ssr)/./node_modules/zustand/esm/react.mjs\");\n/* harmony import */ var zustand_middleware__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! zustand/middleware */ \"(ssr)/./node_modules/zustand/esm/middleware.mjs\");\n/**\n * Authentication Store - Production-Grade Zustand with JWT + Cookie Support\n *\n * Features:\n * - JWT token management with persistence\n * - Role-based access control\n * - Cookie integration for SSR\n * - Clean API for auth state management\n */ /* __next_internal_client_entry_do_not_use__ useAuthStore,canCreateTeacher,canEditTeacher,canDeleteTeacher,useAuth auto */ \n\n// Cookie utilities for SSR compatibility\nconst cookieUtils = {\n    set: (name, value, days = 7)=>{\n        if (typeof document === \"undefined\") return;\n        const expires = new Date(Date.now() + days * 24 * 60 * 60 * 1000).toUTCString();\n        document.cookie = `${name}=${value}; expires=${expires}; path=/; SameSite=Lax`;\n    },\n    get: (name)=>{\n        if (typeof document === \"undefined\") return null;\n        const value = `; ${document.cookie}`;\n        const parts = value.split(`; ${name}=`);\n        if (parts.length === 2) return parts.pop()?.split(\";\").shift() || null;\n        return null;\n    },\n    remove: (name)=>{\n        if (typeof document === \"undefined\") return;\n        document.cookie = `${name}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;`;\n    }\n};\n// Create the auth store with persistence\nconst useAuthStore = (0,zustand__WEBPACK_IMPORTED_MODULE_0__.create)()((0,zustand_middleware__WEBPACK_IMPORTED_MODULE_1__.persist)((set, get)=>({\n        // Initial state\n        token: null,\n        role: null,\n        userId: null,\n        user: null,\n        isLoading: false,\n        // Set authentication data\n        setAuth: ({ token, role, userId, user })=>{\n            console.log(\"\\uD83D\\uDD10 AuthStore.setAuth called\", {\n                hasToken: !!token,\n                role,\n                userId\n            });\n            // Store in localStorage for API client\n            if (false) {}\n            // Store in cookie for SSR/middleware\n            cookieUtils.set(\"access_token\" || 0, token);\n            cookieUtils.set(\"role\" || 0, role || \"\");\n            // Update store state\n            set({\n                token,\n                role,\n                userId: userId || null,\n                user: user || null\n            });\n            console.log(\"✅ Auth data stored successfully\");\n            // Automatically fetch user data if not provided\n            if (!user && token) {\n                get().fetchUser();\n            }\n        },\n        // Fetch user data from backend\n        fetchUser: async ()=>{\n            const { token } = get();\n            if (!token) {\n                console.warn(\"⚠️ No token available for user fetch\");\n                return;\n            }\n            set({\n                isLoading: true\n            });\n            try {\n                console.log(\"\\uD83D\\uDD04 Fetching user data from backend...\");\n                // Use the backend API URL\n                const baseUrl = \"http://127.0.0.1:8000\" || 0;\n                const response = await fetch(`${baseUrl}/auth/me`, {\n                    method: \"GET\",\n                    headers: {\n                        Authorization: `Bearer ${token}`,\n                        \"Content-Type\": \"application/json\"\n                    }\n                });\n                if (response.ok) {\n                    const userData = await response.json();\n                    console.log(\"✅ User data fetched successfully:\", userData);\n                    set({\n                        user: userData,\n                        role: userData.role || get().role,\n                        userId: userData.id || get().userId,\n                        isLoading: false\n                    });\n                } else {\n                    console.error(\"❌ Failed to fetch user data:\", response.status, response.statusText);\n                    // If 401, clear auth data\n                    if (response.status === 401) {\n                        console.log(\"\\uD83D\\uDEAA Token invalid, clearing auth data\");\n                        get().clear();\n                    } else {\n                        set({\n                            isLoading: false\n                        });\n                    }\n                }\n            } catch (error) {\n                console.error(\"❌ Error fetching user data:\", error);\n                set({\n                    isLoading: false\n                });\n            }\n        },\n        // Clear authentication data\n        clear: ()=>{\n            console.log(\"\\uD83D\\uDEAA AuthStore.clear called\");\n            // Clear localStorage\n            if (false) {}\n            // Clear cookies\n            cookieUtils.remove(\"access_token\" || 0);\n            cookieUtils.remove(\"role\" || 0);\n            // Clear store state\n            set({\n                token: null,\n                role: null,\n                userId: null,\n                user: null,\n                isLoading: false\n            });\n            console.log(\"✅ Auth data cleared\");\n        },\n        // Role checking utilities\n        isAdmin: ()=>{\n            const { role } = get();\n            return role === \"ADMIN\" || role === \"SUPER_ADMIN\";\n        },\n        isSuperAdmin: ()=>{\n            const { role } = get();\n            return role === \"SUPER_ADMIN\";\n        }\n    }), {\n    name: \"auth\",\n    storage: (0,zustand_middleware__WEBPACK_IMPORTED_MODULE_1__.createJSONStorage)(()=>localStorage),\n    partialize: (state)=>({\n            token: state.token,\n            role: state.role,\n            userId: state.userId,\n            user: state.user\n        })\n}));\n// Permission utilities\nconst canCreateTeacher = (role)=>role === \"SUPER_ADMIN\" || role === \"ADMIN\";\nconst canEditTeacher = (role)=>role === \"SUPER_ADMIN\" || role === \"ADMIN\";\nconst canDeleteTeacher = (role)=>role === \"SUPER_ADMIN\" || role === \"ADMIN\";\n// Backward compatibility alias\nconst useAuth = useAuthStore;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./stores/authStore.ts\n");

/***/ }),

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"453da7028c0e\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9zY2hvb2wtbWFuYWdlbWVudC1mcm9udGVuZC8uL2FwcC9nbG9iYWxzLmNzcz81ZmQxIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiNDUzZGE3MDI4YzBlXCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/error.tsx":
/*!***********************!*\
  !*** ./app/error.tsx ***!
  \***********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\OneDrive\Pictures\school_pro\school-pro-backend\frontend-integration\app\error.tsx#default`));


/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata),\n/* harmony export */   viewport: () => (/* binding */ viewport)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _components_providers__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/providers */ \"(rsc)/./components/providers.tsx\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! sonner */ \"(rsc)/./node_modules/sonner/dist/index.mjs\");\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./app/globals.css\");\n\n\n\n\n\nconst metadata = {\n    title: \"School Management System\",\n    description: \"Comprehensive school management system for students, teachers, and administrators\",\n    keywords: [\n        \"school\",\n        \"management\",\n        \"education\",\n        \"students\",\n        \"teachers\",\n        \"administration\"\n    ],\n    authors: [\n        {\n            name: \"School Management Team\"\n        }\n    ]\n};\nconst viewport = {\n    width: \"device-width\",\n    initialScale: 1\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        suppressHydrationWarning: true,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: (next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4___default().className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_providers__WEBPACK_IMPORTED_MODULE_1__.Providers, {\n                children: [\n                    children,\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(sonner__WEBPACK_IMPORTED_MODULE_2__.Toaster, {\n                        position: \"top-right\",\n                        richColors: true,\n                        closeButton: true,\n                        toastOptions: {\n                            duration: 4000\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Pictures\\\\school_pro\\\\school-pro-backend\\\\frontend-integration\\\\app\\\\layout.tsx\",\n                        lineNumber: 27,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Pictures\\\\school_pro\\\\school-pro-backend\\\\frontend-integration\\\\app\\\\layout.tsx\",\n                lineNumber: 25,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Pictures\\\\school_pro\\\\school-pro-backend\\\\frontend-integration\\\\app\\\\layout.tsx\",\n            lineNumber: 24,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Pictures\\\\school_pro\\\\school-pro-backend\\\\frontend-integration\\\\app\\\\layout.tsx\",\n        lineNumber: 23,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvbGF5b3V0LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7OztBQU1NQTtBQU42QztBQUdsQjtBQUNWO0FBSWhCLE1BQU1HLFdBQXFCO0lBQ2hDQyxPQUFPO0lBQ1BDLGFBQWE7SUFDYkMsVUFBVTtRQUFDO1FBQVU7UUFBYztRQUFhO1FBQVk7UUFBWTtLQUFpQjtJQUN6RkMsU0FBUztRQUFDO1lBQUVDLE1BQU07UUFBeUI7S0FBRTtBQUMvQyxFQUFFO0FBRUssTUFBTUMsV0FBVztJQUN0QkMsT0FBTztJQUNQQyxjQUFjO0FBQ2hCLEVBQUU7QUFFYSxTQUFTQyxXQUFXLEVBQUVDLFFBQVEsRUFBaUM7SUFDNUUscUJBQ0UsOERBQUNDO1FBQUtDLE1BQUs7UUFBS0Msd0JBQXdCO2tCQUN0Qyw0RUFBQ0M7WUFBS0MsV0FBV2xCLDJKQUFlO3NCQUM5Qiw0RUFBQ0MsNERBQVNBOztvQkFDUFk7a0NBQ0QsOERBQUNYLDJDQUFPQTt3QkFDTmlCLFVBQVM7d0JBQ1RDLFVBQVU7d0JBQ1ZDLFdBQVc7d0JBQ1hDLGNBQWM7NEJBQ1pDLFVBQVU7d0JBQ1o7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFNWiIsInNvdXJjZXMiOlsid2VicGFjazovL3NjaG9vbC1tYW5hZ2VtZW50LWZyb250ZW5kLy4vYXBwL2xheW91dC50c3g/OTk4OCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBQcm92aWRlcnMgfSBmcm9tICdAL2NvbXBvbmVudHMvcHJvdmlkZXJzJztcbmltcG9ydCB0eXBlIHsgTWV0YWRhdGEgfSBmcm9tICduZXh0JztcbmltcG9ydCB7IEludGVyIH0gZnJvbSAnbmV4dC9mb250L2dvb2dsZSc7XG5pbXBvcnQgeyBUb2FzdGVyIH0gZnJvbSAnc29ubmVyJztcbmltcG9ydCAnLi9nbG9iYWxzLmNzcyc7XG5cbmNvbnN0IGludGVyID0gSW50ZXIoeyBzdWJzZXRzOiBbJ2xhdGluJ10gfSk7XG5cbmV4cG9ydCBjb25zdCBtZXRhZGF0YTogTWV0YWRhdGEgPSB7XG4gIHRpdGxlOiAnU2Nob29sIE1hbmFnZW1lbnQgU3lzdGVtJyxcbiAgZGVzY3JpcHRpb246ICdDb21wcmVoZW5zaXZlIHNjaG9vbCBtYW5hZ2VtZW50IHN5c3RlbSBmb3Igc3R1ZGVudHMsIHRlYWNoZXJzLCBhbmQgYWRtaW5pc3RyYXRvcnMnLFxuICBrZXl3b3JkczogWydzY2hvb2wnLCAnbWFuYWdlbWVudCcsICdlZHVjYXRpb24nLCAnc3R1ZGVudHMnLCAndGVhY2hlcnMnLCAnYWRtaW5pc3RyYXRpb24nXSxcbiAgYXV0aG9yczogW3sgbmFtZTogJ1NjaG9vbCBNYW5hZ2VtZW50IFRlYW0nIH1dLFxufTtcblxuZXhwb3J0IGNvbnN0IHZpZXdwb3J0ID0ge1xuICB3aWR0aDogJ2RldmljZS13aWR0aCcsXG4gIGluaXRpYWxTY2FsZTogMSxcbn07XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIFJvb3RMYXlvdXQoeyBjaGlsZHJlbiB9OiB7IGNoaWxkcmVuOiBSZWFjdC5SZWFjdE5vZGUgfSkge1xuICByZXR1cm4gKFxuICAgIDxodG1sIGxhbmc9J2VuJyBzdXBwcmVzc0h5ZHJhdGlvbldhcm5pbmc+XG4gICAgICA8Ym9keSBjbGFzc05hbWU9e2ludGVyLmNsYXNzTmFtZX0+XG4gICAgICAgIDxQcm92aWRlcnM+XG4gICAgICAgICAge2NoaWxkcmVufVxuICAgICAgICAgIDxUb2FzdGVyXG4gICAgICAgICAgICBwb3NpdGlvbj0ndG9wLXJpZ2h0J1xuICAgICAgICAgICAgcmljaENvbG9yc1xuICAgICAgICAgICAgY2xvc2VCdXR0b25cbiAgICAgICAgICAgIHRvYXN0T3B0aW9ucz17e1xuICAgICAgICAgICAgICBkdXJhdGlvbjogNDAwMCxcbiAgICAgICAgICAgIH19XG4gICAgICAgICAgLz5cbiAgICAgICAgPC9Qcm92aWRlcnM+XG4gICAgICA8L2JvZHk+XG4gICAgPC9odG1sPlxuICApO1xufVxuIl0sIm5hbWVzIjpbImludGVyIiwiUHJvdmlkZXJzIiwiVG9hc3RlciIsIm1ldGFkYXRhIiwidGl0bGUiLCJkZXNjcmlwdGlvbiIsImtleXdvcmRzIiwiYXV0aG9ycyIsIm5hbWUiLCJ2aWV3cG9ydCIsIndpZHRoIiwiaW5pdGlhbFNjYWxlIiwiUm9vdExheW91dCIsImNoaWxkcmVuIiwiaHRtbCIsImxhbmciLCJzdXBwcmVzc0h5ZHJhdGlvbldhcm5pbmciLCJib2R5IiwiY2xhc3NOYW1lIiwicG9zaXRpb24iLCJyaWNoQ29sb3JzIiwiY2xvc2VCdXR0b24iLCJ0b2FzdE9wdGlvbnMiLCJkdXJhdGlvbiJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./app/loading.tsx":
/*!*************************!*\
  !*** ./app/loading.tsx ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Loading)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/**\n * Global Loading Component\n * \n * This loading component is shown while pages are loading\n * Provides a consistent loading experience across the app\n */ \nfunction Loading() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen flex items-center justify-center bg-gray-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-center\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mx-auto h-16 w-16 bg-blue-600 rounded-full flex items-center justify-center mb-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-white font-bold text-2xl\",\n                        children: \"\\uD83C\\uDF93\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Pictures\\\\school_pro\\\\school-pro-backend\\\\frontend-integration\\\\app\\\\loading.tsx\",\n                        lineNumber: 13,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Pictures\\\\school_pro\\\\school-pro-backend\\\\frontend-integration\\\\app\\\\loading.tsx\",\n                    lineNumber: 12,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Pictures\\\\school_pro\\\\school-pro-backend\\\\frontend-integration\\\\app\\\\loading.tsx\",\n                    lineNumber: 17,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                    className: \"text-xl font-semibold text-gray-800 mb-2\",\n                    children: \"School Management System\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Pictures\\\\school_pro\\\\school-pro-backend\\\\frontend-integration\\\\app\\\\loading.tsx\",\n                    lineNumber: 20,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-gray-600\",\n                    children: \"Loading...\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Pictures\\\\school_pro\\\\school-pro-backend\\\\frontend-integration\\\\app\\\\loading.tsx\",\n                    lineNumber: 23,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Pictures\\\\school_pro\\\\school-pro-backend\\\\frontend-integration\\\\app\\\\loading.tsx\",\n            lineNumber: 10,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Pictures\\\\school_pro\\\\school-pro-backend\\\\frontend-integration\\\\app\\\\loading.tsx\",\n        lineNumber: 9,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvbG9hZGluZy50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQUE7Ozs7O0NBS0M7QUFDYyxTQUFTQTtJQUN0QixxQkFDRSw4REFBQ0M7UUFBSUMsV0FBVTtrQkFDYiw0RUFBQ0Q7WUFBSUMsV0FBVTs7OEJBRWIsOERBQUNEO29CQUFJQyxXQUFVOzhCQUNiLDRFQUFDQzt3QkFBS0QsV0FBVTtrQ0FBZ0M7Ozs7Ozs7Ozs7OzhCQUlsRCw4REFBQ0Q7b0JBQUlDLFdBQVU7Ozs7Ozs4QkFHZiw4REFBQ0U7b0JBQUdGLFdBQVU7OEJBQTJDOzs7Ozs7OEJBR3pELDhEQUFDRztvQkFBRUgsV0FBVTs4QkFBZ0I7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBSXJDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vc2Nob29sLW1hbmFnZW1lbnQtZnJvbnRlbmQvLi9hcHAvbG9hZGluZy50c3g/YzUyYSJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEdsb2JhbCBMb2FkaW5nIENvbXBvbmVudFxuICogXG4gKiBUaGlzIGxvYWRpbmcgY29tcG9uZW50IGlzIHNob3duIHdoaWxlIHBhZ2VzIGFyZSBsb2FkaW5nXG4gKiBQcm92aWRlcyBhIGNvbnNpc3RlbnQgbG9hZGluZyBleHBlcmllbmNlIGFjcm9zcyB0aGUgYXBwXG4gKi9cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIExvYWRpbmcoKSB7XG4gIHJldHVybiAoXG4gICAgPGRpdiBjbGFzc05hbWU9XCJtaW4taC1zY3JlZW4gZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgYmctZ3JheS01MFwiPlxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlclwiPlxuICAgICAgICB7LyogU2Nob29sIGxvZ28vaWNvbiAqL31cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJteC1hdXRvIGgtMTYgdy0xNiBiZy1ibHVlLTYwMCByb3VuZGVkLWZ1bGwgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgbWItNFwiPlxuICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtd2hpdGUgZm9udC1ib2xkIHRleHQtMnhsXCI+8J+Okzwvc3Bhbj5cbiAgICAgICAgPC9kaXY+XG4gICAgICAgIFxuICAgICAgICB7LyogTG9hZGluZyBzcGlubmVyICovfVxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImFuaW1hdGUtc3BpbiByb3VuZGVkLWZ1bGwgaC0xMiB3LTEyIGJvcmRlci1iLTIgYm9yZGVyLWJsdWUtNjAwIG14LWF1dG8gbWItNFwiPjwvZGl2PlxuICAgICAgICBcbiAgICAgICAgey8qIExvYWRpbmcgdGV4dCAqL31cbiAgICAgICAgPGgyIGNsYXNzTmFtZT1cInRleHQteGwgZm9udC1zZW1pYm9sZCB0ZXh0LWdyYXktODAwIG1iLTJcIj5cbiAgICAgICAgICBTY2hvb2wgTWFuYWdlbWVudCBTeXN0ZW1cbiAgICAgICAgPC9oMj5cbiAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTYwMFwiPkxvYWRpbmcuLi48L3A+XG4gICAgICA8L2Rpdj5cbiAgICA8L2Rpdj5cbiAgKTtcbn1cbiJdLCJuYW1lcyI6WyJMb2FkaW5nIiwiZGl2IiwiY2xhc3NOYW1lIiwic3BhbiIsImgyIiwicCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./app/loading.tsx\n");

/***/ }),

/***/ "(rsc)/./app/not-found.tsx":
/*!***************************!*\
  !*** ./app/not-found.tsx ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ NotFound)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(rsc)/./node_modules/next/dist/api/link.js\");\n\n\n/**\n * Global 404 Not Found Page\n * \n * This page is shown when a route doesn't exist\n * Provides helpful navigation options to get users back on track\n */ function NotFound() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen flex items-center justify-center bg-gray-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-center max-w-md mx-auto p-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mx-auto h-24 w-24 bg-gray-100 rounded-full flex items-center justify-center mb-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-gray-400 text-4xl\",\n                        children: \"\\uD83D\\uDCDA\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Pictures\\\\school_pro\\\\school-pro-backend\\\\frontend-integration\\\\app\\\\not-found.tsx\",\n                        lineNumber: 15,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Pictures\\\\school_pro\\\\school-pro-backend\\\\frontend-integration\\\\app\\\\not-found.tsx\",\n                    lineNumber: 14,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                    className: \"text-6xl font-bold text-gray-800 mb-4\",\n                    children: \"404\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Pictures\\\\school_pro\\\\school-pro-backend\\\\frontend-integration\\\\app\\\\not-found.tsx\",\n                    lineNumber: 19,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                    className: \"text-2xl font-semibold text-gray-700 mb-4\",\n                    children: \"Page Not Found\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Pictures\\\\school_pro\\\\school-pro-backend\\\\frontend-integration\\\\app\\\\not-found.tsx\",\n                    lineNumber: 20,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-gray-600 mb-8\",\n                    children: \"The page you're looking for doesn't exist or has been moved. Let's get you back to where you need to be.\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Pictures\\\\school_pro\\\\school-pro-backend\\\\frontend-integration\\\\app\\\\not-found.tsx\",\n                    lineNumber: 24,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col sm:flex-row gap-3 justify-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                            href: \"/\",\n                            className: \"px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\",\n                            children: \"Go Home\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Pictures\\\\school_pro\\\\school-pro-backend\\\\frontend-integration\\\\app\\\\not-found.tsx\",\n                            lineNumber: 31,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                            href: \"/dashboard\",\n                            className: \"px-6 py-3 bg-gray-200 text-gray-800 rounded-lg hover:bg-gray-300 transition-colors\",\n                            children: \"Dashboard\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Pictures\\\\school_pro\\\\school-pro-backend\\\\frontend-integration\\\\app\\\\not-found.tsx\",\n                            lineNumber: 38,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Pictures\\\\school_pro\\\\school-pro-backend\\\\frontend-integration\\\\app\\\\not-found.tsx\",\n                    lineNumber: 30,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-8 pt-6 border-t border-gray-200\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm text-gray-500 mb-3\",\n                            children: \"Quick Links:\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Pictures\\\\school_pro\\\\school-pro-backend\\\\frontend-integration\\\\app\\\\not-found.tsx\",\n                            lineNumber: 48,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-wrap justify-center gap-4 text-sm\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                    href: \"/dashboard/students\",\n                                    className: \"text-blue-600 hover:underline\",\n                                    children: \"Students\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Pictures\\\\school_pro\\\\school-pro-backend\\\\frontend-integration\\\\app\\\\not-found.tsx\",\n                                    lineNumber: 50,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                    href: \"/dashboard/teachers\",\n                                    className: \"text-blue-600 hover:underline\",\n                                    children: \"Teachers\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Pictures\\\\school_pro\\\\school-pro-backend\\\\frontend-integration\\\\app\\\\not-found.tsx\",\n                                    lineNumber: 53,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                    href: \"/dashboard/classes\",\n                                    className: \"text-blue-600 hover:underline\",\n                                    children: \"Classes\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Pictures\\\\school_pro\\\\school-pro-backend\\\\frontend-integration\\\\app\\\\not-found.tsx\",\n                                    lineNumber: 56,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                    href: \"/dashboard/attendance\",\n                                    className: \"text-blue-600 hover:underline\",\n                                    children: \"Attendance\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Pictures\\\\school_pro\\\\school-pro-backend\\\\frontend-integration\\\\app\\\\not-found.tsx\",\n                                    lineNumber: 59,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Pictures\\\\school_pro\\\\school-pro-backend\\\\frontend-integration\\\\app\\\\not-found.tsx\",\n                            lineNumber: 49,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Pictures\\\\school_pro\\\\school-pro-backend\\\\frontend-integration\\\\app\\\\not-found.tsx\",\n                    lineNumber: 47,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Pictures\\\\school_pro\\\\school-pro-backend\\\\frontend-integration\\\\app\\\\not-found.tsx\",\n            lineNumber: 12,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Pictures\\\\school_pro\\\\school-pro-backend\\\\frontend-integration\\\\app\\\\not-found.tsx\",\n        lineNumber: 11,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/not-found.tsx\n");

/***/ }),

/***/ "(rsc)/./components/providers.tsx":
/*!**********************************!*\
  !*** ./components/providers.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Providers: () => (/* binding */ e0)\n/* harmony export */ });\n/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js\");\n\n\nconst e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\\Users\\<USER>\\OneDrive\\Pictures\\school_pro\\school-pro-backend\\frontend-integration\\components\\providers.tsx#Providers`);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./components/providers.tsx\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/@tanstack","vendor-chunks/next","vendor-chunks/sonner","vendor-chunks/zustand","vendor-chunks/@swc"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=private-next-app-dir%2Fnot-found.tsx&appDir=C%3A%5CUsers%5CImran%20Bhai%5COneDrive%5CPictures%5Cschool_pro%5Cschool-pro-backend%5Cfrontend-integration%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CImran%20Bhai%5COneDrive%5CPictures%5Cschool_pro%5Cschool-pro-backend%5Cfrontend-integration&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();