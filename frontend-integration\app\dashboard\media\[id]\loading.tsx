import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';

export default function MediaDetailLoading() {
  return (
    <div className='container mx-auto p-4 sm:p-6 lg:p-8 max-w-4xl'>
      {/* Header Skeleton */}
      <div className='flex items-center justify-between mb-8'>
        <div className='flex items-center gap-4'>
          <Skeleton className='h-9 w-32' />
          <div>
            <Skeleton className='h-8 w-64 mb-2' />
            <Skeleton className='h-4 w-48' />
          </div>
        </div>
        <div className='flex gap-2'>
          <Skeleton className='h-10 w-24' />
          <Skeleton className='h-10 w-32' />
        </div>
      </div>

      {/* Content Skeleton */}
      <div className='grid grid-cols-1 lg:grid-cols-3 gap-8'>
        <div className='lg:col-span-2'>
          <Card>
            <CardHeader>
              <div className='space-y-4'>
                <Skeleton className='h-8 w-3/4' />
                <div className='flex gap-2'>
                  <Skeleton className='h-6 w-16' />
                  <Skeleton className='h-6 w-16' />
                  <Skeleton className='h-6 w-16' />
                </div>
              </div>
            </CardHeader>
            <CardContent className='space-y-6'>
              <div className='border-2 border-dashed border-gray-200 rounded-lg p-8'>
                <Skeleton className='h-32 w-full' />
              </div>
              <div>
                <Skeleton className='h-5 w-20 mb-2' />
                <Skeleton className='h-4 w-full mb-2' />
                <Skeleton className='h-4 w-3/4' />
              </div>
              <div className='grid grid-cols-2 gap-4'>
                <Skeleton className='h-4 w-32' />
                <Skeleton className='h-4 w-32' />
                <Skeleton className='h-4 w-32' />
                <Skeleton className='h-4 w-32' />
              </div>
            </CardContent>
          </Card>
        </div>

        <div className='space-y-6'>
          <Card>
            <CardHeader>
              <Skeleton className='h-6 w-32' />
            </CardHeader>
            <CardContent className='space-y-3'>
              <div>
                <Skeleton className='h-4 w-16 mb-1' />
                <Skeleton className='h-4 w-32' />
              </div>
              <div>
                <Skeleton className='h-4 w-12 mb-1' />
                <Skeleton className='h-4 w-20' />
              </div>
              <div>
                <Skeleton className='h-4 w-16 mb-1' />
                <Skeleton className='h-4 w-24' />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <Skeleton className='h-6 w-20' />
            </CardHeader>
            <CardContent className='space-y-3'>
              <div>
                <Skeleton className='h-4 w-16 mb-1' />
                <Skeleton className='h-4 w-32' />
              </div>
              <div>
                <Skeleton className='h-4 w-20 mb-1' />
                <Skeleton className='h-4 w-32' />
              </div>
              <div>
                <Skeleton className='h-4 w-16 mb-1' />
                <Skeleton className='h-4 w-8' />
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
