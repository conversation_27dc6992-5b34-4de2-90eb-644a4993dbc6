'use client';

import { Arrow<PERSON>ef<PERSON>, AlertTriangle } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { useEffect } from 'react';

import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { StudentForm } from '@/components/forms/StudentForm';
import { useStudent, useUpdateStudent } from '@/hooks/useStudents';
import { useAuth } from '@/hooks/useAuth';
import { useToast } from '@/hooks/use-toast';
import { canEditStudent } from '@/lib/permissions';
import type { StudentUpdate } from '@/schemas/zodSchemas';

export default function EditStudentPage({ params }: { params: { id: string } }) {
  const router = useRouter();
  const { user } = useAuth();
  const { toast } = useToast();
  const { data: studentData, isLoading: isLoadingStudent } = useStudent(params.id);
  const updateStudentMutation = useUpdateStudent();

  // Check permissions
  useEffect(() => {
    if (!canEditStudent(user?.role)) {
      toast({
        title: 'Access Denied',
        description: 'Only administrators can edit students',
        variant: 'destructive',
      });
      router.push('/dashboard/students');
    }
  }, [user?.role, router, toast]);

  const handleSubmit = async (data: StudentUpdate) => {
    try {
      await updateStudentMutation.mutateAsync({ id: params.id, data });
      toast({
        title: 'Success',
        description: 'Student updated successfully',
      });
      router.push(`/dashboard/students/${params.id}`);
    } catch (error) {
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to update student',
        variant: 'destructive',
      });
    }
  };

  const handleCancel = () => {
    router.push(`/dashboard/students/${params.id}`);
  };

  if (isLoadingStudent) {
    return (
      <div className='container mx-auto p-4 sm:p-6 lg:p-8'>
        <div className='max-w-4xl mx-auto'>
          <div className='animate-pulse space-y-8'>
            <div className='h-8 bg-gray-200 rounded w-1/4'></div>
            <div className='h-64 bg-gray-200 rounded'></div>
            <div className='h-96 bg-gray-200 rounded'></div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className='container mx-auto p-4 sm:p-6 lg:p-8 space-y-8'>
      {/* Header */}
      <div className='flex items-center justify-between'>
        <div className='flex items-center space-x-4'>
          <Button variant='outline' size='sm' onClick={handleCancel}>
            <ArrowLeft className='mr-2 h-4 w-4' />
            Back
          </Button>
          <div>
            <h1 className='text-2xl font-bold text-gray-900'>Edit Student</h1>
            <p className='text-gray-600'>Update student information</p>
          </div>
        </div>
      </div>

      {/* Student Form */}
      <StudentForm
        mode='edit'
        initialData={studentData}
        onSubmit={handleSubmit}
        onCancel={handleCancel}
        isLoading={updateStudentMutation.isPending}
      />
    </div>
  );
}
