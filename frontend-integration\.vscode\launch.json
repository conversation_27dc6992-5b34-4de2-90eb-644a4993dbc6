{"version": "0.2.0", "configurations": [{"name": "Next.js: debug server-side", "type": "node", "request": "attach", "port": 9229, "skipFiles": ["<node_internals>/**"], "localRoot": "${workspaceFolder}", "remoteRoot": "${workspaceFolder}", "outputCapture": "std"}, {"name": "Next.js: debug client-side", "type": "chrome", "request": "launch", "url": "http://localhost:3000", "webRoot": "${workspaceFolder}", "skipFiles": ["<node_internals>/**", "node_modules/**"], "sourceMapPathOverrides": {"webpack://_N_E/*": "${webRoot}/*", "webpack:///./*": "${webRoot}/*", "webpack:///./~/*": "${webRoot}/node_modules/*", "webpack://?:*/*": "${webRoot}/*"}}, {"name": "Next.js: debug full stack", "type": "node", "request": "launch", "program": "${workspaceFolder}/node_modules/.bin/next", "args": ["dev"], "cwd": "${workspaceFolder}", "console": "integratedTerminal", "skipFiles": ["<node_internals>/**"], "env": {"NODE_OPTIONS": "--inspect"}}, {"name": "Launch Chrome against localhost", "type": "chrome", "request": "launch", "url": "http://localhost:3000", "webRoot": "${workspaceFolder}", "userDataDir": "${workspaceFolder}/.vscode/chrome-debug-profile", "runtimeArgs": ["--disable-web-security", "--disable-features=VizDisplayCompositor"]}, {"name": "Attach to Chrome", "type": "chrome", "request": "attach", "port": 9222, "webRoot": "${workspaceFolder}", "urlFilter": "http://localhost:3000/*"}, {"name": "Debug Jest Tests", "type": "node", "request": "launch", "program": "${workspaceFolder}/node_modules/.bin/jest", "args": ["--runInBand", "--no-cache", "--watchAll=false"], "cwd": "${workspaceFolder}", "console": "integratedTerminal", "internalConsoleOptions": "neverOpen", "skipFiles": ["<node_internals>/**", "node_modules/**"]}, {"name": "Debug Current Jest Test", "type": "node", "request": "launch", "program": "${workspaceFolder}/node_modules/.bin/jest", "args": ["--runInBand", "--no-cache", "--watchAll=false", "${relativeFile}"], "cwd": "${workspaceFolder}", "console": "integratedTerminal", "internalConsoleOptions": "neverOpen", "skipFiles": ["<node_internals>/**", "node_modules/**"]}, {"name": "Debug Playwright Tests", "type": "node", "request": "launch", "program": "${workspaceFolder}/node_modules/.bin/playwright", "args": ["test", "--debug"], "cwd": "${workspaceFolder}", "console": "integratedTerminal", "skipFiles": ["<node_internals>/**"]}], "compounds": [{"name": "Next.js: debug full stack (compound)", "configurations": ["Next.js: debug server-side", "Next.js: debug client-side"], "stopAll": true}]}