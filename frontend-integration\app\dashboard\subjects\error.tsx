'use client';

import { ModuleError } from '@/components/ui/module-error';

interface SubjectsErrorProps {
  error: Error & { digest?: string };
  reset: () => void;
}

export default function SubjectsError({ error, reset }: SubjectsErrorProps) {
  return (
    <ModuleError
      error={error}
      reset={reset}
      moduleName="Subjects"
      moduleIcon="📚"
      backHref="/dashboard"
    />
  );
}
