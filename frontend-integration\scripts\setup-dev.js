#!/usr/bin/env node

/**
 * Development Environment Setup Script
 * 
 * Automates the setup of the development environment including:
 * - Environment file creation
 * - Dependency installation
 * - Git hooks setup
 * - VSCode extensions check
 * - Initial quality checks
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function logStep(step, message) {
  log(`\n[${step}] ${message}`, 'cyan');
}

function logSuccess(message) {
  log(`✅ ${message}`, 'green');
}

function logWarning(message) {
  log(`⚠️  ${message}`, 'yellow');
}

function logError(message) {
  log(`❌ ${message}`, 'red');
}

function execCommand(command, options = {}) {
  try {
    const result = execSync(command, { 
      stdio: 'inherit', 
      encoding: 'utf8',
      ...options 
    });
    return result;
  } catch (error) {
    logError(`Failed to execute: ${command}`);
    throw error;
  }
}

function checkNodeVersion() {
  logStep('1', 'Checking Node.js version...');
  
  const nodeVersion = process.version;
  const majorVersion = parseInt(nodeVersion.slice(1).split('.')[0]);
  
  if (majorVersion < 18) {
    logError(`Node.js ${nodeVersion} is not supported. Please upgrade to Node.js 18 or later.`);
    process.exit(1);
  }
  
  logSuccess(`Node.js ${nodeVersion} is supported`);
}

function checkPackageManager() {
  logStep('2', 'Checking package manager...');
  
  try {
    execCommand('npm --version', { stdio: 'pipe' });
    logSuccess('npm is available');
    return 'npm';
  } catch (error) {
    logError('npm is not available');
    process.exit(1);
  }
}

function createEnvironmentFile() {
  logStep('3', 'Setting up environment file...');
  
  const envPath = '.env.local';
  const envExamplePath = '.env.example';
  
  if (fs.existsSync(envPath)) {
    logWarning('.env.local already exists, skipping creation');
    return;
  }
  
  let envContent = '';
  
  if (fs.existsSync(envExamplePath)) {
    envContent = fs.readFileSync(envExamplePath, 'utf8');
    logSuccess('Using .env.example as template');
  } else {
    // Default environment configuration
    envContent = `# App Configuration
NEXT_PUBLIC_APP_URL=http://localhost:3000
NEXT_PUBLIC_APP_NAME="School Management System"

# API Configuration
NEXT_PUBLIC_API_URL=http://localhost:8000/api
NEXT_PUBLIC_USE_DUMMY_DATA=true

# Authentication
NEXTAUTH_SECRET=your-secret-key-change-this-in-production
NEXTAUTH_URL=http://localhost:3000

# Database (if using real backend)
# DATABASE_URL=postgresql://user:password@localhost:5432/school_db

# Optional: Analytics and monitoring
# NEXT_PUBLIC_GOOGLE_ANALYTICS_ID=
# SENTRY_DSN=

# Development
NODE_ENV=development
`;
    logSuccess('Created default environment configuration');
  }
  
  fs.writeFileSync(envPath, envContent);
  logSuccess('.env.local created successfully');
}

function installDependencies(packageManager) {
  logStep('4', 'Installing dependencies...');
  
  try {
    execCommand(`${packageManager} install`);
    logSuccess('Dependencies installed successfully');
  } catch (error) {
    logError('Failed to install dependencies');
    throw error;
  }
}

function runQualityChecks() {
  logStep('5', 'Running initial quality checks...');
  
  try {
    log('Running TypeScript check...', 'blue');
    execCommand('npm run type-check');
    logSuccess('TypeScript check passed');
    
    log('Running ESLint...', 'blue');
    execCommand('npm run lint');
    logSuccess('ESLint check passed');
    
    log('Running Prettier check...', 'blue');
    execCommand('npm run format:check');
    logSuccess('Prettier check passed');
    
  } catch (error) {
    logWarning('Some quality checks failed. Run "npm run quality:fix" to fix automatically.');
  }
}

function checkVSCodeExtensions() {
  logStep('6', 'Checking VSCode setup...');
  
  const extensionsPath = '.vscode/extensions.json';
  
  if (!fs.existsSync(extensionsPath)) {
    logWarning('VSCode extensions.json not found');
    return;
  }
  
  try {
    const extensions = JSON.parse(fs.readFileSync(extensionsPath, 'utf8'));
    const recommendedCount = extensions.recommendations?.length || 0;
    
    logSuccess(`Found ${recommendedCount} recommended VSCode extensions`);
    log('Install them by opening VSCode and running: Extensions: Show Recommended Extensions', 'blue');
  } catch (error) {
    logWarning('Could not parse VSCode extensions.json');
  }
}

function setupGitHooks() {
  logStep('7', 'Setting up Git hooks...');
  
  try {
    // Check if husky is available
    execCommand('npx husky --version', { stdio: 'pipe' });
    
    // Setup husky
    execCommand('npx husky install');
    
    // Add pre-commit hook
    const preCommitHook = `#!/usr/bin/env sh
. "$(dirname -- "$0")/_/husky.sh"

npm run pre-commit
`;
    
    const hooksDir = '.husky';
    if (!fs.existsSync(hooksDir)) {
      fs.mkdirSync(hooksDir, { recursive: true });
    }
    
    fs.writeFileSync(path.join(hooksDir, 'pre-commit'), preCommitHook);
    execCommand('chmod +x .husky/pre-commit');
    
    logSuccess('Git hooks setup successfully');
  } catch (error) {
    logWarning('Git hooks setup skipped (husky not available)');
  }
}

function printNextSteps() {
  logStep('8', 'Setup complete! Next steps:');
  
  log('\n🚀 Start development:', 'bright');
  log('   npm run dev', 'green');
  
  log('\n🔧 Available commands:', 'bright');
  log('   npm run dev:debug     # Start with debugger', 'blue');
  log('   npm run quality:check # Run all quality checks', 'blue');
  log('   npm run quality:fix   # Fix all quality issues', 'blue');
  log('   npm run test          # Run tests', 'blue');
  
  log('\n📝 VSCode setup:', 'bright');
  log('   1. Open project in VSCode', 'blue');
  log('   2. Install recommended extensions when prompted', 'blue');
  log('   3. Enjoy auto-formatting and linting!', 'blue');
  
  log('\n🌐 Access the application:', 'bright');
  log('   http://localhost:3000', 'green');
  
  log('\n📚 Documentation:', 'bright');
  log('   README.md - Complete project documentation', 'blue');
  log('   /docs     - Additional documentation (if available)', 'blue');
  
  log('\n🎯 Demo credentials (dummy data mode):', 'bright');
  log('   Email: <EMAIL>', 'green');
  log('   Password: admin123', 'green');
}

async function main() {
  log('🎓 School Management System - Development Setup', 'bright');
  log('================================================', 'bright');
  
  try {
    checkNodeVersion();
    const packageManager = checkPackageManager();
    createEnvironmentFile();
    installDependencies(packageManager);
    runQualityChecks();
    checkVSCodeExtensions();
    setupGitHooks();
    printNextSteps();
    
    log('\n🎉 Development environment setup completed successfully!', 'green');
    
  } catch (error) {
    logError('\nSetup failed. Please check the errors above and try again.');
    process.exit(1);
  }
}

// Run the setup
if (require.main === module) {
  main();
}

module.exports = {
  main,
  checkNodeVersion,
  createEnvironmentFile,
  installDependencies,
  runQualityChecks,
};
