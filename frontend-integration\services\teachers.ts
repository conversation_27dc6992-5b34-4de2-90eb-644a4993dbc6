// services/teachers.ts - Robust teachers service with proper auth
import { api } from '@/api/apiClient';
import axios from 'axios';

export interface Teacher {
  id: string;
  name?: string;
  full_name?: string;
  surname?: string;
  email: string;
  subject?: string;
  department?: string;
  status?: string;
  created_at?: string;
  updated_at?: string;
}

export async function fetchTeachers(): Promise<Teacher[]> {
  try {
    console.log('[Teachers] Fetching teachers list...');
    const res = await api.get('/api/v1/teachers/'); // Full FastAPI endpoint

    console.log('[Teachers] Response:', res.status, res.data);

    // Handle different response formats
    if (Array.isArray(res.data)) {
      return res.data;
    } else if (res.data?.data && Array.isArray(res.data.data)) {
      return res.data.data; // Handle paginated response
    } else if (res.data?.teachers && Array.isArray(res.data.teachers)) {
      return res.data.teachers; // Handle wrapped response
    }

    console.warn('[Teachers] Unexpected response format:', res.data);
    return [];
  } catch (err) {
    if (axios.isAxiosError(err)) {
      const status = err.response?.status;
      console.error('[Teachers] API Error:', status, err.response?.data);

      if (status === 404) {
        // Backend returns 404 when no records → treat as empty
        console.log('[Teachers] 404 treated as empty list');
        return [];
      } else if (status === 401) {
        console.error('[Teachers] Unauthorized - check token');
        throw new Error('Authentication required. Please login again.');
      } else if (status === 403) {
        console.error('[Teachers] Forbidden - insufficient permissions');
        throw new Error("You don't have permission to view teachers.");
      }
    }

    console.error('[Teachers] Fetch failed:', err);
    throw new Error(err instanceof Error ? err.message : 'Failed to fetch teachers');
  }
}

export async function createTeacher(teacherData: Partial<Teacher>): Promise<Teacher> {
  try {
    console.log('[Teachers] Creating teacher:', teacherData);
    const res = await api.post('/api/v1/teachers/', teacherData);
    console.log('[Teachers] Created:', res.status, res.data);
    return res.data;
  } catch (err) {
    console.error('[Teachers] Create failed:', err);
    throw new Error(err instanceof Error ? err.message : 'Failed to create teacher');
  }
}

export async function updateTeacher(id: string, teacherData: Partial<Teacher>): Promise<Teacher> {
  try {
    console.log('[Teachers] Updating teacher:', id, teacherData);
    const res = await api.put(`/api/v1/teachers/${id}`, teacherData);
    console.log('[Teachers] Updated:', res.status, res.data);
    return res.data;
  } catch (err) {
    console.error('[Teachers] Update failed:', err);
    throw new Error(err instanceof Error ? err.message : 'Failed to update teacher');
  }
}

export async function deleteTeacher(id: string): Promise<void> {
  try {
    console.log('[Teachers] Deleting teacher:', id);
    const res = await api.delete(`/api/v1/teachers/${id}`);
    console.log('[Teachers] Deleted:', res.status);
  } catch (err) {
    console.error('[Teachers] Delete failed:', err);
    throw new Error(err instanceof Error ? err.message : 'Failed to delete teacher');
  }
}

export async function fetchTeacherById(id: string): Promise<Teacher> {
  try {
    console.log('[Teachers] Fetching teacher:', id);
    const res = await api.get(`/api/v1/teachers/${id}`);
    console.log('[Teachers] Fetched:', res.status, res.data);
    return res.data;
  } catch (err) {
    console.error('[Teachers] Fetch by ID failed:', err);
    throw new Error(err instanceof Error ? err.message : 'Failed to fetch teacher');
  }
}
