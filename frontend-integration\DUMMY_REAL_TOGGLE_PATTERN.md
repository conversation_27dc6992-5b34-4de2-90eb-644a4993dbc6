# 💾 Dummy + Real API Toggle Pattern

## 🎯 Overview
This pattern allows seamless switching between dummy data (for development) and real API calls (for production) in every module page.

## 📋 Implementation Pattern

### 1. **Basic Structure**
```typescript
import { useQueryBase } from "@/frontend-integration/hooks/useQueryBase";
import { apiClient } from "@/frontend-integration/api/apiService";

// Dummy data for development
const dummyData = [
  // ... realistic mock data
];

// Toggle flag
const USE_DUMMY_DATA = true; // TODO: Set to false when backend is ready

// Fetch function with toggle
const fetchData = async () => {
  if (USE_DUMMY_DATA) {
    await new Promise(resolve => setTimeout(resolve, 1000)); // Simulate delay
    return dummyData;
  }
  
  const { data } = await apiClient.get("/endpoint");
  return data;
};

// Component using the pattern
export default function ModulePage() {
  const { data = [], isLoading, error } = useQueryBase(["module"], fetchData);
  
  // Handle states...
  return (
    // JSX with data
  );
}
```

### 2. **Complete Example Templates**

#### **Teachers Module**
```typescript
// Dummy data
const dummyTeachers = [
  {
    id: "1",
    name: "Sarah <PERSON>",
    subject: "Mathematics",
    email: "<EMAIL>",
    department: "Mathematics",
    status: "ACTIVE",
  },
  // ... more teachers
];

// Fetch function
const fetchTeachers = async () => {
  if (USE_DUMMY_DATA) {
    await new Promise(resolve => setTimeout(resolve, 1000));
    return dummyTeachers;
  }
  
  const { data } = await apiClient.get("/teachers");
  return data;
};
```

#### **Students Module**
```typescript
// Dummy data
const dummyStudents = [
  {
    id: "1",
    student_id: "STU001",
    first_name: "John",
    last_name: "Doe",
    email: "<EMAIL>",
    class_name: "10th Grade A",
    status: "ACTIVE",
  },
  // ... more students
];

// Fetch function
const fetchStudents = async () => {
  if (USE_DUMMY_DATA) {
    await new Promise(resolve => setTimeout(resolve, 800));
    return dummyStudents;
  }
  
  const { data } = await apiClient.get("/students");
  return data;
};
```

#### **Classes Module**
```typescript
// Dummy data
const dummyClasses = [
  {
    id: 1,
    name: "10th Grade A",
    grade_level: "10th Grade",
    capacity: 30,
    current_enrollment: 25,
    teacher_name: "Sarah Johnson",
    room_number: "101",
  },
  // ... more classes
];

// Fetch function
const fetchClasses = async () => {
  if (USE_DUMMY_DATA) {
    await new Promise(resolve => setTimeout(resolve, 1200));
    return dummyClasses;
  }
  
  const { data } = await apiClient.get("/classes");
  return data;
};
```

#### **Attendance Module**
```typescript
// Dummy data
const dummyAttendance = [
  {
    id: 1,
    student_name: "John Doe",
    class_name: "10th Grade A",
    date: "2024-01-15",
    status: "PRESENT",
    marked_at: "2024-01-15T09:00:00Z",
  },
  // ... more attendance records
];

// Fetch function
const fetchAttendance = async (params?: any) => {
  if (USE_DUMMY_DATA) {
    await new Promise(resolve => setTimeout(resolve, 600));
    return dummyAttendance;
  }
  
  const { data } = await apiClient.get("/attendance", { params });
  return data;
};
```

### 3. **Standard UI States**

#### **Loading State**
```typescript
if (isLoading) {
  return (
    <div className="flex items-center justify-center min-h-[400px]">
      <div className="text-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
        <p className="text-gray-600">Loading {moduleName}...</p>
      </div>
    </div>
  );
}
```

#### **Error State**
```typescript
if (error) {
  return (
    <div className="flex items-center justify-center min-h-[400px]">
      <div className="text-center">
        <div className="text-red-500 text-6xl mb-4">⚠️</div>
        <h2 className="text-xl font-semibold text-gray-800 mb-2">Error Loading {moduleName}</h2>
        <p className="text-gray-600 mb-4">
          {error instanceof Error ? error.message : "An unexpected error occurred"}
        </p>
        <button 
          onClick={() => window.location.reload()} 
          className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors"
        >
          Try Again
        </button>
      </div>
    </div>
  );
}
```

#### **Empty State**
```typescript
if (data.length === 0) {
  return (
    <div className="flex items-center justify-center min-h-[400px]">
      <div className="text-center">
        <div className="text-gray-400 text-6xl mb-4">{moduleIcon}</div>
        <h2 className="text-xl font-semibold text-gray-800 mb-2">No {moduleName} Found</h2>
        <p className="text-gray-600">There are no {moduleName.toLowerCase()} in the system yet.</p>
      </div>
    </div>
  );
}
```

### 4. **Development Indicator**
```typescript
{USE_DUMMY_DATA && (
  <span className="ml-2 px-2 py-1 bg-yellow-100 text-yellow-800 text-xs rounded">
    Using Dummy Data
  </span>
)}
```

## 🚀 Benefits

### **Development Phase**
- ✅ **Works without backend** - Immediate development start
- ✅ **Realistic data** - Proper UI testing with meaningful data
- ✅ **Simulated delays** - Test loading states
- ✅ **Visual indicator** - Clear "Using Dummy Data" badge

### **Production Phase**
- ✅ **Easy toggle** - Change one flag to switch to real API
- ✅ **No code changes** - Same component structure
- ✅ **Type safety** - Same data structure expected
- ✅ **Error handling** - Proper error states for real API failures

## 📋 Implementation Checklist

For each module page:

- [ ] Create realistic dummy data matching backend schema
- [ ] Implement `USE_DUMMY_DATA` toggle flag
- [ ] Add `fetchData` function with conditional logic
- [ ] Use `useQueryBase` hook for data fetching
- [ ] Implement loading, error, and empty states
- [ ] Add development indicator badge
- [ ] Test with dummy data first
- [ ] Switch to real API when backend is ready

## 🎯 Module-Specific Examples

### **All Modules to Implement:**
1. **Students** ✅ (Example created)
2. **Teachers** ✅ (Example created)
3. **Classes** - TODO
4. **Subjects** - TODO
5. **Attendance** - TODO
6. **Exams** - TODO
7. **Fees** - TODO
8. **Parents** - TODO
9. **Announcements** - TODO
10. **Events** - TODO
11. **Dashboard** - TODO
12. **Reports** - TODO

## 🔄 Migration Process

### **Phase 1: Development (Dummy Data)**
```typescript
const USE_DUMMY_DATA = true; // Development mode
```

### **Phase 2: Backend Integration**
```typescript
const USE_DUMMY_DATA = false; // Production mode
```

### **Phase 3: Environment-Based Toggle**
```typescript
const USE_DUMMY_DATA = process.env.NODE_ENV === 'development';
```

This pattern ensures smooth development workflow and easy backend integration! 🚀
