'use client';

import { <PERSON><PERSON> } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { ArrowLeft, Folder, Save } from 'lucide-react';
import Link from 'next/link';
import { useParams, useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';

// Mock media data
const mockMedia = [
  {
    id: 1,
    name: 'School_Event_2024.jpg',
    type: 'Image',
    size: '2.4 MB',
    format: 'JPEG',
    uploadedBy: 'Admin',
    uploadDate: '2024-03-01',
    category: 'Events',
    status: 'Active',
    url: '/uploads/school_event_2024.jpg',
    description: 'Annual school event photos',
  },
  {
    id: 2,
    name: 'Student_Handbook_2024.pdf',
    type: 'Document',
    size: '1.8 MB',
    format: 'PDF',
    uploadedBy: 'Principal',
    uploadDate: '2024-02-28',
    category: 'Documents',
    status: 'Active',
    url: '/uploads/student_handbook_2024.pdf',
    description: 'Official student handbook',
  },
];

export default function EditMediaPage() {
  const { id } = useParams();
  const router = useRouter();
  const [formData, setFormData] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    const record = mockMedia.find(r => r.id === Number(id));
    if (record) {
      setFormData(record);
    }
  }, [id]);

  if (!formData) {
    return <p className='p-6'>Media file not found</p>;
  }

  const handleChange = (field: string, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleSave = async () => {
    setIsLoading(true);
    try {
      // TODO: Replace with actual API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      console.log('Updated media:', formData);
      router.push(`/dashboard/media/${id}`);
    } catch (error) {
      console.error('Error updating media:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const mediaTypes = ['Image', 'Video', 'Document', 'Audio'];
  const categories = ['Events', 'Documents', 'Educational', 'Branding', 'Announcements'];
  const statuses = ['Active', 'Archived', 'Draft'];

  return (
    <div className='container mx-auto p-4 sm:p-6 lg:p-8 max-w-4xl'>
      {/* Header */}
      <div className='flex items-center gap-4 mb-8'>
        <Link href={`/dashboard/media/${id}`}>
          <Button variant='outline' size='sm'>
            <ArrowLeft className='w-4 h-4 mr-2' />
            Back to Media
          </Button>
        </Link>
        <div>
          <h1 className='text-3xl font-bold text-gray-900 flex items-center gap-2'>
            <Folder className='w-8 h-8 text-blue-600' />
            Edit Media File
          </h1>
          <p className='text-gray-600 mt-1'>Update media file details and properties</p>
        </div>
      </div>

      {/* Form */}
      <div className='space-y-8'>
        {/* Basic Information */}
        <Card>
          <CardHeader>
            <CardTitle>File Information</CardTitle>
          </CardHeader>
          <CardContent className='space-y-6'>
            <div className='space-y-2'>
              <Label htmlFor='name'>File Name *</Label>
              <Input
                id='name'
                placeholder='e.g., School_Event_2024.jpg'
                value={formData.name}
                onChange={e => handleChange('name', e.target.value)}
                required
              />
            </div>

            <div className='space-y-2'>
              <Label htmlFor='description'>Description</Label>
              <Textarea
                id='description'
                placeholder='Enter file description...'
                value={formData.description}
                onChange={e => handleChange('description', e.target.value)}
                rows={4}
              />
            </div>

            <div className='grid grid-cols-1 md:grid-cols-3 gap-6'>
              <div className='space-y-2'>
                <Label htmlFor='type'>Type *</Label>
                <Select value={formData.type} onValueChange={value => handleChange('type', value)}>
                  <SelectTrigger>
                    <SelectValue placeholder='Select type' />
                  </SelectTrigger>
                  <SelectContent>
                    {mediaTypes.map(type => (
                      <SelectItem key={type} value={type}>
                        {type}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div className='space-y-2'>
                <Label htmlFor='category'>Category *</Label>
                <Select value={formData.category} onValueChange={value => handleChange('category', value)}>
                  <SelectTrigger>
                    <SelectValue placeholder='Select category' />
                  </SelectTrigger>
                  <SelectContent>
                    {categories.map(category => (
                      <SelectItem key={category} value={category}>
                        {category}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div className='space-y-2'>
                <Label htmlFor='status'>Status *</Label>
                <Select value={formData.status} onValueChange={value => handleChange('status', value)}>
                  <SelectTrigger>
                    <SelectValue placeholder='Select status' />
                  </SelectTrigger>
                  <SelectContent>
                    {statuses.map(status => (
                      <SelectItem key={status} value={status}>
                        {status}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* File Properties (Read-only) */}
        <Card>
          <CardHeader>
            <CardTitle>File Properties</CardTitle>
          </CardHeader>
          <CardContent className='space-y-4'>
            <div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
              <div className='space-y-2'>
                <Label>Format</Label>
                <Input value={formData.format} disabled />
              </div>
              <div className='space-y-2'>
                <Label>File Size</Label>
                <Input value={formData.size} disabled />
              </div>
              <div className='space-y-2'>
                <Label>Uploaded By</Label>
                <Input value={formData.uploadedBy} disabled />
              </div>
              <div className='space-y-2'>
                <Label>Upload Date</Label>
                <Input value={formData.uploadDate} disabled />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Actions */}
        <div className='flex justify-end gap-4'>
          <Link href={`/dashboard/media/${id}`}>
            <Button type='button' variant='outline'>
              Cancel
            </Button>
          </Link>
          <Button onClick={handleSave} disabled={isLoading}>
            {isLoading ? (
              <>
                <div className='w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2' />
                Updating...
              </>
            ) : (
              <>
                <Save className='w-4 h-4 mr-2' />
                Update Media
              </>
            )}
          </Button>
        </div>
      </div>
    </div>
  );
}
