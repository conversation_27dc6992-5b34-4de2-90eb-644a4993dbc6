// scripts/debug-create-button.mjs - Debug create button functionality
// This script helps identify why the create button might not be working

console.log("🔍 Create Button Debug Test");
console.log("Testing the create button functionality and navigation...\n");

// Test 1: Check if the create route exists
console.log("🚀 Test 1: Checking create route accessibility");

const createPageUrl = "http://localhost:3000/dashboard/classes/create";
console.log(`Testing URL: ${createPageUrl}`);

try {
  const response = await fetch(createPageUrl, {
    method: 'GET',
    headers: {
      'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
      'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
    }
  });

  console.log(`📊 Status: ${response.status} ${response.statusText}`);
  
  if (response.ok) {
    console.log("✅ Create page is accessible");
    const html = await response.text();
    
    // Check if the page contains expected elements
    if (html.includes('Create Class') || html.includes('ClassForm')) {
      console.log("✅ Create page contains expected content");
    } else {
      console.log("⚠️  Create page may not be rendering correctly");
    }
    
    if (html.includes('error') || html.includes('Error')) {
      console.log("⚠️  Page may contain errors");
    }
  } else if (response.status === 404) {
    console.log("❌ Create page not found - check routing");
  } else if (response.status === 500) {
    console.log("❌ Server error on create page");
  } else {
    console.log(`❌ Unexpected status: ${response.status}`);
  }
} catch (error) {
  if (error.code === 'ECONNREFUSED') {
    console.log("🔌 Frontend server not running on localhost:3000");
    console.log("💡 Try: npm run dev or yarn dev");
  } else {
    console.log(`❌ Error: ${error.message}`);
  }
}

// Test 2: Check permissions logic
console.log("\n🚀 Test 2: Testing permission logic");

// Simulate different user roles
const testRoles = ['SUPER_ADMIN', 'ADMIN', 'TEACHER', 'STUDENT', null, undefined];

const canCreateClass = (role) => role === "SUPER_ADMIN" || role === "ADMIN";

testRoles.forEach(role => {
  const canCreate = canCreateClass(role);
  const status = canCreate ? "✅ CAN CREATE" : "❌ CANNOT CREATE";
  console.log(`Role: ${role || 'null/undefined'} → ${status}`);
});

// Test 3: Check if ModulePageLayout is working
console.log("\n🚀 Test 3: ModulePageLayout Integration");
console.log("The create button is rendered by ModulePageLayout component");
console.log("Check these potential issues:");
console.log("1. createRoute prop is undefined or null");
console.log("2. canCreateClass() returns false for current user");
console.log("3. ModulePageLayout component has a bug");
console.log("4. Button click handler is not working");
console.log("5. Navigation/routing is broken");

console.log("\n🎯 Debugging Steps:");
console.log("1. Check browser console for JavaScript errors");
console.log("2. Verify user role in localStorage or cookies");
console.log("3. Test navigation manually: /dashboard/classes/create");
console.log("4. Check if ModulePageLayout renders the create button");
console.log("5. Verify button onClick handler is called");

console.log("\n💡 Quick Fixes to Try:");
console.log("1. Clear browser cache and localStorage");
console.log("2. Check user authentication state");
console.log("3. Verify ADMIN/SUPER_ADMIN role assignment");
console.log("4. Test with different user accounts");
console.log("5. Check browser network tab for failed requests");
