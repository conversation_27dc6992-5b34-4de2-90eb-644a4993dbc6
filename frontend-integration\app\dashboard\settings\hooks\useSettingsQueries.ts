/**
 * Settings Hooks - TanStack Query Integration
 * 
 * Custom hooks for settings data management with:
 * - Query hooks for data fetching
 * - Mutation hooks for updates
 * - Optimistic updates
 * - Error handling
 * - Cache invalidation
 */

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useToast } from '@/hooks/use-toast';
import { settingsApi } from '../adapters/settings.api';
import type {
  SchoolProfile,
  UsersRoles,
  Fees,
  AcademicStructure,
  ExamTerms,
  Notifications,
  IDCardNote,
  Sessions,
  SettingsData,
} from '../schemas/settings.schemas';

// Query keys
export const SETTINGS_QUERY_KEYS = {
  ALL: ['settings'] as const,
  SCHOOL_PROFILE: ['settings', 'school-profile'] as const,
  USERS_ROLES: ['settings', 'users-roles'] as const,
  FEES: ['settings', 'fees'] as const,
  ACADEMIC_STRUCTURE: ['settings', 'academic-structure'] as const,
  EXAM_TERMS: ['settings', 'exam-terms'] as const,
  NOTIFICATIONS: ['settings', 'notifications'] as const,
  ID_CARD_NOTE: ['settings', 'id-card-note'] as const,
  SESSIONS: ['settings', 'sessions'] as const,
} as const;

// Query configuration
const QUERY_CONFIG = {
  staleTime: 5 * 60 * 1000, // 5 minutes
  gcTime: 10 * 60 * 1000, // 10 minutes
  retry: 2,
};

// All Settings Hook
export const useAllSettings = () => {
  return useQuery({
    queryKey: SETTINGS_QUERY_KEYS.ALL,
    queryFn: settingsApi.getAllSettings,
    ...QUERY_CONFIG,
  });
};

// School Profile Hooks
export const useSchoolProfile = () => {
  return useQuery({
    queryKey: SETTINGS_QUERY_KEYS.SCHOOL_PROFILE,
    queryFn: settingsApi.getSchoolProfile,
    ...QUERY_CONFIG,
  });
};

export const useUpdateSchoolProfile = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: settingsApi.updateSchoolProfile,
    onMutate: async (newData) => {
      // Cancel outgoing refetches
      await queryClient.cancelQueries({ queryKey: SETTINGS_QUERY_KEYS.SCHOOL_PROFILE });

      // Snapshot previous value
      const previousData = queryClient.getQueryData<SchoolProfile>(SETTINGS_QUERY_KEYS.SCHOOL_PROFILE);

      // Optimistically update
      if (previousData) {
        queryClient.setQueryData<SchoolProfile>(
          SETTINGS_QUERY_KEYS.SCHOOL_PROFILE,
          { ...previousData, ...newData }
        );
      }

      return { previousData };
    },
    onError: (error, variables, context) => {
      // Rollback on error
      if (context?.previousData) {
        queryClient.setQueryData(SETTINGS_QUERY_KEYS.SCHOOL_PROFILE, context.previousData);
      }
      toast({
        title: 'Update failed',
        description: 'Failed to update school profile. Please try again.',
        variant: 'destructive',
      });
    },
    onSuccess: () => {
      toast({
        title: 'Profile updated',
        description: 'School profile has been updated successfully.',
      });
    },
    onSettled: () => {
      // Always refetch after error or success
      queryClient.invalidateQueries({ queryKey: SETTINGS_QUERY_KEYS.SCHOOL_PROFILE });
    },
  });
};

// Users & Roles Hooks
export const useUsersRoles = () => {
  return useQuery({
    queryKey: SETTINGS_QUERY_KEYS.USERS_ROLES,
    queryFn: settingsApi.getUsersRoles,
    ...QUERY_CONFIG,
  });
};

export const useUpdateUsersRoles = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: settingsApi.updateUsersRoles,
    onMutate: async (newData) => {
      await queryClient.cancelQueries({ queryKey: SETTINGS_QUERY_KEYS.USERS_ROLES });
      const previousData = queryClient.getQueryData<UsersRoles>(SETTINGS_QUERY_KEYS.USERS_ROLES);
      
      if (previousData) {
        queryClient.setQueryData<UsersRoles>(
          SETTINGS_QUERY_KEYS.USERS_ROLES,
          { ...previousData, ...newData }
        );
      }

      return { previousData };
    },
    onError: (error, variables, context) => {
      if (context?.previousData) {
        queryClient.setQueryData(SETTINGS_QUERY_KEYS.USERS_ROLES, context.previousData);
      }
      toast({
        title: 'Update failed',
        description: 'Failed to update users & roles settings. Please try again.',
        variant: 'destructive',
      });
    },
    onSuccess: () => {
      toast({
        title: 'Settings updated',
        description: 'Users & roles settings have been updated successfully.',
      });
    },
    onSettled: () => {
      queryClient.invalidateQueries({ queryKey: SETTINGS_QUERY_KEYS.USERS_ROLES });
    },
  });
};

// Fees Hooks
export const useFees = () => {
  return useQuery({
    queryKey: SETTINGS_QUERY_KEYS.FEES,
    queryFn: settingsApi.getFees,
    ...QUERY_CONFIG,
  });
};

export const useUpdateFees = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: settingsApi.updateFees,
    onMutate: async (newData) => {
      await queryClient.cancelQueries({ queryKey: SETTINGS_QUERY_KEYS.FEES });
      const previousData = queryClient.getQueryData<Fees>(SETTINGS_QUERY_KEYS.FEES);
      
      if (previousData) {
        queryClient.setQueryData<Fees>(
          SETTINGS_QUERY_KEYS.FEES,
          { ...previousData, ...newData }
        );
      }

      return { previousData };
    },
    onError: (error, variables, context) => {
      if (context?.previousData) {
        queryClient.setQueryData(SETTINGS_QUERY_KEYS.FEES, context.previousData);
      }
      toast({
        title: 'Update failed',
        description: 'Failed to update fees settings. Please try again.',
        variant: 'destructive',
      });
    },
    onSuccess: () => {
      toast({
        title: 'Settings updated',
        description: 'Fees settings have been updated successfully.',
      });
    },
    onSettled: () => {
      queryClient.invalidateQueries({ queryKey: SETTINGS_QUERY_KEYS.FEES });
    },
  });
};

// Academic Structure Hooks
export const useAcademicStructure = () => {
  return useQuery({
    queryKey: SETTINGS_QUERY_KEYS.ACADEMIC_STRUCTURE,
    queryFn: settingsApi.getAcademicStructure,
    ...QUERY_CONFIG,
  });
};

export const useUpdateAcademicStructure = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: settingsApi.updateAcademicStructure,
    onMutate: async (newData) => {
      await queryClient.cancelQueries({ queryKey: SETTINGS_QUERY_KEYS.ACADEMIC_STRUCTURE });
      const previousData = queryClient.getQueryData<AcademicStructure>(SETTINGS_QUERY_KEYS.ACADEMIC_STRUCTURE);
      
      if (previousData) {
        queryClient.setQueryData<AcademicStructure>(
          SETTINGS_QUERY_KEYS.ACADEMIC_STRUCTURE,
          { ...previousData, ...newData }
        );
      }

      return { previousData };
    },
    onError: (error, variables, context) => {
      if (context?.previousData) {
        queryClient.setQueryData(SETTINGS_QUERY_KEYS.ACADEMIC_STRUCTURE, context.previousData);
      }
      toast({
        title: 'Update failed',
        description: 'Failed to update academic structure settings. Please try again.',
        variant: 'destructive',
      });
    },
    onSuccess: () => {
      toast({
        title: 'Settings updated',
        description: 'Academic structure settings have been updated successfully.',
      });
    },
    onSettled: () => {
      queryClient.invalidateQueries({ queryKey: SETTINGS_QUERY_KEYS.ACADEMIC_STRUCTURE });
    },
  });
};

// File Upload Hook
export const useFileUpload = () => {
  const { toast } = useToast();

  return useMutation({
    mutationFn: ({ file, type }: { file: File; type: string }) => 
      settingsApi.uploadFile(file, type),
    onError: () => {
      toast({
        title: 'Upload failed',
        description: 'Failed to upload file. Please try again.',
        variant: 'destructive',
      });
    },
    onSuccess: () => {
      toast({
        title: 'File uploaded',
        description: 'File has been uploaded successfully.',
      });
    },
  });
};

// Bulk Settings Update Hook
export const useBulkSettingsUpdate = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (updates: Partial<SettingsData>) => {
      const promises = [];
      
      if (updates.schoolProfile) {
        promises.push(settingsApi.updateSchoolProfile(updates.schoolProfile));
      }
      if (updates.usersRoles) {
        promises.push(settingsApi.updateUsersRoles(updates.usersRoles));
      }
      if (updates.fees) {
        promises.push(settingsApi.updateFees(updates.fees));
      }
      if (updates.academicStructure) {
        promises.push(settingsApi.updateAcademicStructure(updates.academicStructure));
      }

      return Promise.all(promises);
    },
    onError: () => {
      toast({
        title: 'Bulk update failed',
        description: 'Failed to update settings. Please try again.',
        variant: 'destructive',
      });
    },
    onSuccess: () => {
      toast({
        title: 'Settings updated',
        description: 'All settings have been updated successfully.',
      });
    },
    onSettled: () => {
      // Invalidate all settings queries
      queryClient.invalidateQueries({ queryKey: SETTINGS_QUERY_KEYS.ALL });
    },
  });
};
