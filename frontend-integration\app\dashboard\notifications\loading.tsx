/**
 * Notifications Loading Page - Professional Loading State
 */

import { Card, CardContent } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';

export default function NotificationsLoading() {
  return (
    <div className='container mx-auto p-4 sm:p-6 lg:p-8 space-y-8'>
      {/* Header Skeleton */}
      <div className='flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4'>
        <div>
          <Skeleton className='h-8 w-48 mb-2' />
          <Skeleton className='h-4 w-64' />
        </div>
        <Skeleton className='h-10 w-32' />
      </div>

      {/* Stats Cards Skeleton */}
      <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6'>
        {Array.from({ length: 4 }).map((_, i) => (
          <Card key={i}>
            <CardContent className='p-6'>
              <div className='flex items-center justify-between'>
                <div>
                  <Skeleton className='h-4 w-20 mb-2' />
                  <Skeleton className='h-8 w-16' />
                </div>
                <Skeleton className='h-12 w-12 rounded-lg' />
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Search and Filters Skeleton */}
      <div className='flex flex-col sm:flex-row gap-4'>
        <Skeleton className='h-10 flex-1' />
        <Skeleton className='h-10 w-32' />
        <Skeleton className='h-10 w-32' />
        <Skeleton className='h-10 w-32' />
      </div>

      {/* Notifications List Skeleton */}
      <div className='space-y-4'>
        {Array.from({ length: 5 }).map((_, i) => (
          <Card key={i}>
            <CardContent className='p-6'>
              <div className='flex flex-col lg:flex-row lg:items-start lg:justify-between gap-4'>
                <div className='flex-1 space-y-3'>
                  <div className='flex items-start justify-between gap-4'>
                    <div className='flex-1'>
                      <div className='flex items-center gap-2 mb-2'>
                        <Skeleton className='w-2 h-2 rounded-full' />
                        <Skeleton className='h-6 w-64' />
                      </div>
                      <Skeleton className='h-4 w-96 mb-3' />
                      <div className='flex gap-2'>
                        <Skeleton className='h-6 w-20' />
                        <Skeleton className='h-6 w-16' />
                        <Skeleton className='h-6 w-16' />
                      </div>
                    </div>
                    <div className='flex gap-2'>
                      <Skeleton className='h-8 w-16' />
                      <Skeleton className='h-8 w-12' />
                    </div>
                  </div>
                  <div className='grid grid-cols-4 gap-4 pt-4'>
                    <Skeleton className='h-4 w-24' />
                    <Skeleton className='h-4 w-20' />
                    <Skeleton className='h-4 w-20' />
                    <Skeleton className='h-4 w-16' />
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
}
