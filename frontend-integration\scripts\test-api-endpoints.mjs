// scripts/test-api-endpoints.mjs - Automated endpoint structure test
// Tests endpoint structure and redirect behavior without requiring authentication

const base = (process.env.NEXT_PUBLIC_API_URL || 'http://127.0.0.1:8000/api/v1').replace(/\/$/, '');
const token = process.env.API_TOKEN;

console.log('🔍 API Endpoint Structure Test');
console.log('Base URL:', base);
console.log('Token:', token ? '✅ SET' : '❌ NOT SET');
console.log('Testing endpoint structure and redirect behavior...\n');

const endpoints = [
  // Collection endpoints - should have trailing slash
  { name: 'Classes List', url: `${base}/classes/`, expectRedirect: false, type: 'collection' },
  { name: 'Teachers List', url: `${base}/teachers/`, expectRedirect: false, type: 'collection' },
  { name: 'Students List', url: `${base}/students/`, expectRedirect: false, type: 'collection' },
  // Sub-route endpoints - should NOT have trailing slash
  {
    name: 'Classes Stats',
    url: `${base}/classes/stats`,
    expectRedirect: false,
    type: 'stats',
    expectData: true,
  },
  {
    name: 'Teachers Stats',
    url: `${base}/teachers/stats`,
    expectRedirect: false,
    type: 'stats',
    expectData: true,
  },
  // Test without trailing slash to detect redirect issues
  {
    name: 'Classes (no slash)',
    url: `${base}/classes`,
    expectRedirect: true,
    type: 'redirect-test',
  },
  {
    name: 'Teachers (no slash)',
    url: `${base}/teachers`,
    expectRedirect: true,
    type: 'redirect-test',
  },
  {
    name: 'Students (no slash)',
    url: `${base}/students`,
    expectRedirect: true,
    type: 'redirect-test',
  },
];

async function testEndpoint(endpoint) {
  console.log(`🚀 Testing: ${endpoint.name}`);
  console.log(`   URL: ${endpoint.url}`);

  try {
    const headers = {
      'Content-Type': 'application/json',
    };

    // Add Bearer token if available
    if (token) {
      headers.Authorization = `Bearer ${token}`;
    }

    const res = await fetch(endpoint.url, {
      method: 'GET',
      headers,
      redirect: 'manual', // Handle redirects manually
    });

    console.log(`   Status: ${res.status} ${res.statusText}`);

    if (res.status === 307 || res.status === 308) {
      const location = res.headers.get('location');
      console.log(`   🔄 Redirect to: ${location}`);

      if (endpoint.expectRedirect) {
        console.log(`   ✅ Expected redirect detected`);
      } else {
        console.log(`   ⚠️  Unexpected redirect - endpoint should have trailing slash`);
      }
    } else if (res.status === 401) {
      if (token) {
        console.log(`   🔐 Unauthorized (token may be invalid or expired)`);
      } else {
        console.log(`   🔐 Unauthorized (expected without token)`);
      }
    } else if (res.status === 404) {
      console.log(`   📝 Not Found (endpoint may not be implemented)`);
    } else if (res.status === 200) {
      console.log(`   ✅ OK (endpoint accessible and working)`);

      // For stats endpoints, validate the response data
      if (endpoint.type === 'stats' && endpoint.expectData) {
        try {
          const data = await res.json();
          console.log(`   📊 Stats data received:`, JSON.stringify(data, null, 2));

          // Validate required fields based on endpoint type
          if (endpoint.name.includes('Classes')) {
            const requiredFields = [
              'total',
              'active',
              'inactive',
              'totalStudents',
              'averageCapacity',
            ];
            const missingFields = requiredFields.filter(field => !(field in data));
            if (missingFields.length === 0) {
              console.log(`   ✅ All required fields present for classes stats`);
            } else {
              console.log(`   ⚠️  Missing fields in classes stats: ${missingFields.join(', ')}`);
            }
          } else if (endpoint.name.includes('Teachers')) {
            const requiredFields = [
              'total',
              'active',
              'inactive',
              'departments',
              'averageExperience',
            ];
            const missingFields = requiredFields.filter(field => !(field in data));
            if (missingFields.length === 0) {
              console.log(`   ✅ All required fields present for teachers stats`);

              // Validate departments is a dict/object
              if (
                typeof data.departments === 'object' &&
                data.departments !== null &&
                !Array.isArray(data.departments)
              ) {
                console.log(`   ✅ Departments field is correctly formatted as object`);
              } else {
                console.log(
                  `   ⚠️  Departments should be an object, got: ${typeof data.departments}`
                );
              }
            } else {
              console.log(`   ⚠️  Missing fields in teachers stats: ${missingFields.join(', ')}`);
            }
          }
        } catch (jsonError) {
          console.log(`   ⚠️  Could not parse JSON response: ${jsonError.message}`);
        }
      }
    } else if (res.status === 422) {
      console.log(`   ❌ 422 Unprocessable Content - validation error (check query parameters)`);
    } else {
      console.log(`   ❓ Unexpected status: ${res.status}`);
    }
  } catch (error) {
    if (error.code === 'ECONNREFUSED') {
      console.log(`   🔌 Connection refused - backend server not running`);
    } else {
      console.log(`   ❌ Error: ${error.message}`);
    }
  }

  console.log(''); // Empty line for readability
}

(async () => {
  for (const endpoint of endpoints) {
    await testEndpoint(endpoint);
  }

  console.log('🎯 Summary:');
  console.log('✅ Collection endpoints (/classes/, /teachers/, /students/) should return 401/200');
  console.log(
    '✅ Stats endpoints (/classes/stats, /teachers/stats) should return 401/200 with JSON data'
  );
  console.log('✅ Endpoints without trailing slash should return 307 redirect');
  console.log('❌ 404 indicates endpoint not implemented in backend');
  console.log('❌ 422 indicates validation error (missing required parameters)');
  console.log('🔌 Connection refused indicates backend server not running');
  console.log('');
  console.log('📊 Stats endpoints should return:');
  console.log('   Classes: {total, active, inactive, totalStudents, averageCapacity}');
  console.log('   Teachers: {total, active, inactive, departments: {}, averageExperience}');
  console.log('');
  console.log('💡 To test with authentication:');
  console.log('set API_TOKEN=your_jwt_token && node scripts/test-api-endpoints.mjs');
})();
