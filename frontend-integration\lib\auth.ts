/**
 * Unified Auth Helper - Simple API for Authentication
 *
 * Provides the exact API requested in the user requirements:
 * - login(username, password) -> stores JWT token
 * - getAuthHeader() -> returns Authorization header object
 */

/**
 * Login user and store JWT token
 * @param username - Username or email
 * @param password - Password
 * @returns Promise with login response data
 */
export async function login(username: string, password: string) {
  const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/api/v1/auth/login`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ username, password }),
  });

  if (!response.ok) {
    throw new Error('Login failed');
  }

  const data = await response.json();

  // Store token in localStorage for API client to use
  if (typeof window !== 'undefined') {
    localStorage.setItem('access_token', data.access_token);
    localStorage.setItem('auth.token', data.access_token); // Backup key
  }

  return data;
}

/**
 * Get Authorization header for API requests
 * @returns Object with Authorization header or empty object
 */
export function getAuthHeader() {
  if (typeof window === 'undefined') return {};

  const token = localStorage.getItem('access_token');
  return token ? { Authorization: `Bearer ${token}` } : {};
}
