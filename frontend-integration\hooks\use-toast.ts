/**
 * Toast Hook - Sonner Integration
 * 
 * Simple wrapper around Sonner toast for consistent usage
 * across the application with type safety.
 */

import { toast as sonnerToast } from 'sonner';

interface ToastOptions {
  title: string;
  description?: string;
  variant?: 'default' | 'destructive';
  duration?: number;
}

export const useToast = () => {
  const toast = ({ title, description, variant = 'default', duration }: ToastOptions) => {
    const message = description ? `${title}\n${description}` : title;
    
    if (variant === 'destructive') {
      sonnerToast.error(message, { duration });
    } else {
      sonnerToast.success(message, { duration });
    }
  };

  return { toast };
};
