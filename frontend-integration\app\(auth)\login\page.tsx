/**
 * Login Page - Professional implementation with React Query
 *
 * Features:
 * - Form handling with validation
 * - React Query auth hooks
 * - Proper error handling and user feedback
 * - Redirect after successful login
 * - Cookie-based authentication
 */

'use client';

import { useIsAuthenticated, useLogin } from '@/hooks/useAuthQuery';
import { useRouter, useSearchParams } from 'next/navigation';
import { useEffect, useState } from 'react';

// Demo credentials for testing
const DEMO_CREDENTIALS = {
  username: '<EMAIL>',
  password: 'admin123',
};

export default function LoginPage() {
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [rememberMe, setRememberMe] = useState(false);

  const router = useRouter();
  const searchParams = useSearchParams();
  const redirectTo = searchParams.get('redirect') || '/dashboard';

  const loginMutation = useLogin({
    onSuccess: () => {
      router.push(redirectTo);
    },
  });

  const { isAuthenticated, isLoading: authLoading } = useIsAuthenticated();

  // Redirect if already authenticated
  useEffect(() => {
    if (isAuthenticated && !authLoading) {
      router.push(redirectTo);
    }
  }, [isAuthenticated, authLoading, router, redirectTo]);

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!username || !password) {
      return;
    }

    loginMutation.mutate({
      username,
      password,
    });
  };

  const fillDemoCredentials = () => {
    setUsername(DEMO_CREDENTIALS.username);
    setPassword(DEMO_CREDENTIALS.password);
  };

  return (
    <div className='space-y-6'>
      {/* Header */}
      <div className='text-center'>
        <h2 className='text-2xl font-bold text-gray-900'>Sign in to your account</h2>
        <p className='mt-2 text-sm text-gray-600'>Access the school management system</p>
      </div>

      {/* Demo credentials info */}
      <div className='bg-yellow-50 border border-yellow-200 rounded-md p-4'>
        <div className='text-sm text-yellow-800'>
          <div className='flex justify-between items-center mb-2'>
            <p className='font-medium'>Demo Credentials:</p>
            <button
              type='button'
              onClick={fillDemoCredentials}
              className='text-xs bg-yellow-200 hover:bg-yellow-300 px-2 py-1 rounded'
            >
              Fill Form
            </button>
          </div>
          <p>Username: {DEMO_CREDENTIALS.username}</p>
          <p>Password: {DEMO_CREDENTIALS.password}</p>
        </div>
      </div>

      {/* Login Form */}
      <form className='space-y-6' onSubmit={handleLogin}>
        <div>
          <label htmlFor='username' className='block text-sm font-medium text-gray-700'>
            Username or Email
          </label>
          <div className='mt-1'>
            <input
              id='username'
              name='username'
              type='text'
              autoComplete='username'
              required
              value={username}
              onChange={e => setUsername(e.target.value)}
              className='appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm'
              placeholder='Enter your username or email'
            />
          </div>
        </div>

        <div>
          <label htmlFor='password' className='block text-sm font-medium text-gray-700'>
            Password
          </label>
          <div className='mt-1'>
            <input
              id='password'
              name='password'
              type='password'
              autoComplete='current-password'
              required
              value={password}
              onChange={e => setPassword(e.target.value)}
              className='appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm'
              placeholder='Enter your password'
            />
          </div>
        </div>

        <div className='flex items-center justify-between'>
          <div className='flex items-center'>
            <input
              id='remember-me'
              name='remember-me'
              type='checkbox'
              checked={rememberMe}
              onChange={e => setRememberMe(e.target.checked)}
              className='h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded'
            />
            <label htmlFor='remember-me' className='ml-2 block text-sm text-gray-900'>
              Remember me
            </label>
          </div>

          <div className='text-sm'>
            <a href='#' className='font-medium text-blue-600 hover:text-blue-500'>
              Forgot your password?
            </a>
          </div>
        </div>

        {/* Error Message */}
        {loginMutation.error && (
          <div className='bg-red-50 border border-red-200 rounded-md p-4'>
            <div className='text-sm text-red-800'>
              {loginMutation.error instanceof Error
                ? loginMutation.error.message
                : 'Login failed. Please try again.'}
            </div>
          </div>
        )}

        {/* Submit Button */}
        <div>
          <button
            type='submit'
            disabled={loginMutation.isPending || !username || !password}
            className='group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed'
          >
            {loginMutation.isPending ? (
              <div className='flex items-center'>
                <div className='animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2' />
                Signing in...
              </div>
            ) : (
              'Sign in'
            )}
          </button>
        </div>

        {/* Development Mode Indicator */}
        <div className='text-center'>
          <span className='px-2 py-1 bg-green-100 text-green-800 text-xs rounded'>
            Production Ready - Cookie-based Auth
          </span>
        </div>
      </form>

      {/* Additional links */}
      <div className='text-center text-sm text-gray-600'>
        <p>
          Don't have an account?{' '}
          <a href='#' className='font-medium text-blue-600 hover:text-blue-500'>
            Contact administrator
          </a>
        </p>
      </div>
    </div>
  );
}
