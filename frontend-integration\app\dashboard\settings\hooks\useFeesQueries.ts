/**
 * Fees Hooks - TanStack Query Integration
 * 
 * Custom hooks for fees data management with:
 * - Query hooks for all fees operations
 * - Mutation hooks with optimistic updates
 * - Error handling and toast notifications
 * - Cache invalidation strategies
 */

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useToast } from '@/hooks/use-toast';
import { feesApi } from '../adapters/fees.api';
import type {
  FeeType,
  ClassFeeSchedule,
  LateFeeRule,
  DiscountRule,
  FeeRules,
  OpeningBalance,
  OpeningBalanceEntry,
  FeesSettings,
  CSVImport,
  BulkFeeOperation,
} from '../schemas/fees.schemas';

// Query keys
export const FEES_QUERY_KEYS = {
  ALL: ['fees'] as const,
  FEE_TYPES: ['fees', 'types'] as const,
  CLASS_SCHEDULE: ['fees', 'class-schedule'] as const,
  FEE_RULES: ['fees', 'rules'] as const,
  LATE_FEE_RULE: ['fees', 'rules', 'late-fee'] as const,
  DISCOUNT_RULES: ['fees', 'rules', 'discounts'] as const,
  OPENING_BALANCE: ['fees', 'opening-balance'] as const,
  SETTINGS: ['fees', 'settings'] as const,
} as const;

// Query configuration
const QUERY_CONFIG = {
  staleTime: 5 * 60 * 1000, // 5 minutes
  gcTime: 10 * 60 * 1000, // 10 minutes
  retry: 2,
};

// Fee Types Hooks
export const useFeeTypes = () => {
  return useQuery({
    queryKey: FEES_QUERY_KEYS.FEE_TYPES,
    queryFn: feesApi.getFeeTypes,
    ...QUERY_CONFIG,
  });
};

export const useCreateFeeType = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: feesApi.createFeeType,
    onMutate: async (newFeeType) => {
      await queryClient.cancelQueries({ queryKey: FEES_QUERY_KEYS.FEE_TYPES });
      const previousFeeTypes = queryClient.getQueryData<FeeType[]>(FEES_QUERY_KEYS.FEE_TYPES);
      
      if (previousFeeTypes) {
        queryClient.setQueryData<FeeType[]>(
          FEES_QUERY_KEYS.FEE_TYPES,
          [...previousFeeTypes, { ...newFeeType, id: 'temp-' + Date.now() }]
        );
      }

      return { previousFeeTypes };
    },
    onError: (error, variables, context) => {
      if (context?.previousFeeTypes) {
        queryClient.setQueryData(FEES_QUERY_KEYS.FEE_TYPES, context.previousFeeTypes);
      }
      toast({
        title: 'Create failed',
        description: 'Failed to create fee type. Please try again.',
        variant: 'destructive',
      });
    },
    onSuccess: () => {
      toast({
        title: 'Fee type created',
        description: 'Fee type has been created successfully.',
      });
    },
    onSettled: () => {
      queryClient.invalidateQueries({ queryKey: FEES_QUERY_KEYS.FEE_TYPES });
    },
  });
};

export const useUpdateFeeType = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: Partial<FeeType> }) => 
      feesApi.updateFeeType(id, data),
    onSuccess: () => {
      toast({
        title: 'Fee type updated',
        description: 'Fee type has been updated successfully.',
      });
      queryClient.invalidateQueries({ queryKey: FEES_QUERY_KEYS.FEE_TYPES });
    },
    onError: () => {
      toast({
        title: 'Update failed',
        description: 'Failed to update fee type. Please try again.',
        variant: 'destructive',
      });
    },
  });
};

export const useDeleteFeeType = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: feesApi.deleteFeeType,
    onSuccess: () => {
      toast({
        title: 'Fee type deleted',
        description: 'Fee type has been deleted successfully.',
      });
      queryClient.invalidateQueries({ queryKey: FEES_QUERY_KEYS.FEE_TYPES });
    },
    onError: () => {
      toast({
        title: 'Delete failed',
        description: 'Failed to delete fee type. Please try again.',
        variant: 'destructive',
      });
    },
  });
};

export const useReorderFeeTypes = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: feesApi.reorderFeeTypes,
    onMutate: async (newOrder) => {
      await queryClient.cancelQueries({ queryKey: FEES_QUERY_KEYS.FEE_TYPES });
      const previousFeeTypes = queryClient.getQueryData<FeeType[]>(FEES_QUERY_KEYS.FEE_TYPES);
      
      queryClient.setQueryData<FeeType[]>(FEES_QUERY_KEYS.FEE_TYPES, newOrder);
      return { previousFeeTypes };
    },
    onError: (error, variables, context) => {
      if (context?.previousFeeTypes) {
        queryClient.setQueryData(FEES_QUERY_KEYS.FEE_TYPES, context.previousFeeTypes);
      }
      toast({
        title: 'Reorder failed',
        description: 'Failed to reorder fee types. Please try again.',
        variant: 'destructive',
      });
    },
    onSuccess: () => {
      toast({
        title: 'Fee types reordered',
        description: 'Fee types have been reordered successfully.',
      });
    },
  });
};

// Class Fee Schedule Hooks
export const useClassFeeSchedule = () => {
  return useQuery({
    queryKey: FEES_QUERY_KEYS.CLASS_SCHEDULE,
    queryFn: feesApi.getClassFeeSchedule,
    ...QUERY_CONFIG,
  });
};

export const useUpdateClassFeeSchedule = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: feesApi.updateClassFeeSchedule,
    onMutate: async (newSchedule) => {
      await queryClient.cancelQueries({ queryKey: FEES_QUERY_KEYS.CLASS_SCHEDULE });
      const previousSchedule = queryClient.getQueryData<ClassFeeSchedule>(FEES_QUERY_KEYS.CLASS_SCHEDULE);
      
      queryClient.setQueryData<ClassFeeSchedule>(FEES_QUERY_KEYS.CLASS_SCHEDULE, newSchedule);
      return { previousSchedule };
    },
    onError: (error, variables, context) => {
      if (context?.previousSchedule) {
        queryClient.setQueryData(FEES_QUERY_KEYS.CLASS_SCHEDULE, context.previousSchedule);
      }
      toast({
        title: 'Update failed',
        description: 'Failed to update class fee schedule. Please try again.',
        variant: 'destructive',
      });
    },
    onSuccess: () => {
      toast({
        title: 'Schedule updated',
        description: 'Class fee schedule has been updated successfully.',
      });
    },
  });
};

export const useBulkUpdateClassFees = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: feesApi.bulkUpdateClassFees,
    onSuccess: () => {
      toast({
        title: 'Bulk update completed',
        description: 'Class fees have been updated successfully.',
      });
      queryClient.invalidateQueries({ queryKey: FEES_QUERY_KEYS.CLASS_SCHEDULE });
    },
    onError: () => {
      toast({
        title: 'Bulk update failed',
        description: 'Failed to update class fees. Please try again.',
        variant: 'destructive',
      });
    },
  });
};

export const useImportClassFeeSchedule = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: feesApi.importClassFeeSchedule,
    onSuccess: (result) => {
      toast({
        title: 'Import completed',
        description: `Imported ${result.summary.valid} of ${result.summary.total} rows successfully.`,
      });
      queryClient.invalidateQueries({ queryKey: FEES_QUERY_KEYS.CLASS_SCHEDULE });
    },
    onError: () => {
      toast({
        title: 'Import failed',
        description: 'Failed to import CSV data. Please check the format.',
        variant: 'destructive',
      });
    },
  });
};

export const useExportClassFeeSchedule = () => {
  const { toast } = useToast();

  return useMutation({
    mutationFn: feesApi.exportClassFeeSchedule,
    onSuccess: () => {
      toast({
        title: 'Export completed',
        description: 'Class fee schedule has been exported successfully.',
      });
    },
    onError: () => {
      toast({
        title: 'Export failed',
        description: 'Failed to export class fee schedule.',
        variant: 'destructive',
      });
    },
  });
};

// Fee Rules Hooks
export const useFeeRules = () => {
  return useQuery({
    queryKey: FEES_QUERY_KEYS.FEE_RULES,
    queryFn: feesApi.getFeeRules,
    ...QUERY_CONFIG,
  });
};

export const useUpdateLateFeeRule = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: feesApi.updateLateFeeRule,
    onSuccess: () => {
      toast({
        title: 'Late fee rule updated',
        description: 'Late fee rule has been updated successfully.',
      });
      queryClient.invalidateQueries({ queryKey: FEES_QUERY_KEYS.FEE_RULES });
    },
    onError: () => {
      toast({
        title: 'Update failed',
        description: 'Failed to update late fee rule. Please try again.',
        variant: 'destructive',
      });
    },
  });
};

export const useDiscountRules = () => {
  return useQuery({
    queryKey: FEES_QUERY_KEYS.DISCOUNT_RULES,
    queryFn: feesApi.getDiscountRules,
    ...QUERY_CONFIG,
  });
};

export const useCreateDiscountRule = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: feesApi.createDiscountRule,
    onSuccess: () => {
      toast({
        title: 'Discount rule created',
        description: 'Discount rule has been created successfully.',
      });
      queryClient.invalidateQueries({ queryKey: FEES_QUERY_KEYS.DISCOUNT_RULES });
      queryClient.invalidateQueries({ queryKey: FEES_QUERY_KEYS.FEE_RULES });
    },
    onError: () => {
      toast({
        title: 'Create failed',
        description: 'Failed to create discount rule. Please try again.',
        variant: 'destructive',
      });
    },
  });
};

export const useDeleteDiscountRule = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: feesApi.deleteDiscountRule,
    onSuccess: () => {
      toast({
        title: 'Discount rule deleted',
        description: 'Discount rule has been deleted successfully.',
      });
      queryClient.invalidateQueries({ queryKey: FEES_QUERY_KEYS.DISCOUNT_RULES });
      queryClient.invalidateQueries({ queryKey: FEES_QUERY_KEYS.FEE_RULES });
    },
    onError: () => {
      toast({
        title: 'Delete failed',
        description: 'Failed to delete discount rule. Please try again.',
        variant: 'destructive',
      });
    },
  });
};

// Opening Balance Hooks
export const useOpeningBalance = () => {
  return useQuery({
    queryKey: FEES_QUERY_KEYS.OPENING_BALANCE,
    queryFn: feesApi.getOpeningBalance,
    ...QUERY_CONFIG,
  });
};

export const useCreateOpeningBalanceEntry = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: feesApi.createOpeningBalanceEntry,
    onSuccess: () => {
      toast({
        title: 'Balance entry created',
        description: 'Opening balance entry has been created successfully.',
      });
      queryClient.invalidateQueries({ queryKey: FEES_QUERY_KEYS.OPENING_BALANCE });
    },
    onError: () => {
      toast({
        title: 'Create failed',
        description: 'Failed to create opening balance entry. Please try again.',
        variant: 'destructive',
      });
    },
  });
};

export const useDeleteOpeningBalanceEntry = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: feesApi.deleteOpeningBalanceEntry,
    onSuccess: () => {
      toast({
        title: 'Balance entry deleted',
        description: 'Opening balance entry has been deleted successfully.',
      });
      queryClient.invalidateQueries({ queryKey: FEES_QUERY_KEYS.OPENING_BALANCE });
    },
    onError: () => {
      toast({
        title: 'Delete failed',
        description: 'Failed to delete opening balance entry. Please try again.',
        variant: 'destructive',
      });
    },
  });
};

// Fee Calculation Hook
export const useCalculateFee = () => {
  const { toast } = useToast();

  return useMutation({
    mutationFn: feesApi.calculateFee,
    onError: () => {
      toast({
        title: 'Calculation failed',
        description: 'Failed to calculate fee. Please try again.',
        variant: 'destructive',
      });
    },
  });
};

// Complete Fees Settings Hook
export const useFeesSettings = () => {
  return useQuery({
    queryKey: FEES_QUERY_KEYS.SETTINGS,
    queryFn: feesApi.getAllFeesSettings,
    ...QUERY_CONFIG,
  });
};

export const useUpdateFeesSettings = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: feesApi.updateFeesSettings,
    onSuccess: () => {
      toast({
        title: 'Settings updated',
        description: 'Fees settings have been updated successfully.',
      });
      queryClient.invalidateQueries({ queryKey: FEES_QUERY_KEYS.ALL });
    },
    onError: () => {
      toast({
        title: 'Update failed',
        description: 'Failed to update fees settings. Please try again.',
        variant: 'destructive',
      });
    },
  });
};
