#!/usr/bin/env node

/**
 * Test Class Creation API
 * Tests the POST /api/v1/classes/ endpoint with different payloads
 */

const API_BASE = 'http://127.0.0.1:8000/api/v1';

// Get token from environment
const token = process.env.API_TOKEN;
if (!token) {
  console.log('❌ No API_TOKEN set. Run:');
  console.log('Windows: set API_TOKEN=<your_jwt_token> && node scripts/test-class-creation.mjs');
  console.log('Linux/Mac: API_TOKEN=<your_jwt_token> node scripts/test-class-creation.mjs');
  process.exit(1);
}

console.log('🧪 Testing Class Creation API');
console.log(`Base URL: ${API_BASE}`);
console.log(`Token: ${token.substring(0, 20)}...`);

// Test payloads - from minimal to full
const testPayloads = [
  {
    name: 'Minimal Test',
    payload: {
      name: 'Test Class Minimal',
      grade: 'Grade 10',
      capacity: 30,
      academic_year: '2025',
      teacher_id: 1
    }
  },
  {
    name: 'Backend Schema Match',
    payload: {
      name: 'Advanced Computer',
      class_name: 'Advanced Computer',
      section: 'A',
      grade: 'Grade 9',
      level: 'Advanced',
      subject: 'Computer',
      teacher_id: 1,
      capacity: 30,
      room_number: '102',
      building: 'Science Block',
      floor: 2,
      schedule: 'Mon-Wed-Fri 10:00-11:00',
      duration_minutes: 60,
      description: 'Advanced computer class for grade 9 students',
      prerequisites: 'Basic Computer Skills',
      is_active: true,
      academic_year: '2024-2025',
      semester: 'Fall'
    }
  }
];

async function testClassCreation(testCase) {
  try {
    console.log(`\n🚀 Testing: ${testCase.name}`);
    console.log('📤 Payload:', JSON.stringify(testCase.payload, null, 2));

    const response = await fetch(`${API_BASE}/classes/`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      },
      body: JSON.stringify(testCase.payload)
    });

    const responseText = await response.text();
    console.log(`📊 Status: ${response.status} ${response.statusText}`);

    if (response.ok) {
      const data = JSON.parse(responseText);
      console.log('✅ Success! Created class:', {
        id: data.id,
        name: data.name,
        grade: data.grade,
        teacher_id: data.teacher_id
      });
      return { success: true, data };
    } else {
      console.log('❌ Failed!');
      try {
        const errorData = JSON.parse(responseText);
        console.log('📄 Error Details:', JSON.stringify(errorData, null, 2));
      } catch {
        console.log('📄 Raw Response:', responseText);
      }
      return { success: false, error: responseText };
    }
  } catch (error) {
    console.error('💥 Request Error:', error.message);
    return { success: false, error: error.message };
  }
}

async function main() {
  const results = [];
  
  for (const testCase of testPayloads) {
    const result = await testClassCreation(testCase);
    results.push({ ...testCase, result });
    
    // Wait between requests
    await new Promise(resolve => setTimeout(resolve, 1000));
  }

  console.log('\n📊 Summary:');
  results.forEach(({ name, result }) => {
    console.log(`${result.success ? '✅' : '❌'} ${name}: ${result.success ? 'SUCCESS' : 'FAILED'}`);
  });

  const successCount = results.filter(r => r.result.success).length;
  console.log(`\n🎯 ${successCount}/${results.length} tests passed`);

  if (successCount === 0) {
    console.log('\n💡 Troubleshooting:');
    console.log('1. Check if backend server is running on http://127.0.0.1:8000');
    console.log('2. Verify JWT token is valid and not expired');
    console.log('3. Check backend logs for detailed error messages');
    console.log('4. Ensure teacher_id=1 exists in the database');
  }
}

main().catch(console.error);
