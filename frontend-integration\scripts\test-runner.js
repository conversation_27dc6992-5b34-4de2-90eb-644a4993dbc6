#!/usr/bin/env node

/**
 * Test Runner Script
 * 
 * This script provides a convenient way to run different types of tests
 * with proper configuration and reporting.
 */

const { execSync } = require('child_process');
const path = require('path');

// ANSI color codes for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function runCommand(command, description) {
  log(`\n🚀 ${description}`, 'cyan');
  log(`Running: ${command}`, 'yellow');
  
  try {
    execSync(command, { 
      stdio: 'inherit',
      cwd: process.cwd()
    });
    log(`✅ ${description} completed successfully`, 'green');
    return true;
  } catch (error) {
    log(`❌ ${description} failed`, 'red');
    return false;
  }
}

function main() {
  const args = process.argv.slice(2);
  const command = args[0] || 'help';

  log('🧪 School Management System - Test Runner', 'bright');
  log('=' .repeat(50), 'blue');

  switch (command) {
    case 'all':
      log('\n📋 Running all tests...', 'magenta');
      runCommand('npm test -- --watchAll=false', 'All Tests');
      break;

    case 'watch':
      log('\n👀 Running tests in watch mode...', 'magenta');
      runCommand('npm run test:watch', 'Watch Mode Tests');
      break;

    case 'coverage':
      log('\n📊 Running tests with coverage...', 'magenta');
      runCommand('npm run test:coverage', 'Coverage Tests');
      break;

    case 'ci':
      log('\n🔄 Running tests in CI mode...', 'magenta');
      runCommand('npm run test:ci', 'CI Tests');
      break;

    case 'unit':
      log('\n🔧 Running unit tests...', 'magenta');
      runCommand('npm test -- --testPathPattern="__tests__/(lib|hooks|utils)" --watchAll=false', 'Unit Tests');
      break;

    case 'components':
      log('\n🎨 Running component tests...', 'magenta');
      runCommand('npm test -- --testPathPattern="__tests__/components" --watchAll=false', 'Component Tests');
      break;

    case 'auth':
      log('\n🔐 Running authentication tests...', 'magenta');
      runCommand('npm test -- --testPathPattern="auth" --watchAll=false', 'Authentication Tests');
      break;

    case 'teachers':
      log('\n👩‍🏫 Running teacher module tests...', 'magenta');
      runCommand('npm test -- --testPathPattern="teachers" --watchAll=false', 'Teacher Module Tests');
      break;

    case 'students':
      log('\n🎓 Running student module tests...', 'magenta');
      runCommand('npm test -- --testPathPattern="students" --watchAll=false', 'Student Module Tests');
      break;

    case 'lint':
      log('\n🔍 Running linting...', 'magenta');
      runCommand('npm run lint', 'ESLint');
      break;

    case 'type-check':
      log('\n📝 Running type checking...', 'magenta');
      runCommand('npm run type-check', 'TypeScript Type Check');
      break;

    case 'quality':
      log('\n✨ Running full quality check...', 'magenta');
      const steps = [
        { cmd: 'npm run type-check', desc: 'TypeScript Type Check' },
        { cmd: 'npm run lint', desc: 'ESLint' },
        { cmd: 'npm run test:ci', desc: 'Tests' },
      ];
      
      let allPassed = true;
      for (const step of steps) {
        if (!runCommand(step.cmd, step.desc)) {
          allPassed = false;
          break;
        }
      }
      
      if (allPassed) {
        log('\n🎉 All quality checks passed!', 'green');
      } else {
        log('\n💥 Quality checks failed!', 'red');
        process.exit(1);
      }
      break;

    case 'help':
    default:
      log('\n📖 Available Commands:', 'bright');
      log('');
      log('  all         - Run all tests once', 'cyan');
      log('  watch       - Run tests in watch mode', 'cyan');
      log('  coverage    - Run tests with coverage report', 'cyan');
      log('  ci          - Run tests in CI mode', 'cyan');
      log('  unit        - Run unit tests only', 'cyan');
      log('  components  - Run component tests only', 'cyan');
      log('  auth        - Run authentication tests', 'cyan');
      log('  teachers    - Run teacher module tests', 'cyan');
      log('  students    - Run student module tests', 'cyan');
      log('  lint        - Run ESLint', 'cyan');
      log('  type-check  - Run TypeScript type checking', 'cyan');
      log('  quality     - Run full quality check (types + lint + tests)', 'cyan');
      log('  help        - Show this help message', 'cyan');
      log('');
      log('📝 Examples:', 'bright');
      log('  node scripts/test-runner.js all', 'yellow');
      log('  node scripts/test-runner.js coverage', 'yellow');
      log('  node scripts/test-runner.js quality', 'yellow');
      log('');
      break;
  }
}

if (require.main === module) {
  main();
}

module.exports = { runCommand, log };
