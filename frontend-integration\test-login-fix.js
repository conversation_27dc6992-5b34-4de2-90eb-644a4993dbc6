#!/usr/bin/env node

/**
 * Test Login Fix - Verify 404 Error Resolution
 * 
 * This script tests the login flow to ensure:
 * 1. Next.js proxy forwards requests correctly
 * 2. API client uses correct baseURL
 * 3. Auth endpoints resolve to correct paths
 * 4. Login request uses proper form-data format
 */

import axios from 'axios';

// Test configuration
const FRONTEND_URL = 'http://localhost:3000';
const BACKEND_URL = 'http://127.0.0.1:8000';

// Test credentials (adjust as needed)
const TEST_CREDENTIALS = {
  username: 'admin',
  password: 'admin123'
};

async function testBackendDirect() {
  console.log('\n🔧 Testing DIRECT backend authentication...');
  console.log(`   URL: ${BACKEND_URL}/api/v1/auth/login`);
  
  try {
    // Test with URL-encoded form data (correct format)
    const formData = new URLSearchParams();
    formData.append('username', TEST_CREDENTIALS.username);
    formData.append('password', TEST_CREDENTIALS.password);
    
    const response = await axios.post(`${BACKEND_URL}/api/v1/auth/login`, formData.toString(), {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        'Accept': 'application/json'
      },
      timeout: 10000
    });
    
    console.log('✅ Direct backend login SUCCESS');
    console.log(`   Status: ${response.status}`);
    console.log(`   Token received: ${!!response.data.access_token}`);
    console.log(`   User: ${response.data.user?.username || 'N/A'}`);
    return true;
  } catch (error) {
    console.log('❌ Direct backend login FAILED');
    console.log(`   Status: ${error.response?.status || 'Network Error'}`);
    console.log(`   Error: ${error.response?.data?.detail || error.message}`);
    return false;
  }
}

async function testNextjsProxy() {
  console.log('\n🌐 Testing NEXT.JS proxy authentication...');
  console.log(`   URL: ${FRONTEND_URL}/api/v1/auth/login`);
  
  try {
    // Test with URL-encoded form data (correct format)
    const formData = new URLSearchParams();
    formData.append('username', TEST_CREDENTIALS.username);
    formData.append('password', TEST_CREDENTIALS.password);
    
    const response = await axios.post(`${FRONTEND_URL}/api/v1/auth/login`, formData.toString(), {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        'Accept': 'application/json'
      },
      timeout: 10000
    });
    
    console.log('✅ Next.js proxy login SUCCESS');
    console.log(`   Status: ${response.status}`);
    console.log(`   Token received: ${!!response.data.access_token}`);
    console.log(`   User: ${response.data.user?.username || 'N/A'}`);
    return true;
  } catch (error) {
    console.log('❌ Next.js proxy login FAILED');
    console.log(`   Status: ${error.response?.status || 'Network Error'}`);
    console.log(`   Error: ${error.response?.data?.detail || error.message}`);
    
    // Check for specific 404 error
    if (error.response?.status === 404) {
      console.log('🚨 404 ERROR DETECTED - This indicates proxy configuration issue');
      console.log('   Expected: Request should reach backend /api/v1/auth/login');
      console.log('   Actual: Request is hitting wrong endpoint or not being proxied');
    }
    
    return false;
  }
}

async function testMeEndpoint() {
  console.log('\n👤 Testing /me endpoint through proxy...');
  console.log(`   URL: ${FRONTEND_URL}/api/v1/auth/me`);
  
  try {
    const response = await axios.get(`${FRONTEND_URL}/api/v1/auth/me`, {
      headers: {
        'Accept': 'application/json'
      },
      timeout: 10000
    });
    
    console.log('✅ /me endpoint accessible (should return 401 without auth)');
    console.log(`   Status: ${response.status}`);
  } catch (error) {
    if (error.response?.status === 401) {
      console.log('✅ /me endpoint working correctly (401 Unauthorized as expected)');
      console.log(`   Status: ${error.response.status}`);
      return true;
    } else if (error.response?.status === 404) {
      console.log('❌ /me endpoint returns 404 - proxy issue');
      console.log(`   Status: ${error.response.status}`);
      return false;
    } else {
      console.log(`⚠️  /me endpoint unexpected response: ${error.response?.status || 'Network Error'}`);
      return false;
    }
  }
}

async function main() {
  console.log('🧪 Login Fix Verification Test');
  console.log('================================');
  
  const results = {
    backendDirect: false,
    nextjsProxy: false,
    meEndpoint: false
  };
  
  // Test 1: Direct backend
  results.backendDirect = await testBackendDirect();
  
  // Test 2: Next.js proxy
  results.nextjsProxy = await testNextjsProxy();
  
  // Test 3: /me endpoint
  results.meEndpoint = await testMeEndpoint();
  
  // Summary
  console.log('\n📊 Test Results Summary');
  console.log('========================');
  console.log(`Direct Backend:  ${results.backendDirect ? '✅ PASS' : '❌ FAIL'}`);
  console.log(`Next.js Proxy:   ${results.nextjsProxy ? '✅ PASS' : '❌ FAIL'}`);
  console.log(`/me Endpoint:    ${results.meEndpoint ? '✅ PASS' : '❌ FAIL'}`);
  
  if (results.backendDirect && results.nextjsProxy && results.meEndpoint) {
    console.log('\n🎉 ALL TESTS PASSED - Login fix is working!');
    console.log('   Frontend can now successfully authenticate through Next.js proxy');
    console.log('   Expected flow: Frontend → Next.js proxy → Backend');
    process.exit(0);
  } else {
    console.log('\n🚨 SOME TESTS FAILED - Issues detected:');
    
    if (!results.backendDirect) {
      console.log('   - Backend not responding or credentials wrong');
      console.log('   - Check: Backend running on port 8000, admin user exists');
    }
    
    if (!results.nextjsProxy) {
      console.log('   - Next.js proxy not working correctly');
      console.log('   - Check: next.config.js rewrites, frontend running on port 3000');
    }
    
    if (!results.meEndpoint) {
      console.log('   - Auth endpoints not accessible through proxy');
      console.log('   - Check: API client baseURL, auth-config paths');
    }
    
    process.exit(1);
  }
}

// Run the test
main().catch(error => {
  console.error('\n💥 Test script error:', error.message);
  process.exit(1);
});
