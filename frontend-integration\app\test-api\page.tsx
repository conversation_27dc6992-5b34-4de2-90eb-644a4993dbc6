'use client';

import { But<PERSON> } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { useState } from 'react';

export default function TestApiPage() {
  const [results, setResults] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);

  const addResult = (test: string, status: string, data: any, error?: string) => {
    setResults(prev => [
      ...prev,
      { test, status, data, error, timestamp: new Date().toISOString() },
    ]);
  };

  const testDirectFetch = async () => {
    setLoading(true);
    setResults([]);

    try {
      // Debug token storage
      const authToken = localStorage.getItem('auth.token');
      const accessToken = localStorage.getItem('access_token');
      addResult('Token Debug', 'INFO', {
        'auth.token': authToken ? `${authToken.substring(0, 20)}...` : 'Not found',
        access_token: accessToken ? `${accessToken.substring(0, 20)}...` : 'Not found',
      });

      // Test 1: Health endpoint
      addResult('Health Check', 'TESTING', 'Fetching /api/v1/health...');

      const healthResponse = await fetch('http://127.0.0.1:8000/api/v1/health');
      if (healthResponse.ok) {
        const healthData = await healthResponse.json();
        addResult('Health Check', 'SUCCESS', healthData);
      } else {
        addResult(
          'Health Check',
          'FAILED',
          null,
          `${healthResponse.status} ${healthResponse.statusText}`
        );
      }

      // Test 2: Teachers endpoint
      addResult('Teachers API', 'TESTING', 'Fetching /api/v1/teachers/...');

      const teachersResponse = await fetch('http://127.0.0.1:8000/api/v1/teachers/');
      if (teachersResponse.ok) {
        const teachersData = await teachersResponse.json();
        addResult('Teachers API', 'SUCCESS', teachersData);
      } else {
        addResult(
          'Teachers API',
          'FAILED',
          null,
          `${teachersResponse.status} ${teachersResponse.statusText}`
        );
      }

      // Test 3: With auth header (if token exists)
      const token = localStorage.getItem('access_token') || localStorage.getItem('auth.token');
      if (token) {
        addResult('Auth Test', 'TESTING', 'Testing with auth token...');

        const authResponse = await fetch('http://127.0.0.1:8000/api/v1/teachers/', {
          headers: {
            Authorization: `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
        });

        if (authResponse.ok) {
          const authData = await authResponse.json();
          addResult('Auth Test', 'SUCCESS', authData);
        } else {
          addResult(
            'Auth Test',
            'FAILED',
            null,
            `${authResponse.status} ${authResponse.statusText}`
          );
        }
      } else {
        addResult('Auth Test', 'SKIPPED', 'No auth token found in localStorage');
      }
    } catch (error) {
      addResult(
        'Connection Test',
        'ERROR',
        null,
        error instanceof Error ? error.message : 'Unknown error'
      );
    } finally {
      setLoading(false);
    }
  };

  const testViaApiClient = async () => {
    setLoading(true);
    setResults([]);

    try {
      // Test using our API client
      const { api } = await import('@/lib/api');

      addResult('API Client Test', 'TESTING', 'Using lib/api.ts client...');

      const response = await api.get('/api/v1/teachers/');
      addResult('API Client Test', 'SUCCESS', response.data);
    } catch (error: any) {
      addResult('API Client Test', 'ERROR', null, error.message || 'API client error');
    } finally {
      setLoading(false);
    }
  };

  const clearResults = () => {
    setResults([]);
  };

  return (
    <div className='container mx-auto p-6 space-y-6'>
      <Card>
        <CardHeader>
          <CardTitle>API Connection Test</CardTitle>
        </CardHeader>
        <CardContent className='space-y-4'>
          <div className='flex gap-4'>
            <Button onClick={testDirectFetch} disabled={loading}>
              Test Direct Fetch
            </Button>
            <Button onClick={testViaApiClient} disabled={loading} variant='outline'>
              Test API Client
            </Button>
            <Button onClick={clearResults} variant='ghost'>
              Clear Results
            </Button>
          </div>

          {loading && <p className='text-blue-600'>Testing API connections...</p>}

          <div className='space-y-2'>
            {results.map((result, index) => (
              <Card
                key={index}
                className={`border-l-4 ${
                  result.status === 'SUCCESS'
                    ? 'border-l-green-500'
                    : result.status === 'FAILED' || result.status === 'ERROR'
                    ? 'border-l-red-500'
                    : result.status === 'TESTING'
                    ? 'border-l-blue-500'
                    : 'border-l-gray-500'
                }`}
              >
                <CardContent className='p-4'>
                  <div className='flex justify-between items-start'>
                    <div>
                      <h4 className='font-semibold'>{result.test}</h4>
                      <p
                        className={`text-sm ${
                          result.status === 'SUCCESS'
                            ? 'text-green-600'
                            : result.status === 'FAILED' || result.status === 'ERROR'
                            ? 'text-red-600'
                            : result.status === 'TESTING'
                            ? 'text-blue-600'
                            : 'text-gray-600'
                        }`}
                      >
                        {result.status}
                      </p>
                    </div>
                    <span className='text-xs text-gray-500'>
                      {new Date(result.timestamp).toLocaleTimeString()}
                    </span>
                  </div>

                  {result.error && (
                    <p className='text-red-600 text-sm mt-2'>Error: {result.error}</p>
                  )}

                  {result.data && (
                    <details className='mt-2'>
                      <summary className='text-sm cursor-pointer text-gray-600'>
                        View Response Data
                      </summary>
                      <pre className='text-xs bg-gray-100 p-2 rounded mt-1 overflow-auto'>
                        {typeof result.data === 'string'
                          ? result.data
                          : JSON.stringify(result.data, null, 2)}
                      </pre>
                    </details>
                  )}
                </CardContent>
              </Card>
            ))}
          </div>

          {results.length === 0 && !loading && (
            <p className='text-gray-500 text-center py-8'>
              Click a test button to check API connectivity
            </p>
          )}
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Debug Information</CardTitle>
        </CardHeader>
        <CardContent>
          <div className='space-y-2 text-sm'>
            <p>
              <strong>API URL:</strong> {process.env.NEXT_PUBLIC_API_URL || 'http://127.0.0.1:8000'}
            </p>
            <p>
              <strong>Current URL:</strong>{' '}
              {typeof window !== 'undefined' ? window.location.href : 'N/A'}
            </p>
            <p>
              <strong>Auth Token:</strong>{' '}
              {typeof window !== 'undefined' &&
              (localStorage.getItem('access_token') || localStorage.getItem('auth.token'))
                ? 'Present'
                : 'Not found'}
            </p>
            <p>
              <strong>User Agent:</strong>{' '}
              {typeof window !== 'undefined'
                ? navigator.userAgent.substring(0, 100) + '...'
                : 'N/A'}
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
