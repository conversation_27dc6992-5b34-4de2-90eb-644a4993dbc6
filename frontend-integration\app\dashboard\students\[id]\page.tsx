'use client';

/**
 * Student Detail Page
 * 
 * Shows comprehensive student information including:
 * - Personal details
 * - Academic information
 * - Guardian details
 * - Photo management
 * - Quick actions
 */

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { 
  ArrowLeft, 
  Edit, 
  Camera, 
  Download, 
  Trash2, 
  Power, 
  PowerOff,
  User,
  Mail,
  Phone,
  MapPin,
  Calendar,
  GraduationCap,
  Users,
  FileText
} from 'lucide-react';

// Components
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import { useToast } from '@/hooks/use-toast';

// Hooks and Services
import { useStudent, useDeleteStudent, useToggleStudent, useUploadStudentPhoto } from '@/hooks/useStudents';
import { Student } from '@/types';

// Mock data for classes and sections
const mockClasses = [
  { id: 1, name: 'Class 1A' },
  { id: 2, name: 'Class 1B' },
  { id: 3, name: 'Class 2A' },
  { id: 4, name: 'Class 2B' },
  { id: 5, name: 'Class 3A' },
];

const mockSections = [
  { id: 1, name: 'Section A' },
  { id: 2, name: 'Section B' },
  { id: 3, name: 'Section C' },
];

interface StudentDetailPageProps {
  params: {
    id: string;
  };
}

export default function StudentDetailPage({ params }: StudentDetailPageProps) {
  const router = useRouter();
  const { toast } = useToast();
  const [isUploading, setIsUploading] = useState(false);

  // API queries
  const { data: student, isLoading, error } = useStudent(params.id);
  const deleteStudentMutation = useDeleteStudent();
  const toggleStudentMutation = useToggleStudent();
  const uploadPhotoMutation = useUploadStudentPhoto();

  // Handlers
  const handleDelete = async () => {
    if (!student) return;
    
    if (confirm('Are you sure you want to delete this student? This action cannot be undone.')) {
      try {
        await deleteStudentMutation.mutateAsync(student.id);
        toast({
          title: "Success",
          description: "Student deleted successfully!",
        });
        router.push('/dashboard/students');
      } catch (error) {
        console.error('Failed to delete student:', error);
      }
    }
  };

  const handleToggleStatus = async () => {
    if (!student) return;
    
    try {
      await toggleStudentMutation.mutateAsync(student.id);
    } catch (error) {
      console.error('Failed to toggle student status:', error);
    }
  };

  const handlePhotoUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file || !student) return;

    setIsUploading(true);
    try {
      await uploadPhotoMutation.mutateAsync({ id: student.id, file });
      toast({
        title: "Success",
        description: "Photo uploaded successfully!",
      });
    } catch (error) {
      console.error('Failed to upload photo:', error);
      toast({
        title: "Error",
        description: "Failed to upload photo",
        variant: "destructive",
      });
    } finally {
      setIsUploading(false);
    }
  };

  if (isLoading) {
    return (
      <div className="container mx-auto p-6 space-y-6">
        <div className="flex items-center gap-4">
          <Skeleton className="h-8 w-8" />
          <Skeleton className="h-8 w-48" />
        </div>
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          <div className="lg:col-span-2 space-y-6">
            <Skeleton className="h-64 w-full" />
            <Skeleton className="h-32 w-full" />
          </div>
          <div className="space-y-6">
            <Skeleton className="h-32 w-full" />
            <Skeleton className="h-32 w-full" />
          </div>
        </div>
      </div>
    );
  }

  if (error || !student) {
    return (
      <div className="container mx-auto p-6">
        <Card className="border-red-200 bg-red-50">
          <CardContent className="p-6 text-center">
            <h3 className="text-lg font-medium text-red-800 mb-2">Student Not Found</h3>
            <p className="text-red-600 mb-4">
              {error instanceof Error ? error.message : 'The requested student could not be found.'}
            </p>
            <Button onClick={() => router.push('/dashboard/students')}>
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to Students
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  const className = mockClasses.find(c => c.id === student.class_id)?.name || 'N/A';
  const sectionName = mockSections.find(s => s.id === student.section_id)?.name || 'N/A';
  const fullName = `${student.first_name} ${student.last_name}`;
  const initials = `${student.first_name[0]}${student.last_name[0]}`;

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button
            variant="outline"
            size="sm"
            onClick={() => router.push('/dashboard/students')}
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back
          </Button>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">{fullName}</h1>
            <p className="text-gray-600">Student Details</p>
          </div>
        </div>
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            onClick={handleToggleStatus}
            disabled={toggleStudentMutation.isLoading}
          >
            {student.is_active ? (
              <>
                <PowerOff className="w-4 h-4 mr-2" />
                Deactivate
              </>
            ) : (
              <>
                <Power className="w-4 h-4 mr-2" />
                Activate
              </>
            )}
          </Button>
          <Button
            variant="outline"
            onClick={() => router.push(`/dashboard/students/${student.id}/edit`)}
          >
            <Edit className="w-4 h-4 mr-2" />
            Edit
          </Button>
          <Button
            variant="destructive"
            onClick={handleDelete}
            disabled={deleteStudentMutation.isLoading}
          >
            <Trash2 className="w-4 h-4 mr-2" />
            Delete
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Main Content */}
        <div className="lg:col-span-2 space-y-6">
          {/* Personal Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <User className="w-5 h-5" />
                Personal Information
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium text-gray-500">Registration Number</label>
                  <p className="text-lg font-mono">{student.reg_no}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">Full Name</label>
                  <p className="text-lg">{fullName}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">Gender</label>
                  <p className="text-lg capitalize">{student.gender}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">Date of Birth</label>
                  <p className="text-lg">
                    {student.dob ? new Date(student.dob).toLocaleDateString() : 'N/A'}
                  </p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">Email</label>
                  <p className="text-lg flex items-center gap-2">
                    <Mail className="w-4 h-4 text-gray-400" />
                    {student.email || 'N/A'}
                  </p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">Address</label>
                  <p className="text-lg flex items-center gap-2">
                    <MapPin className="w-4 h-4 text-gray-400" />
                    {student.address || 'N/A'}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Academic Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <GraduationCap className="w-5 h-5" />
                Academic Information
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium text-gray-500">Class</label>
                  <p className="text-lg">{className}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">Section</label>
                  <p className="text-lg">{sectionName}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">Status</label>
                  <Badge 
                    variant={student.is_active ? "default" : "secondary"}
                    className={student.is_active ? "bg-green-100 text-green-800" : "bg-gray-100 text-gray-800"}
                  >
                    {student.is_active ? 'Active' : 'Inactive'}
                  </Badge>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">Enrollment Date</label>
                  <p className="text-lg flex items-center gap-2">
                    <Calendar className="w-4 h-4 text-gray-400" />
                    {new Date(student.created_at).toLocaleDateString()}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Guardian Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Users className="w-5 h-5" />
                Guardian Information
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium text-gray-500">Guardian Name</label>
                  <p className="text-lg">{student.guardian_name || 'N/A'}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">Guardian Phone</label>
                  <p className="text-lg flex items-center gap-2">
                    <Phone className="w-4 h-4 text-gray-400" />
                    {student.guardian_phone || 'N/A'}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Photo */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <span>Photo</span>
                <label className="cursor-pointer">
                  <input
                    type="file"
                    accept="image/*"
                    onChange={handlePhotoUpload}
                    className="hidden"
                    disabled={isUploading}
                  />
                  <Button variant="outline" size="sm" disabled={isUploading}>
                    <Camera className="w-4 h-4 mr-2" />
                    {isUploading ? 'Uploading...' : 'Upload'}
                  </Button>
                </label>
              </CardTitle>
            </CardHeader>
            <CardContent className="flex justify-center">
              <Avatar className="h-32 w-32">
                <AvatarImage src={student.photo_url} alt={fullName} />
                <AvatarFallback className="bg-gradient-to-br from-blue-500 to-purple-600 text-white text-2xl">
                  {initials}
                </AvatarFallback>
              </Avatar>
            </CardContent>
          </Card>

          {/* Quick Actions */}
          <Card>
            <CardHeader>
              <CardTitle>Quick Actions</CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              <Button 
                variant="outline" 
                className="w-full justify-start"
                onClick={() => router.push(`/dashboard/students/${student.id}/edit`)}
              >
                <Edit className="w-4 h-4 mr-2" />
                Edit Student
              </Button>
              <Button 
                variant="outline" 
                className="w-full justify-start"
                onClick={() => router.push(`/dashboard/attendance?student_id=${student.id}`)}
              >
                <FileText className="w-4 h-4 mr-2" />
                View Attendance
              </Button>
              <Button 
                variant="outline" 
                className="w-full justify-start"
                onClick={() => router.push(`/dashboard/grades?student_id=${student.id}`)}
              >
                <GraduationCap className="w-4 h-4 mr-2" />
                View Grades
              </Button>
              <Button 
                variant="outline" 
                className="w-full justify-start"
                onClick={() => router.push(`/dashboard/fees?student_id=${student.id}`)}
              >
                <Download className="w-4 h-4 mr-2" />
                View Fees
              </Button>
            </CardContent>
          </Card>

          {/* Student ID Card */}
          <Card>
            <CardHeader>
              <CardTitle>Student ID</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="text-center">
                <Avatar className="h-16 w-16 mx-auto mb-2">
                  <AvatarImage src={student.photo_url} alt={fullName} />
                  <AvatarFallback className="bg-gradient-to-br from-blue-500 to-purple-600 text-white">
                    {initials}
                  </AvatarFallback>
                </Avatar>
                <h3 className="font-semibold">{fullName}</h3>
                <p className="text-sm text-gray-500">{student.reg_no}</p>
                <p className="text-sm text-gray-500">{className} - {sectionName}</p>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
