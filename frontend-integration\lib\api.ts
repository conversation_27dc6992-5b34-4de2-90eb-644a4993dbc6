// lib/api.ts - Canonical API client with robust error handling
import axios from 'axios';

const base = (process.env.NEXT_PUBLIC_API_URL || 'http://127.0.0.1:8000').replace(/\/$/, '');
export const api = axios.create({
  baseURL: base, // e.g. http://127.0.0.1:8000 (endpoints will include /api/v1)
  headers: { 'Content-Type': 'application/json' },
  timeout: 30000,
  withCredentials: true,
});

// Pull token from Zustand or localStorage (no SSR break)
const getToken = () => {
  try {
    // Prefer Zustand if available
    const z = (globalThis as any)?.__zustandStore__?.auth?.getState?.();
    if (z?.token) return z.token;
  } catch {}

  // Fallback to localStorage
  if (typeof window !== 'undefined') {
    return localStorage.getItem('access_token') || localStorage.getItem('auth.token');
  }
  return null;
};

// Request interceptor - attach token and log requests
api.interceptors.request.use(config => {
  const token = getToken();
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }

  if (process.env.NODE_ENV === 'development') {
    // Debug the EXACT URL used
    console.debug('[API] →', config.method?.toUpperCase(), `${config.baseURL}${config.url}`);
    if (token) {
      console.debug('[API] 🔑 Token attached:', token.substring(0, 20) + '...');
    } else {
      console.warn('[API] ⚠️ No token found');
    }
  }
  return config;
});

// Response interceptor - handle errors gracefully
api.interceptors.response.use(
  res => {
    if (process.env.NODE_ENV === 'development') {
      console.debug('[API] ✓', res.status, res.config.method?.toUpperCase(), res.config.url);
    }
    return res;
  },
  err => {
    if (process.env.NODE_ENV === 'development') {
      const status = err?.response?.status;
      const method = err?.config?.method?.toUpperCase();
      const url = err?.config?.url;
      console.error(`[API] ✖ ${status} ${method} ${url}`, err?.response?.data || err.message);
    }
    throw err;
  }
);

export default api;
