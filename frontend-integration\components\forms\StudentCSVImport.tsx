'use client';

import { useState, useRef } from 'react';
import { Upload, Download, FileText, AlertCircle, CheckCircle, X, Loader2 } from 'lucide-react';

// UI Components
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';

// Services
import { StudentService } from '@/api/services/studentService';
import type { StudentImportResponse } from '@/types';

interface StudentCSVImportProps {
  onImportComplete?: (result: StudentImportResponse) => void;
  onCancel?: () => void;
}

interface ImportState {
  status: 'idle' | 'uploading' | 'processing' | 'completed' | 'error';
  progress: number;
  result?: StudentImportResponse;
  error?: string;
}

/**
 * StudentCSVImport Component
 * 
 * Provides comprehensive CSV import functionality with:
 * - File validation and pre-flight checks
 * - Sample CSV download
 * - Progress tracking
 * - Detailed error reporting
 * - Success feedback
 */
export function StudentCSVImport({ onImportComplete, onCancel }: StudentCSVImportProps) {
  const [importState, setImportState] = useState<ImportState>({
    status: 'idle',
    progress: 0,
  });
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Handle file selection
  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    // Validate file type
    if (!file.name.toLowerCase().endsWith('.csv')) {
      setImportState({
        status: 'error',
        progress: 0,
        error: 'Please select a CSV file (.csv extension required)',
      });
      return;
    }

    // Validate file size (10MB limit)
    if (file.size > 10 * 1024 * 1024) {
      setImportState({
        status: 'error',
        progress: 0,
        error: 'File size must be less than 10MB',
      });
      return;
    }

    setSelectedFile(file);
    setImportState({ status: 'idle', progress: 0 });
  };

  // Handle CSV import
  const handleImport = async () => {
    if (!selectedFile) return;

    setImportState({ status: 'uploading', progress: 10 });

    try {
      // Simulate progress updates
      const progressInterval = setInterval(() => {
        setImportState(prev => ({
          ...prev,
          progress: Math.min(prev.progress + 10, 90),
        }));
      }, 200);

      const result = await StudentService.importStudents(selectedFile);
      
      clearInterval(progressInterval);
      
      setImportState({
        status: 'completed',
        progress: 100,
        result,
      });

      // Call completion callback
      onImportComplete?.(result);

    } catch (error: any) {
      setImportState({
        status: 'error',
        progress: 0,
        error: error.message || 'Failed to import CSV file',
      });
    }
  };

  // Handle sample CSV download
  const handleDownloadSample = async () => {
    try {
      await StudentService.downloadSampleCSV();
    } catch (error: any) {
      setImportState({
        status: 'error',
        progress: 0,
        error: error.message || 'Failed to download sample CSV',
      });
    }
  };

  // Reset component state
  const handleReset = () => {
    setSelectedFile(null);
    setImportState({ status: 'idle', progress: 0 });
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Upload className="w-5 h-5" />
          Import Students from CSV
        </CardTitle>
        <CardDescription>
          Upload a CSV file to import multiple students at once. Download the sample file to see the required format.
        </CardDescription>
      </CardHeader>

      <CardContent className="space-y-6">
        {/* Sample CSV Download */}
        <div className="flex items-center justify-between p-4 bg-blue-50 rounded-lg border border-blue-200">
          <div className="flex items-center gap-3">
            <FileText className="w-5 h-5 text-blue-600" />
            <div>
              <p className="font-medium text-blue-900">Need the correct format?</p>
              <p className="text-sm text-blue-700">Download our sample CSV file with the required headers</p>
            </div>
          </div>
          <Button variant="outline" size="sm" onClick={handleDownloadSample}>
            <Download className="w-4 h-4 mr-2" />
            Sample CSV
          </Button>
        </div>

        {/* File Upload Section */}
        <div className="space-y-4">
          <div className="flex items-center gap-4">
            <input
              ref={fileInputRef}
              type="file"
              accept=".csv"
              onChange={handleFileSelect}
              className="hidden"
            />
            <Button
              variant="outline"
              onClick={() => fileInputRef.current?.click()}
              disabled={importState.status === 'uploading' || importState.status === 'processing'}
            >
              <Upload className="w-4 h-4 mr-2" />
              Select CSV File
            </Button>
            
            {selectedFile && (
              <div className="flex items-center gap-2">
                <FileText className="w-4 h-4 text-green-600" />
                <span className="text-sm font-medium">{selectedFile.name}</span>
                <Badge variant="secondary">
                  {(selectedFile.size / 1024).toFixed(1)} KB
                </Badge>
              </div>
            )}
          </div>

          {/* Progress Bar */}
          {(importState.status === 'uploading' || importState.status === 'processing') && (
            <div className="space-y-2">
              <div className="flex items-center justify-between text-sm">
                <span>
                  {importState.status === 'uploading' ? 'Uploading...' : 'Processing...'}
                </span>
                <span>{importState.progress}%</span>
              </div>
              <Progress value={importState.progress} className="w-full" />
            </div>
          )}
        </div>

        {/* Error Display */}
        {importState.status === 'error' && importState.error && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{importState.error}</AlertDescription>
          </Alert>
        )}

        {/* Success Display */}
        {importState.status === 'completed' && importState.result && (
          <Alert className="border-green-200 bg-green-50">
            <CheckCircle className="h-4 w-4 text-green-600" />
            <AlertDescription className="text-green-800">
              <div className="space-y-2">
                <p className="font-medium">Import completed successfully!</p>
                <div className="grid grid-cols-3 gap-4 text-sm">
                  <div>
                    <span className="font-medium text-green-900">Created:</span>
                    <span className="ml-1">{importState.result.created}</span>
                  </div>
                  <div>
                    <span className="font-medium text-green-900">Updated:</span>
                    <span className="ml-1">{importState.result.updated}</span>
                  </div>
                  <div>
                    <span className="font-medium text-red-900">Errors:</span>
                    <span className="ml-1">{importState.result.errors.length}</span>
                  </div>
                </div>
                
                {/* Error Details */}
                {importState.result.errors.length > 0 && (
                  <div className="mt-3 p-3 bg-red-50 rounded border border-red-200">
                    <p className="font-medium text-red-900 mb-2">Import Errors:</p>
                    <div className="space-y-1 max-h-32 overflow-y-auto">
                      {importState.result.errors.map((error, index) => (
                        <div key={index} className="text-sm text-red-800">
                          <span className="font-medium">Row {error.row}:</span> {error.error}
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </AlertDescription>
          </Alert>
        )}

        {/* Action Buttons */}
        <div className="flex justify-end space-x-4 pt-4 border-t">
          <Button variant="outline" onClick={onCancel || handleReset}>
            <X className="w-4 h-4 mr-2" />
            {importState.status === 'completed' ? 'Close' : 'Cancel'}
          </Button>

          {importState.status === 'completed' ? (
            <Button onClick={handleReset}>
              Import Another File
            </Button>
          ) : (
            <Button
              onClick={handleImport}
              disabled={!selectedFile || importState.status === 'uploading' || importState.status === 'processing'}
            >
              {importState.status === 'uploading' || importState.status === 'processing' ? (
                <Loader2 className="w-4 h-4 mr-2 animate-spin" />
              ) : (
                <Upload className="w-4 h-4 mr-2" />
              )}
              Import Students
            </Button>
          )}
        </div>
      </CardContent>
    </Card>
  );
}

export default StudentCSVImport;
