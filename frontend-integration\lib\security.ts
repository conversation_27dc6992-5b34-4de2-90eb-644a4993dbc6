/**
 * Security utilities for SUPER_ADMIN role implementation
 * Provides enhanced security features and audit logging
 */

import type { Role } from '@/stores/authStore';

// Security configuration
export const SECURITY_CONFIG = {
  SESSION_TIMEOUT: 30 * 60 * 1000, // 30 minutes in milliseconds
  MAX_LOGIN_ATTEMPTS: 3,
  LOCKOUT_DURATION: 15 * 60 * 1000, // 15 minutes
  REQUIRE_MFA_FOR_SUPER_ADMIN: true,
  PASSWORD_MIN_LENGTH: 12,
  PASSWORD_REQUIRE_SPECIAL_CHARS: true,
} as const;

// Action types for audit logging
export type AuditAction = 
  | 'CREATE_CLASS'
  | 'UPDATE_CLASS' 
  | 'DELETE_CLASS'
  | 'BULK_DELETE_CLASSES'
  | 'CREATE_USER'
  | 'UPDATE_USER'
  | 'DELETE_USER'
  | 'CHANGE_USER_ROLE'
  | 'ACCESS_SYSTEM_SETTINGS'
  | 'BACKUP_SYSTEM'
  | 'VIEW_AUDIT_LOGS'
  | 'LOGIN'
  | 'LOGOUT'
  | 'FAILED_LOGIN';

// Audit log entry interface
export interface AuditLogEntry {
  id: string;
  userId: string;
  userEmail: string;
  userRole: Role;
  action: AuditAction;
  resource?: string;
  resourceId?: string;
  details?: Record<string, any>;
  timestamp: string;
  ipAddress?: string;
  userAgent?: string;
  success: boolean;
  errorMessage?: string;
}

// Security validation functions
export const validateSuperAdminAction = (role?: Role): boolean => {
  return role === 'SUPER_ADMIN';
};

export const requireSuperAdmin = (role?: Role): void => {
  if (!validateSuperAdminAction(role)) {
    throw new Error('SUPER_ADMIN role required for this action');
  }
};

// Session management
export const isSessionExpired = (lastActivity: number): boolean => {
  return Date.now() - lastActivity > SECURITY_CONFIG.SESSION_TIMEOUT;
};

export const updateLastActivity = (): void => {
  if (typeof window !== 'undefined') {
    localStorage.setItem('lastActivity', Date.now().toString());
  }
};

export const getLastActivity = (): number => {
  if (typeof window !== 'undefined') {
    const lastActivity = localStorage.getItem('lastActivity');
    return lastActivity ? parseInt(lastActivity, 10) : Date.now();
  }
  return Date.now();
};

// Audit logging functions
export const logAuditEvent = async (entry: Omit<AuditLogEntry, 'id' | 'timestamp'>): Promise<void> => {
  const auditEntry: AuditLogEntry = {
    ...entry,
    id: crypto.randomUUID(),
    timestamp: new Date().toISOString(),
  };

  // Store locally for now - in production, send to backend
  if (typeof window !== 'undefined') {
    const existingLogs = JSON.parse(localStorage.getItem('auditLogs') || '[]');
    existingLogs.push(auditEntry);
    
    // Keep only last 1000 entries
    if (existingLogs.length > 1000) {
      existingLogs.splice(0, existingLogs.length - 1000);
    }
    
    localStorage.setItem('auditLogs', JSON.stringify(existingLogs));
  }

  // In production, also send to backend API
  try {
    // await fetch('/api/v1/audit-logs', {
    //   method: 'POST',
    //   headers: { 'Content-Type': 'application/json' },
    //   body: JSON.stringify(auditEntry)
    // });
    console.log('🔍 Audit Log:', auditEntry);
  } catch (error) {
    console.error('Failed to log audit event:', error);
  }
};

// Get audit logs (SUPER_ADMIN only)
export const getAuditLogs = (role?: Role): AuditLogEntry[] => {
  requireSuperAdmin(role);
  
  if (typeof window !== 'undefined') {
    return JSON.parse(localStorage.getItem('auditLogs') || '[]');
  }
  return [];
};

// Confirmation dialog utilities for destructive actions
export const confirmDestructiveAction = (
  action: string,
  resourceName: string,
  callback: () => void
): void => {
  const confirmed = window.confirm(
    `⚠️ DESTRUCTIVE ACTION\n\n` +
    `Are you sure you want to ${action}?\n\n` +
    `Resource: ${resourceName}\n\n` +
    `This action cannot be undone. Type "CONFIRM" to proceed.`
  );

  if (confirmed) {
    const userInput = window.prompt(
      `Please type "CONFIRM" to ${action}:`
    );
    
    if (userInput === 'CONFIRM') {
      callback();
    } else {
      alert('Action cancelled - confirmation text did not match.');
    }
  }
};

// Enhanced confirmation for bulk operations
export const confirmBulkAction = (
  action: string,
  itemCount: number,
  callback: () => void
): void => {
  const confirmed = window.confirm(
    `⚠️ BULK OPERATION\n\n` +
    `Are you sure you want to ${action}?\n\n` +
    `This will affect ${itemCount} items.\n\n` +
    `This action cannot be undone.`
  );

  if (confirmed) {
    const userInput = window.prompt(
      `Please type the number of items (${itemCount}) to confirm:`
    );
    
    if (userInput === itemCount.toString()) {
      callback();
    } else {
      alert('Action cancelled - item count did not match.');
    }
  }
};

// Password strength validation
export const validatePasswordStrength = (password: string): {
  isValid: boolean;
  errors: string[];
} => {
  const errors: string[] = [];

  if (password.length < SECURITY_CONFIG.PASSWORD_MIN_LENGTH) {
    errors.push(`Password must be at least ${SECURITY_CONFIG.PASSWORD_MIN_LENGTH} characters long`);
  }

  if (!/[A-Z]/.test(password)) {
    errors.push('Password must contain at least one uppercase letter');
  }

  if (!/[a-z]/.test(password)) {
    errors.push('Password must contain at least one lowercase letter');
  }

  if (!/\d/.test(password)) {
    errors.push('Password must contain at least one number');
  }

  if (SECURITY_CONFIG.PASSWORD_REQUIRE_SPECIAL_CHARS && !/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
    errors.push('Password must contain at least one special character');
  }

  return {
    isValid: errors.length === 0,
    errors
  };
};

// Multi-factor authentication check
export const requireMFA = (role?: Role): boolean => {
  return role === 'SUPER_ADMIN' && SECURITY_CONFIG.REQUIRE_MFA_FOR_SUPER_ADMIN;
};

// IP address and user agent detection
export const getClientInfo = (): { ipAddress?: string; userAgent?: string } => {
  if (typeof window !== 'undefined') {
    return {
      userAgent: navigator.userAgent,
      // IP address would be obtained from backend in production
      ipAddress: 'client-side-unknown'
    };
  }
  return {};
};

// Security event handlers
export const handleSecurityEvent = async (
  action: AuditAction,
  userId: string,
  userEmail: string,
  userRole: Role,
  success: boolean,
  details?: Record<string, any>,
  errorMessage?: string
): Promise<void> => {
  const clientInfo = getClientInfo();
  
  await logAuditEvent({
    userId,
    userEmail,
    userRole,
    action,
    success,
    details,
    errorMessage,
    ...clientInfo
  });

  // Update last activity on successful actions
  if (success) {
    updateLastActivity();
  }
};

// Export security utilities
export const SecurityUtils = {
  validateSuperAdminAction,
  requireSuperAdmin,
  isSessionExpired,
  updateLastActivity,
  getLastActivity,
  logAuditEvent,
  getAuditLogs,
  confirmDestructiveAction,
  confirmBulkAction,
  validatePasswordStrength,
  requireMFA,
  getClientInfo,
  handleSecurityEvent,
} as const;

// Export types
export type { AuditLogEntry, AuditAction };
export { SECURITY_CONFIG };
