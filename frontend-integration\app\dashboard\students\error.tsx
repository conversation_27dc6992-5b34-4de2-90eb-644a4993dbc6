'use client';

/**
 * Students Error Boundary
 *
 * Handles errors that occur in the students module
 */

import { AlertTriangle, Home, RefreshCw, Users } from 'lucide-react';
import Link from 'next/link';
import { useEffect } from 'react';

import { Alert, AlertDescription } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

interface StudentsErrorProps {
  error: Error & { digest?: string };
  reset: () => void;
}

export default function StudentsError({ error, reset }: StudentsErrorProps) {
  useEffect(() => {
    // Log the error to an error reporting service
    console.error('Students module error:', error);
  }, [error]);

  return (
    <div className='container mx-auto p-4 sm:p-6 lg:p-8'>
      <Card className='max-w-2xl mx-auto border-0 shadow-xl'>
        <CardHeader className='text-center space-y-4'>
          <div className='mx-auto w-16 h-16 bg-red-100 rounded-2xl flex items-center justify-center'>
            <AlertTriangle className='w-8 h-8 text-red-600' />
          </div>
          <div>
            <CardTitle className='text-2xl font-bold text-gray-900 flex items-center justify-center gap-2'>
              <Users className='w-6 h-6' />
              Students Module Error
            </CardTitle>
          </div>
        </CardHeader>

        <CardContent className='space-y-6'>
          <Alert variant='destructive'>
            <AlertTriangle className='h-4 w-4' />
            <AlertDescription className='text-sm'>
              {error.message ||
                'An unexpected error occurred while loading the students module. This could be due to a network issue, server problem, or data loading error.'}
            </AlertDescription>
          </Alert>

          {/* Error Details for Development */}
          {process.env.NODE_ENV === 'development' && (
            <Alert>
              <AlertDescription className='text-xs font-mono bg-gray-50 p-2 rounded'>
                <strong>Error Details:</strong>
                <br />
                {error.stack || error.message}
                {error.digest && (
                  <>
                    <br />
                    <strong>Error ID:</strong> {error.digest}
                  </>
                )}
              </AlertDescription>
            </Alert>
          )}

          <div className='space-y-3'>
            <Button
              onClick={reset}
              className='w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700'
            >
              <RefreshCw className='w-4 h-4 mr-2' />
              Try Again
            </Button>

            <div className='grid grid-cols-1 sm:grid-cols-2 gap-3'>
              <Button variant='outline' className='w-full' asChild>
                <Link href='/dashboard'>
                  <Home className='w-4 h-4 mr-2' />
                  Back to Dashboard
                </Link>
              </Button>

              <Button variant='outline' className='w-full' asChild>
                <Link href='/dashboard/teachers'>
                  <Users className='w-4 h-4 mr-2' />
                  View Teachers
                </Link>
              </Button>
            </div>
          </div>

          <div className='text-center text-sm text-gray-600 space-y-2'>
            <p>
              If the problem persists, please{' '}
              <Link href='/contact' className='text-blue-600 hover:underline'>
                contact support
              </Link>{' '}
              or try refreshing the page.
            </p>

            <div className='flex items-center justify-center space-x-4 text-xs text-gray-500'>
              <span>Error occurred at: {new Date().toLocaleString()}</span>
              {error.digest && <span>ID: {error.digest.slice(0, 8)}</span>}
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
