/**
 * Teachers CRUD API - Simple API using auth helper
 *
 * Provides the exact API requested in the user requirements:
 * - All functions automatically add Authorization header
 * - Consistent error handling
 * - Simple request/response pattern
 */

import { getAuthHeader } from './auth';

const API = process.env.NEXT_PUBLIC_API_URL;

/**
 * Generic request helper with auth headers
 */
async function req(url: string, opts: any = {}) {
  const response = await fetch(`${API}${url}`, {
    ...opts,
    headers: {
      ...getAuthHeader(),
      ...(opts.headers || {}),
    },
  });

  if (!response.ok) {
    throw new Error('Request failed');
  }

  return response.json();
}

/**
 * Get all teachers
 */
export const getTeachers = () => req('/api/v1/teachers/');

/**
 * Get single teacher by ID
 */
export const getTeacher = (id: string) => req(`/api/v1/teachers/${id}`);

/**
 * Create new teacher
 */
export const createTeacher = (data: any) =>
  req('/api/v1/teachers/', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(data),
  });

/**
 * Update existing teacher
 */
export const updateTeacher = (id: string, data: any) =>
  req(`/api/v1/teachers/${id}`, {
    method: 'PUT',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(data),
  });

/**
 * Delete teacher
 */
export const deleteTeacher = (id: string) =>
  req(`/api/v1/teachers/${id}`, {
    method: 'DELETE',
  });
