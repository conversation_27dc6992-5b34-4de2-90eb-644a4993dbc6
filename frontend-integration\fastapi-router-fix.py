# FastAPI Router Configuration Fix
# Add this to your main FastAPI application to fix trailing slash issues

from fastapi import FastAPI, APIRouter, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from typing import List, Optional, Dict, Any
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# ===== ROUTER CONFIGURATION WITH SLASH HANDLING =====

# Teachers Router
teachers_router = APIRouter(
    prefix="/api/v1/teachers",
    tags=["teachers"],
    responses={404: {"description": "Not found"}},
)

# Students Router  
students_router = APIRouter(
    prefix="/api/v1/students",
    tags=["students"],
    responses={404: {"description": "Not found"}},
)

# Classes Router
classes_router = APIRouter(
    prefix="/api/v1/classes",
    tags=["classes"],
    responses={404: {"description": "Not found"}},
)

# ===== TEACHERS ENDPOINTS =====

@teachers_router.get("/")
@teachers_router.get("")  # Handle both with and without trailing slash
async def get_teachers():
    """Get all teachers - consistent slash handling"""
    try:
        logger.info("Fetching teachers list")
        # Mock data - replace with your database query
        teachers = [
            {
                "id": "teacher-1",
                "name": "John Smith",
                "email": "<EMAIL>",
                "subject": "Mathematics",
                "department": "Science",
                "status": "ACTIVE"
            },
            {
                "id": "teacher-2", 
                "name": "<PERSON> <PERSON>",
                "email": "<EMAIL>",
                "subject": "English",
                "department": "Languages",
                "status": "ACTIVE"
            }
        ]
        return teachers
    except Exception as e:
        logger.error(f"Error fetching teachers: {e}")
        return []

@teachers_router.get("/{teacher_id}")
async def get_teacher(teacher_id: str):
    """Get single teacher by ID"""
    try:
        # Mock response - replace with your database query
        return {
            "id": teacher_id,
            "name": "Mock Teacher",
            "email": "<EMAIL>",
            "subject": "Mathematics",
            "department": "Science",
            "status": "ACTIVE"
        }
    except Exception as e:
        logger.error(f"Error fetching teacher {teacher_id}: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")

@teachers_router.post("/")
@teachers_router.post("")  # Handle both with and without trailing slash
async def create_teacher(teacher_data: dict):
    """Create new teacher"""
    try:
        logger.info(f"Creating teacher: {teacher_data.get('name', 'Unknown')}")
        created_teacher = {
            "id": "mock-teacher-123",
            **teacher_data,
            "created_at": "2024-01-01T00:00:00Z",
            "updated_at": "2024-01-01T00:00:00Z"
        }
        return created_teacher
    except Exception as e:
        logger.error(f"Error creating teacher: {e}")
        raise HTTPException(status_code=500, detail="Failed to create teacher")

@teachers_router.get("/stats")
async def get_teacher_stats():
    """Get teacher statistics - no trailing slash for sub-routes"""
    try:
        logger.info("Fetching teacher statistics")
        stats = {
            "total": 25,
            "active": 23,
            "inactive": 2,
            "departments": 8,
            "averageExperience": 7
        }
        return stats
    except Exception as e:
        logger.error(f"Error fetching teacher stats: {e}")
        return {
            "total": 0,
            "active": 0,
            "inactive": 0,
            "departments": 0,
            "averageExperience": 0
        }

# ===== STUDENTS ENDPOINTS =====

@students_router.get("/")
@students_router.get("")  # Handle both with and without trailing slash
async def get_students():
    """Get all students - consistent slash handling"""
    try:
        logger.info("Fetching students list")
        # Mock data - replace with your database query
        students = [
            {
                "id": "student-1",
                "name": "Alice Johnson",
                "email": "<EMAIL>",
                "grade": "10",
                "section": "A",
                "status": "ACTIVE"
            },
            {
                "id": "student-2",
                "name": "Bob Smith", 
                "email": "<EMAIL>",
                "grade": "10",
                "section": "B",
                "status": "ACTIVE"
            }
        ]
        return students
    except Exception as e:
        logger.error(f"Error fetching students: {e}")
        return []

@students_router.get("/{student_id}")
async def get_student(student_id: str):
    """Get single student by ID"""
    try:
        # Mock response - replace with your database query
        return {
            "id": student_id,
            "name": "Mock Student",
            "email": "<EMAIL>",
            "grade": "10",
            "section": "A",
            "status": "ACTIVE"
        }
    except Exception as e:
        logger.error(f"Error fetching student {student_id}: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")

@students_router.post("/")
@students_router.post("")  # Handle both with and without trailing slash
async def create_student(student_data: dict):
    """Create new student"""
    try:
        logger.info(f"Creating student: {student_data.get('name', 'Unknown')}")
        created_student = {
            "id": "mock-student-123",
            **student_data,
            "created_at": "2024-01-01T00:00:00Z",
            "updated_at": "2024-01-01T00:00:00Z"
        }
        return created_student
    except Exception as e:
        logger.error(f"Error creating student: {e}")
        raise HTTPException(status_code=500, detail="Failed to create student")

# ===== CLASSES ENDPOINTS =====

@classes_router.get("/")
@classes_router.get("")  # Handle both with and without trailing slash
async def get_classes():
    """Get all classes - consistent slash handling"""
    try:
        logger.info("Fetching classes list")
        # Mock data - replace with your database query
        classes = [
            {
                "id": "class-1",
                "name": "Mathematics 10A",
                "grade": "10",
                "section": "A",
                "capacity": 30,
                "enrolled": 28,
                "teacher_name": "John Smith",
                "status": "ACTIVE"
            },
            {
                "id": "class-2",
                "name": "English 10B",
                "grade": "10", 
                "section": "B",
                "capacity": 30,
                "enrolled": 25,
                "teacher_name": "Sarah Johnson",
                "status": "ACTIVE"
            }
        ]
        return classes
    except Exception as e:
        logger.error(f"Error fetching classes: {e}")
        return []

@classes_router.get("/{class_id}")
async def get_class(class_id: str):
    """Get single class by ID"""
    try:
        # Mock response - replace with your database query
        return {
            "id": class_id,
            "name": "Mock Class 10A",
            "grade": "10",
            "section": "A",
            "capacity": 30,
            "enrolled": 25,
            "teacher_id": "teacher-123",
            "teacher_name": "John Doe",
            "status": "ACTIVE"
        }
    except Exception as e:
        logger.error(f"Error fetching class {class_id}: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")

@classes_router.post("/")
@classes_router.post("")  # Handle both with and without trailing slash
async def create_class(class_data: dict):
    """Create new class"""
    try:
        logger.info(f"Creating class: {class_data.get('name', 'Unknown')}")
        created_class = {
            "id": "mock-class-123",
            **class_data,
            "enrolled": 0,
            "created_at": "2024-01-01T00:00:00Z",
            "updated_at": "2024-01-01T00:00:00Z"
        }
        return created_class
    except Exception as e:
        logger.error(f"Error creating class: {e}")
        raise HTTPException(status_code=500, detail="Failed to create class")

@classes_router.get("/stats")
async def get_class_stats():
    """Get class statistics - no trailing slash for sub-routes"""
    try:
        logger.info("Fetching class statistics")
        stats = {
            "total": 12,
            "active": 10,
            "inactive": 2,
            "totalStudents": 285,
            "averageCapacity": 30
        }
        return stats
    except Exception as e:
        logger.error(f"Error fetching class stats: {e}")
        return {
            "total": 0,
            "active": 0,
            "inactive": 0,
            "totalStudents": 0,
            "averageCapacity": 0
        }

# ===== ROUTER REGISTRATION =====
# Add this to your main FastAPI app:

"""
# In your main.py file:

from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware

app = FastAPI(title="School Management API", version="1.0.0")

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000", "http://127.0.0.1:3000"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include routers
app.include_router(teachers_router)
app.include_router(students_router) 
app.include_router(classes_router)

# Health check
@app.get("/api/v1/health")
async def health_check():
    return {"status": "healthy", "message": "School Management API is running"}
"""
