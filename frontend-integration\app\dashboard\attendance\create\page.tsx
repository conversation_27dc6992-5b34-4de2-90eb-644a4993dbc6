'use client';

import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import {
  ArrowLeft,
  Calendar,
  Check,
  Clock,
  Plus,
  Users,
} from 'lucide-react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { useState } from 'react';

interface AttendanceFormData {
  date: string;
  class: string;
  subject: string;
  period: string;
  students: { id: string; name: string; status: 'present' | 'absent' | 'late' }[];
  notes: string;
}

const initialFormData: AttendanceFormData = {
  date: new Date().toISOString().split('T')[0],
  class: '',
  subject: '',
  period: '',
  students: [],
  notes: '',
};

const classes = ['Grade 9', 'Grade 10', 'Grade 11', 'Grade 12'];
const subjects = ['Mathematics', 'Science', 'English', 'History', 'Physics'];
const periods = ['1st Period', '2nd Period', '3rd Period', '4th Period', '5th Period'];

const mockStudents = [
  { id: 'STU001', name: 'John Doe' },
  { id: 'STU002', name: 'Jane Smith' },
  { id: 'STU003', name: 'Mike Johnson' },
  { id: 'STU004', name: 'Sarah Wilson' },
  { id: 'STU005', name: 'David Brown' },
];

export default function CreateAttendancePage() {
  const router = useRouter();
  const [formData, setFormData] = useState<AttendanceFormData>(initialFormData);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleInputChange = (field: keyof AttendanceFormData, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleClassChange = (className: string) => {
    setFormData(prev => ({
      ...prev,
      class: className,
      students: mockStudents.map(student => ({
        ...student,
        status: 'present' as const,
      })),
    }));
  };

  const handleStudentStatusChange = (studentId: string, status: 'present' | 'absent' | 'late') => {
    setFormData(prev => ({
      ...prev,
      students: prev.students.map(student =>
        student.id === studentId ? { ...student, status } : student
      ),
    }));
  };

  const markAllPresent = () => {
    setFormData(prev => ({
      ...prev,
      students: prev.students.map(student => ({ ...student, status: 'present' as const })),
    }));
  };

  const markAllAbsent = () => {
    setFormData(prev => ({
      ...prev,
      students: prev.students.map(student => ({ ...student, status: 'absent' as const })),
    }));
  };

  const handleSubmit = async () => {
    setIsSubmitting(true);
    
    try {
      // TODO: Replace with actual API call
      console.log('Submitting attendance data:', formData);
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // Success - redirect to attendance list
      router.push('/dashboard/attendance');
    } catch (error) {
      console.error('Error creating attendance record:', error);
      // TODO: Show error toast
    } finally {
      setIsSubmitting(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'present': return 'text-green-600 bg-green-50';
      case 'absent': return 'text-red-600 bg-red-50';
      case 'late': return 'text-yellow-600 bg-yellow-50';
      default: return 'text-gray-600 bg-gray-50';
    }
  };

  const presentCount = formData.students.filter(s => s.status === 'present').length;
  const absentCount = formData.students.filter(s => s.status === 'absent').length;
  const lateCount = formData.students.filter(s => s.status === 'late').length;

  return (
    <div className="container mx-auto p-4 sm:p-6 lg:p-8 max-w-4xl">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-4">
          <Link href="/dashboard/attendance">
            <Button variant="ghost" size="sm">
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to Attendance
            </Button>
          </Link>
          <div>
            <h1 className="text-2xl sm:text-3xl font-bold tracking-tight flex items-center">
              <Plus className="w-8 h-8 mr-3 text-blue-600" />
              Take Attendance
            </h1>
            <p className="text-muted-foreground mt-1">
              Record student attendance for today
            </p>
          </div>
        </div>
      </div>

      {/* Session Details Card */}
      <Card className="mb-6">
        <CardHeader>
          <CardTitle className="flex items-center">
            <Calendar className="w-5 h-5 mr-2 text-blue-600" />
            Session Details
          </CardTitle>
          <CardDescription>
            Configure the attendance session details
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div>
              <Label htmlFor="date">Date *</Label>
              <Input
                id="date"
                type="date"
                value={formData.date}
                onChange={(e) => handleInputChange('date', e.target.value)}
                required
              />
            </div>
            <div>
              <Label htmlFor="class">Class *</Label>
              <Select value={formData.class} onValueChange={handleClassChange}>
                <SelectTrigger>
                  <SelectValue placeholder="Select class" />
                </SelectTrigger>
                <SelectContent>
                  {classes.map((className) => (
                    <SelectItem key={className} value={className}>
                      {className}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div>
              <Label htmlFor="subject">Subject</Label>
              <Select value={formData.subject} onValueChange={(value) => handleInputChange('subject', value)}>
                <SelectTrigger>
                  <SelectValue placeholder="Select subject" />
                </SelectTrigger>
                <SelectContent>
                  {subjects.map((subject) => (
                    <SelectItem key={subject} value={subject}>
                      {subject}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div>
              <Label htmlFor="period">Period</Label>
              <Select value={formData.period} onValueChange={(value) => handleInputChange('period', value)}>
                <SelectTrigger>
                  <SelectValue placeholder="Select period" />
                </SelectTrigger>
                <SelectContent>
                  {periods.map((period) => (
                    <SelectItem key={period} value={period}>
                      {period}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Attendance Summary */}
      {formData.students.length > 0 && (
        <div className="grid grid-cols-3 gap-4 mb-6">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center space-x-2">
                <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Present</p>
                  <p className="text-2xl font-bold text-green-600">{presentCount}</p>
                </div>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center space-x-2">
                <div className="w-3 h-3 bg-red-500 rounded-full"></div>
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Absent</p>
                  <p className="text-2xl font-bold text-red-600">{absentCount}</p>
                </div>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center space-x-2">
                <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Late</p>
                  <p className="text-2xl font-bold text-yellow-600">{lateCount}</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Student Attendance Card */}
      {formData.students.length > 0 && (
        <Card className="mb-6">
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle className="flex items-center">
                  <Users className="w-5 h-5 mr-2 text-green-600" />
                  Student Attendance
                </CardTitle>
                <CardDescription>
                  Mark attendance for each student
                </CardDescription>
              </div>
              <div className="flex space-x-2">
                <Button variant="outline" size="sm" onClick={markAllPresent}>
                  Mark All Present
                </Button>
                <Button variant="outline" size="sm" onClick={markAllAbsent}>
                  Mark All Absent
                </Button>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {formData.students.map((student) => (
                <div key={student.id} className="flex items-center justify-between p-3 border rounded-lg">
                  <div className="flex items-center space-x-3">
                    <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                      <Users className="w-5 h-5 text-blue-600" />
                    </div>
                    <div>
                      <p className="font-medium">{student.name}</p>
                      <p className="text-sm text-muted-foreground">{student.id}</p>
                    </div>
                  </div>
                  <div className="flex space-x-2">
                    <Button
                      variant={student.status === 'present' ? 'default' : 'outline'}
                      size="sm"
                      onClick={() => handleStudentStatusChange(student.id, 'present')}
                      className={student.status === 'present' ? 'bg-green-600 hover:bg-green-700' : ''}
                    >
                      Present
                    </Button>
                    <Button
                      variant={student.status === 'late' ? 'default' : 'outline'}
                      size="sm"
                      onClick={() => handleStudentStatusChange(student.id, 'late')}
                      className={student.status === 'late' ? 'bg-yellow-600 hover:bg-yellow-700' : ''}
                    >
                      Late
                    </Button>
                    <Button
                      variant={student.status === 'absent' ? 'default' : 'outline'}
                      size="sm"
                      onClick={() => handleStudentStatusChange(student.id, 'absent')}
                      className={student.status === 'absent' ? 'bg-red-600 hover:bg-red-700' : ''}
                    >
                      Absent
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Notes Card */}
      <Card className="mb-6">
        <CardHeader>
          <CardTitle className="flex items-center">
            <Clock className="w-5 h-5 mr-2 text-purple-600" />
            Additional Notes
          </CardTitle>
          <CardDescription>
            Add any additional notes about this attendance session
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Textarea
            value={formData.notes}
            onChange={(e) => handleInputChange('notes', e.target.value)}
            placeholder="Any additional notes or observations..."
            rows={3}
          />
        </CardContent>
      </Card>

      {/* Submit Button */}
      <div className="flex justify-end">
        <Button
          onClick={handleSubmit}
          disabled={isSubmitting || !formData.class || formData.students.length === 0}
          className="bg-green-600 hover:bg-green-700"
        >
          {isSubmitting ? (
            <>
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
              Saving Attendance...
            </>
          ) : (
            <>
              <Check className="w-4 h-4 mr-2" />
              Save Attendance
            </>
          )}
        </Button>
      </div>
    </div>
  );
}
