/**
 * Student Store - Domain-Specific State Management
 *
 * Manages student-related state with best practices:
 * - Normalized data structure for performance
 * - Optimistic updates with rollback
 * - Comprehensive error handling
 * - Search and filtering capabilities
 * - Bulk operations support
 * - Real-time updates integration ready
 */

import { Student, StudentCreate, StudentUpdate } from '@/schemas/zodSchemas';
import { create } from 'zustand';
import { createJSONStorage, devtools, persist, subscribeWithSelector } from 'zustand/middleware';

// Create a more flexible type for store operations that explicitly allows undefined
type StudentPartial = {
  [K in keyof Student]?: Student[K] | undefined;
};

// Types for student management
export interface StudentFilters {
  search?: string;
  grade_level?: string;
  class_name?: string;
  status?: 'ACTIVE' | 'INACTIVE' | 'GRADUATED';
  enrollment_date_from?: string;
  enrollment_date_to?: string;
}

export interface StudentSort {
  field: keyof Student;
  direction: 'asc' | 'desc';
}

export interface StudentPagination {
  page: number;
  size: number;
  total: number;
}

export interface BulkOperation {
  type: 'update' | 'delete' | 'export';
  studentIds: string[];
  data?: Partial<StudentUpdate>;
}

export interface StudentState {
  // Data State
  students: Record<string, Student>; // Normalized by ID
  studentIds: string[]; // Ordered list of IDs
  selectedStudentIds: string[];

  // UI State
  isLoading: boolean;
  isCreating: boolean;
  isUpdating: boolean;
  isDeleting: boolean;
  isBulkOperating: boolean;
  error: string | null;

  // Filtering & Search
  filters: StudentFilters;
  sort: StudentSort;
  pagination: StudentPagination;
  searchResults: string[]; // Student IDs matching search

  // Cache Management
  lastFetch: number | null;
  cacheExpiry: number; // 5 minutes default

  // Actions - Data Management
  setStudents: (students: Student[]) => void;
  addStudent: (student: Student) => void;
  updateStudent: (id: string, updates: StudentPartial) => void;
  removeStudent: (id: string) => void;
  clearStudents: () => void;

  // Actions - CRUD Operations
  createStudent: (data: StudentCreate) => Promise<Student>;
  fetchStudents: (force?: boolean) => Promise<void>;
  fetchStudent: (id: string) => Promise<Student>;
  updateStudentData: (id: string, data: StudentUpdate) => Promise<Student>;
  deleteStudent: (id: string) => Promise<void>;

  // Actions - Bulk Operations
  bulkUpdate: (studentIds: string[], data: StudentPartial) => Promise<void>;
  bulkDelete: (studentIds: string[]) => Promise<void>;
  bulkExport: (studentIds: string[], format: 'csv' | 'excel' | 'pdf') => Promise<void>;

  // Actions - Selection
  selectStudent: (id: string) => void;
  deselectStudent: (id: string) => void;
  selectAllStudents: () => void;
  clearSelection: () => void;
  toggleStudentSelection: (id: string) => void;

  // Actions - Filtering & Search
  setFilters: (filters: Partial<StudentFilters>) => void;
  clearFilters: () => void;
  setSort: (sort: StudentSort) => void;
  setPagination: (pagination: Partial<StudentPagination>) => void;
  searchStudents: (query: string) => void;

  // Actions - Error Handling
  setError: (error: string | null) => void;
  clearError: () => void;

  // Actions - Cache Management
  invalidateCache: () => void;
  isCacheValid: () => boolean;

  // Getters
  getStudent: (id: string) => Student | undefined;
  getSelectedStudents: () => Student[];
  getFilteredStudents: () => Student[];
  getStudentsByClass: (className: string) => Student[];
  getStudentsByGrade: (gradeLevel: string) => Student[];
  getTotalCount: () => number;
  getSelectedCount: () => number;
}

// Default values
const defaultFilters: StudentFilters = {};
const defaultSort: StudentSort = { field: 'last_name', direction: 'asc' };
const defaultPagination: StudentPagination = { page: 1, size: 20, total: 0 };

export const useStudentStore = create<StudentState>()(
  subscribeWithSelector(
    devtools(
      persist(
        (set, get) => ({
          // Initial State
          students: {},
          studentIds: [],
          selectedStudentIds: [],

          // UI State
          isLoading: false,
          isCreating: false,
          isUpdating: false,
          isDeleting: false,
          isBulkOperating: false,
          error: null,

          // Filtering & Search
          filters: defaultFilters,
          sort: defaultSort,
          pagination: defaultPagination,
          searchResults: [],

          // Cache Management
          lastFetch: null,
          cacheExpiry: 5 * 60 * 1000, // 5 minutes

          // Data Management Actions
          setStudents: (students: Student[]) => {
            const studentsById = students.reduce((acc, student) => {
              acc[student.id] = student;
              return acc;
            }, {} as Record<string, Student>);

            set({
              students: studentsById,
              studentIds: students.map(s => s.id),
              lastFetch: Date.now(),
              error: null,
            });
          },

          addStudent: (student: Student) => {
            set(state => ({
              students: { ...state.students, [student.id]: student },
              studentIds: [...state.studentIds, student.id],
              pagination: {
                ...state.pagination,
                total: state.pagination.total + 1,
              },
            }));
          },

          updateStudent: (id: string, updates: StudentPartial) => {
            set((state): StudentState => {
              const existingStudent = state.students[id];
              if (!existingStudent) return state;

              // Filter out undefined values from updates
              const filteredUpdates = Object.fromEntries(
                Object.entries(updates).filter(([_, value]) => value !== undefined)
              );

              const updatedStudent: Student = {
                ...existingStudent,
                ...filteredUpdates,
              } as Student;

              return {
                ...state,
                students: {
                  ...state.students,
                  [id]: updatedStudent,
                },
              };
            });
          },

          removeStudent: (id: string) => {
            set(state => ({
              students: Object.fromEntries(
                Object.entries(state.students).filter(([key]) => key !== id)
              ),
              studentIds: state.studentIds.filter(studentId => studentId !== id),
              selectedStudentIds: state.selectedStudentIds.filter(studentId => studentId !== id),
              pagination: {
                ...state.pagination,
                total: Math.max(0, state.pagination.total - 1),
              },
            }));
          },

          clearStudents: () => {
            set({
              students: {},
              studentIds: [],
              selectedStudentIds: [],
              searchResults: [],
              pagination: { ...defaultPagination, total: 0 },
              lastFetch: null,
            });
          },

          // CRUD Operations
          createStudent: async (data: StudentCreate) => {
            set({ isCreating: true, error: null });

            try {
              // TODO: Replace with actual API call
              // const response = await apiClient.post('/students', data);
              // const newStudent = response.data;

              // Mock API call
              await new Promise(resolve => setTimeout(resolve, 1000));
              const newStudent: Student = {
                id: `student_${Date.now()}`,
                ...data,
                created_at: new Date().toISOString(),
                updated_at: new Date().toISOString(),
              };

              get().addStudent(newStudent);
              set({ isCreating: false });

              return newStudent;
            } catch (error) {
              const errorMessage =
                error instanceof Error ? error.message : 'Failed to create student';
              set({ error: errorMessage, isCreating: false });
              throw error;
            }
          },

          fetchStudents: async (force = false) => {
            // Check cache validity
            if (!force && get().isCacheValid()) {
              return;
            }

            set({ isLoading: true, error: null });

            try {
              // TODO: Replace with actual API call
              // const response = await apiClient.get('/students', { params: filters });
              // const students = response.data.data;

              // Mock API call
              await new Promise(resolve => setTimeout(resolve, 1000));
              const mockStudents: Student[] = [
                {
                  id: '1',
                  student_id: 'STU001',
                  first_name: 'John',
                  last_name: 'Doe',
                  email: '<EMAIL>',
                  phone: '+1234567890',
                  class_name: 'Class A',
                  grade_level: '10th Grade',
                  status: 'ACTIVE',
                  enrollment_date: '2024-01-15',
                  created_at: '2024-01-15T00:00:00Z',
                  updated_at: '2024-01-15T00:00:00Z',
                },
                // Add more mock students as needed
              ];

              get().setStudents(mockStudents);
              set({ isLoading: false });
            } catch (error) {
              const errorMessage =
                error instanceof Error ? error.message : 'Failed to fetch students';
              set({ error: errorMessage, isLoading: false });
              throw error;
            }
          },

          fetchStudent: async (id: string) => {
            // Check if student exists in cache
            const existingStudent = get().students[id];
            if (existingStudent) {
              return existingStudent;
            }

            set({ isLoading: true, error: null });

            try {
              // TODO: Replace with actual API call
              // const response = await apiClient.get(`/students/${id}`);
              // const student = response.data;

              // Mock API call
              await new Promise(resolve => setTimeout(resolve, 500));
              const mockStudent: Student = {
                id,
                student_id: `STU${id.padStart(3, '0')}`,
                first_name: 'Mock',
                last_name: 'Student',
                email: `student${id}@school.edu`,
                phone: '+1234567890',
                class_name: 'Class A',
                grade_level: '10th Grade',
                status: 'ACTIVE',
                enrollment_date: '2024-01-15',
                created_at: '2024-01-15T00:00:00Z',
                updated_at: '2024-01-15T00:00:00Z',
              };

              get().addStudent(mockStudent);
              set({ isLoading: false });

              return mockStudent;
            } catch (error) {
              const errorMessage =
                error instanceof Error ? error.message : 'Failed to fetch student';
              set({ error: errorMessage, isLoading: false });
              throw error;
            }
          },

          updateStudentData: async (id: string, data: StudentUpdate) => {
            const originalStudent = get().students[id];
            if (!originalStudent) {
              throw new Error('Student not found');
            }

            // Optimistic update
            get().updateStudent(id, data as StudentPartial);
            set({ isUpdating: true, error: null });

            try {
              // TODO: Replace with actual API call
              // const response = await apiClient.put(`/students/${id}`, data);
              // const updatedStudent = response.data;

              // Mock API call
              await new Promise(resolve => setTimeout(resolve, 1000));

              // Filter out undefined values from data
              const filteredData = Object.fromEntries(
                Object.entries(data).filter(([_, value]) => value !== undefined)
              );

              const updatedStudent: Student = {
                ...originalStudent,
                ...filteredData,
                updated_at: new Date().toISOString(),
              };

              get().updateStudent(id, updatedStudent);
              set({ isUpdating: false });

              return updatedStudent;
            } catch (error) {
              // Rollback optimistic update
              get().updateStudent(id, originalStudent);

              const errorMessage =
                error instanceof Error ? error.message : 'Failed to update student';
              set({ error: errorMessage, isUpdating: false });
              throw error;
            }
          },

          deleteStudent: async (id: string) => {
            const originalStudent = get().students[id];
            if (!originalStudent) {
              throw new Error('Student not found');
            }

            // Optimistic delete
            get().removeStudent(id);
            set({ isDeleting: true, error: null });

            try {
              // TODO: Replace with actual API call
              // await apiClient.delete(`/students/${id}`);

              // Mock API call
              await new Promise(resolve => setTimeout(resolve, 1000));

              set({ isDeleting: false });
            } catch (error) {
              // Rollback optimistic delete
              get().addStudent(originalStudent);

              const errorMessage =
                error instanceof Error ? error.message : 'Failed to delete student';
              set({ error: errorMessage, isDeleting: false });
              throw error;
            }
          },

          // Bulk Operations
          bulkUpdate: async (studentIds: string[], data: StudentPartial) => {
            const originalStudents = studentIds.map(id => get().students[id]).filter(Boolean);

            // Filter out undefined values from data
            const filteredData = Object.fromEntries(
              Object.entries(data).filter(([_, value]) => value !== undefined)
            );

            // Optimistic updates
            studentIds.forEach(id => {
              get().updateStudent(id, filteredData);
            });

            set({ isBulkOperating: true, error: null });

            try {
              // TODO: Replace with actual API call
              // await apiClient.patch('/students/bulk', { ids: studentIds, data });

              // Mock API call
              await new Promise(resolve => setTimeout(resolve, 2000));

              set({ isBulkOperating: false });
            } catch (error) {
              // Rollback optimistic updates
              originalStudents.forEach(student => {
                if (student) {
                  get().updateStudent(student.id, student);
                }
              });

              const errorMessage =
                error instanceof Error ? error.message : 'Failed to bulk update students';
              set({ error: errorMessage, isBulkOperating: false });
              throw error;
            }
          },

          bulkDelete: async (studentIds: string[]) => {
            const originalStudents = studentIds.map(id => get().students[id]).filter(Boolean);

            // Optimistic deletes
            studentIds.forEach(id => {
              get().removeStudent(id);
            });

            set({ isBulkOperating: true, error: null });

            try {
              // TODO: Replace with actual API call
              // await apiClient.delete('/students/bulk', { data: { ids: studentIds } });

              // Mock API call
              await new Promise(resolve => setTimeout(resolve, 2000));

              set({ isBulkOperating: false });
              get().clearSelection();
            } catch (error) {
              // Rollback optimistic deletes
              originalStudents.forEach(student => {
                if (student) {
                  get().addStudent(student);
                }
              });

              const errorMessage =
                error instanceof Error ? error.message : 'Failed to bulk delete students';
              set({ error: errorMessage, isBulkOperating: false });
              throw error;
            }
          },

          bulkExport: async (studentIds: string[], format: 'csv' | 'excel' | 'pdf') => {
            set({ isBulkOperating: true, error: null });

            try {
              // TODO: Replace with actual API call
              // const response = await apiClient.post('/students/export', { ids: studentIds, format });
              // const blob = new Blob([response.data], { type: response.headers['content-type'] });
              // const url = window.URL.createObjectURL(blob);
              // const a = document.createElement('a');
              // a.href = url;
              // a.download = `students.${format}`;
              // a.click();

              // Mock export
              await new Promise(resolve => setTimeout(resolve, 3000));
              console.log(`Exported ${studentIds.length} students as ${format}`);

              set({ isBulkOperating: false });
            } catch (error) {
              const errorMessage =
                error instanceof Error ? error.message : 'Failed to export students';
              set({ error: errorMessage, isBulkOperating: false });
              throw error;
            }
          },

          // Selection Actions
          selectStudent: (id: string) => {
            set(state => ({
              selectedStudentIds: [...new Set([...state.selectedStudentIds, id])],
            }));
          },

          deselectStudent: (id: string) => {
            set(state => ({
              selectedStudentIds: state.selectedStudentIds.filter(studentId => studentId !== id),
            }));
          },

          selectAllStudents: () => {
            const filteredStudents = get().getFilteredStudents();
            set({
              selectedStudentIds: filteredStudents.map(student => student.id),
            });
          },

          clearSelection: () => {
            set({ selectedStudentIds: [] });
          },

          toggleStudentSelection: (id: string) => {
            const isSelected = get().selectedStudentIds.includes(id);
            if (isSelected) {
              get().deselectStudent(id);
            } else {
              get().selectStudent(id);
            }
          },

          // Filtering & Search Actions
          setFilters: (filters: Partial<StudentFilters>) => {
            set(state => ({
              filters: { ...state.filters, ...filters },
              pagination: { ...state.pagination, page: 1 }, // Reset to first page
            }));
          },

          clearFilters: () => {
            set({
              filters: defaultFilters,
              searchResults: [],
              pagination: { ...defaultPagination, total: get().getTotalCount() },
            });
          },

          setSort: (sort: StudentSort) => {
            set({ sort });
          },

          setPagination: (pagination: Partial<StudentPagination>) => {
            set(state => ({
              pagination: { ...state.pagination, ...pagination },
            }));
          },

          searchStudents: (query: string) => {
            if (!query.trim()) {
              set({ searchResults: [] });
              return;
            }

            const searchTerm = query.toLowerCase();
            const matchingIds = get().studentIds.filter(id => {
              const student = get().students[id];
              if (!student) return false;

              return (
                student.first_name.toLowerCase().includes(searchTerm) ||
                student.last_name.toLowerCase().includes(searchTerm) ||
                student.email.toLowerCase().includes(searchTerm) ||
                student.student_id.toLowerCase().includes(searchTerm) ||
                student.class_name?.toLowerCase().includes(searchTerm) ||
                student.grade_level?.toLowerCase().includes(searchTerm)
              );
            });

            set({ searchResults: matchingIds });
          },

          // Error Handling
          setError: (error: string | null) => {
            set({ error });
          },

          clearError: () => {
            set({ error: null });
          },

          // Cache Management
          invalidateCache: () => {
            set({ lastFetch: null });
          },

          isCacheValid: () => {
            const { lastFetch, cacheExpiry } = get();
            if (!lastFetch) return false;
            return Date.now() - lastFetch < cacheExpiry;
          },

          // Getters
          getStudent: (id: string) => {
            return get().students[id];
          },

          getSelectedStudents: () => {
            const { students, selectedStudentIds } = get();
            return selectedStudentIds
              .map(id => students[id])
              .filter((student): student is Student => student != null);
          },

          getFilteredStudents: () => {
            const { students, studentIds, filters, searchResults, sort } = get();

            let filteredIds = searchResults.length > 0 ? searchResults : studentIds;

            // Apply filters
            if (Object.keys(filters).length > 0) {
              filteredIds = filteredIds.filter(id => {
                const student = students[id];
                if (!student) return false;

                if (filters.grade_level && student.grade_level !== filters.grade_level)
                  return false;
                if (filters.class_name && student.class_name !== filters.class_name) return false;
                if (filters.status && student.status !== filters.status) return false;

                if (filters.enrollment_date_from && student.enrollment_date) {
                  if (student.enrollment_date < filters.enrollment_date_from) return false;
                }

                if (filters.enrollment_date_to && student.enrollment_date) {
                  if (student.enrollment_date > filters.enrollment_date_to) return false;
                }

                return true;
              });
            }

            // Apply sorting
            const studentsToSort = filteredIds
              .map(id => students[id])
              .filter((student): student is Student => student != null);
            studentsToSort.sort((a, b) => {
              const aValue = a[sort.field];
              const bValue = b[sort.field];

              // Handle undefined values - put them at the end
              if (aValue == null && bValue == null) return 0;
              if (aValue == null) return sort.direction === 'asc' ? 1 : -1;
              if (bValue == null) return sort.direction === 'asc' ? -1 : 1;

              // At this point, both values are not null
              if (aValue! < bValue!) return sort.direction === 'asc' ? -1 : 1;
              if (aValue! > bValue!) return sort.direction === 'asc' ? 1 : -1;
              return 0;
            });

            return studentsToSort;
          },

          getStudentsByClass: (className: string) => {
            const { students, studentIds } = get();
            return studentIds
              .map(id => students[id])
              .filter(
                (student): student is Student => student != null && student.class_name === className
              );
          },

          getStudentsByGrade: (gradeLevel: string) => {
            const { students, studentIds } = get();
            return studentIds
              .map(id => students[id])
              .filter(
                (student): student is Student =>
                  student != null && student.grade_level === gradeLevel
              );
          },

          getTotalCount: () => {
            return get().studentIds.length;
          },

          getSelectedCount: () => {
            return get().selectedStudentIds.length;
          },
        }),
        {
          name: 'student-storage',
          storage: createJSONStorage(() => localStorage),
          partialize: state => ({
            // Only persist non-sensitive data
            filters: state.filters,
            sort: state.sort,
            pagination: state.pagination,
            // Don't persist actual student data for privacy
          }),
        }
      ),
      {
        name: 'student-store',
        enabled: process.env.NODE_ENV === 'development',
      }
    )
  )
);

// Selectors for performance optimization
export const useStudentSelectors = {
  // Basic selectors
  students: () => useStudentStore(state => state.students),
  studentIds: () => useStudentStore(state => state.studentIds),
  selectedStudentIds: () => useStudentStore(state => state.selectedStudentIds),

  // UI state selectors
  isLoading: () => useStudentStore(state => state.isLoading),
  isCreating: () => useStudentStore(state => state.isCreating),
  isUpdating: () => useStudentStore(state => state.isUpdating),
  isDeleting: () => useStudentStore(state => state.isDeleting),
  isBulkOperating: () => useStudentStore(state => state.isBulkOperating),
  error: () => useStudentStore(state => state.error),

  // Computed selectors
  filteredStudents: () => useStudentStore(state => state.getFilteredStudents()),
  selectedStudents: () => useStudentStore(state => state.getSelectedStudents()),
  totalCount: () => useStudentStore(state => state.getTotalCount()),
  selectedCount: () => useStudentStore(state => state.getSelectedCount()),

  // Specific student selector
  student: (id: string) => useStudentStore(state => state.getStudent(id)),
};

// Action selectors for better organization
export const useStudentActions = {
  // Data actions
  setStudents: () => useStudentStore(state => state.setStudents),
  addStudent: () => useStudentStore(state => state.addStudent),
  updateStudent: () => useStudentStore(state => state.updateStudent),
  removeStudent: () => useStudentStore(state => state.removeStudent),
  clearStudents: () => useStudentStore(state => state.clearStudents),

  // CRUD actions
  createStudent: () => useStudentStore(state => state.createStudent),
  fetchStudents: () => useStudentStore(state => state.fetchStudents),
  fetchStudent: () => useStudentStore(state => state.fetchStudent),
  updateStudentData: () => useStudentStore(state => state.updateStudentData),
  deleteStudent: () => useStudentStore(state => state.deleteStudent),

  // Bulk actions
  bulkUpdate: () => useStudentStore(state => state.bulkUpdate),
  bulkDelete: () => useStudentStore(state => state.bulkDelete),
  bulkExport: () => useStudentStore(state => state.bulkExport),

  // Selection actions
  selectStudent: () => useStudentStore(state => state.selectStudent),
  deselectStudent: () => useStudentStore(state => state.deselectStudent),
  selectAllStudents: () => useStudentStore(state => state.selectAllStudents),
  clearSelection: () => useStudentStore(state => state.clearSelection),
  toggleStudentSelection: () => useStudentStore(state => state.toggleStudentSelection),

  // Filter actions
  setFilters: () => useStudentStore(state => state.setFilters),
  clearFilters: () => useStudentStore(state => state.clearFilters),
  setSort: () => useStudentStore(state => state.setSort),
  setPagination: () => useStudentStore(state => state.setPagination),
  searchStudents: () => useStudentStore(state => state.searchStudents),

  // Error actions
  setError: () => useStudentStore(state => state.setError),
  clearError: () => useStudentStore(state => state.clearError),

  // Cache actions
  invalidateCache: () => useStudentStore(state => state.invalidateCache),
};
