/**
 * Authentication Hook
 *
 * Provides easy access to auth state and actions in components
 * Built on top of Zustand auth store
 */

import { useRouter } from 'next/navigation';
import { useEffect } from 'react';

import { useAuthStore } from '../stores/authStore';

export const useAuth = () => {
  const auth = useAuthStore();
  const router = useRouter();

  // Enhanced logout with navigation
  const logoutWithRedirect = (redirectTo?: string) => {
    auth.clear();
    router.push(redirectTo || '/login');
  };

  return {
    ...auth,
    logoutWithRedirect,
    // Computed values for backward compatibility
    isAuthenticated: !!auth.token,
    isLoading: auth.isLoading, // Use actual loading state from store
    isAdmin: auth.role === 'ADMIN' || auth.role === 'SUPER_ADMIN',
    isTeacher: auth.role === 'TEACHER',
    isStudent: auth.role === 'STUDENT',
    // Legacy methods
    initialize: () => {}, // No-op for backward compatibility
  };
};

// Specialized hooks for better performance
export const useAuthUser = () => useAuthStore(state => state.user);
export const useAuthLoading = () => false; // No loading state in current store
export const useAuthError = () => null; // No error state in current store

// Hook for protected routes
export const useRequireAuth = (redirectTo = '/login') => {
  const { isAuthenticated, isLoading } = useAuth();
  const router = useRouter();

  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      router.push(redirectTo);
    }
  }, [isAuthenticated, isLoading, router, redirectTo]);

  return { isAuthenticated, isLoading };
};

// Hook for guest-only routes (login, register)
export const useRequireGuest = (redirectTo = '/dashboard') => {
  const { isAuthenticated, isLoading } = useAuth();
  const router = useRouter();

  useEffect(() => {
    if (!isLoading && isAuthenticated) {
      router.push(redirectTo);
    }
  }, [isAuthenticated, isLoading, router, redirectTo]);

  return { isAuthenticated, isLoading };
};
